# -*-coding:utf-8-*-
from GenVidInfo import *
from Gen_Samples import *

import os
from Config.devices_selected import select_device, get_free_gpu_ids
device = select_device(get_free_gpu_ids(num_gpus=3, selected_device=[1,2,3]), batch_size=128)            # 用1张卡进行推理
os.environ["CUDA_VISABLE_DEVICES"] = f'{device}'                                # 选择空闲的卡
print(f"@Moss: we find and use device {device} for free cuda infer")

# ================================1. 先读配置项，读取视频, 保存data_info.pkl =====================================================================

data_cls = read_config(f"Config/baseline_standjump_fake.yml")  # 读取所有配置内容, 如无必要，无需修改主函数
if not Path(data_cls.get('save_dir')).exists():
    ori_save = data_cls['videos_pth']
    data_cls['save_dir'] = Path(ori_save).with_name(f"{Path(ori_save).name}_sampled").__str__()         # 采样数据存储位置


# 获取 符合逻辑的关键帧idx和骨骼点
data_dict = track_standjump_videos(vid_pth=data_cls.videos_pth, model_info=data_cls.model_info,
                                   mark_info=data_cls.mark_info)

# 存储信息
with open(f'{data_cls.videos_pth}/data_info.pkl', 'wb') as f:
    pickle.dump(data_dict, f)


# ============================== 2. 再读pkl，生成样本 ================================================================================

assert Path(f'{data_cls.videos_pth}/data_info.pkl').exists(), f"@Moss: No file in {data_cls.videos_pth}/data_info.pkl， Please <EMAIL>"
with open(f'{data_cls.videos_pth}/data_info.pkl', 'rb') as dt:
    vid_info = pickle.load(dt)

data_cls['vid_info'] = vid_info         # 写入大类
data_cls_pkl = data_cls


# 基于关键帧序列 采样生成 样本帧索引id
standjump = data_cls_pkl[data_cls_pkl.proj_name]
vid_info = gen_fake_samples(data_cls_pkl.vid_info, len_sample=standjump.len_sample, act_frames=standjump.act_frames)

# 生成单样本及单标签
Path(data_cls_pkl.save_dir).mkdir(exist_ok=True, parents=True)  # 不存在则创建 存储路径
invalid_vids = gen_lab_fake_vid(vid_info, data_cls_pkl)

if not len(invalid_vids) == 0:
    Path(data_cls_pkl.videos_pth + '_invalid_vids').mkdir(exist_ok=True)
    dst = Path(data_cls_pkl.videos_pth + '_invalid_vids')
    for src_pth in invalid_vids:
        dst_pth = str(Path(dst) / Path(src_pth).name)
        os.rename(src_pth, dst_pth)