"""
fixed by @Moss 20240604
# Step-1
读取mp4视频生成 data_info.pkl 骨骼点,有效状态帧等信息
"""

import json
import os
import pickle
import onnxruntime
import numpy as np
import torch

import yaml
import cv2
from pathlib import Path
from Config.ConfigDict import ConfigDict

from utils.general import LoadImages,scale_coords
from utils.logics import standjump_logic
from proj_skpt.getSkpt_solidBall import pose_v8_infer


def read_config(config_pth: str) -> ConfigDict:
    """
    # 读取采集数据需要的必要参数
    :param config_pth: Config/baseline.yml
    :return:
    """
    # 读取baseline
    with open(file=config_pth, mode='r', encoding='utf-8') as f:
        data = yaml.safe_load(f)
        data_cls = ConfigDict(data)  # 参数变量转成可调用的方法

    # 读取配置文件——标定区域
    with open(file=Path(data_cls.mark_file).absolute(), mode='r', encoding='utf-8') as jf:
        mark_info = json.load(jf)
    data_cls['mark_info'] = mark_info       # 写入data_cls


    return data_cls


def read_files(vid_pth: str) -> list:
    """

    :param vid_pth: data_cls.videos_pth 来自config/baseline.yml的 videos_pth
    :return: [one_vid_pth,...]
    """

    # exist
    assert Path(vid_pth).exists(), f"@Moss: {vid_pth} don't exist!"
    p = Path(vid_pth).absolute()  # os-agnostic absolute path
    if p.is_dir():
        files = [str(file) for file in list(p.rglob('*.mp4'))]
    elif p.is_file() and str(p).endswith('mp4'):
        files = [str(p)]  # files
    else:
        raise Exception(f'ERROR: No videos found in {str(p)}')

    return files


def saveformed(data_dict:dict, data_base:tuple, status:int, skpt:np, img_save:any):
    """
    # 存储状态信息 和可能有的骨骼信息
    :param img_save: 无人/不符合False，1时存储skpt绘制后的图像
    :param skpt: 骨骼关键点
    :param data_dict: 存储视频信息
    :param data_base: 基本信息
    :param status: 数据状态：-1无人，0框内不符合逻辑，1有效数据
    :return:
    """
    path, im_shape, frame_id, video_frames = data_base
    if path not in data_dict.keys():
        data_dict[path] = {'img_shape':im_shape, 'video_frames':video_frames,'frame_id': [frame_id]}
        data_dict[path]['status'] = [status]
        data_dict[path]['skpt'] = [skpt]
    else:
        data_dict[path]['frame_id'].append(frame_id)
        data_dict[path]['status'].append(status)
        data_dict[path]['skpt'].append(skpt)

    return


def param_videos(vid_pth, model_info=None, mark_info=None) ->dict:
    """
    :param model_info: 姿态模型信息 {pth:模型路径, imgsz:推理时使用的输入图像尺寸}
    :param mark_info: 标定信息 such {'vid_width': 1920, 'ReadyPt1_x': 左上, 'ReadyPt2_x': 右下}
    :param vid_pth: 视频数据路径 --来自配置文件Config/baseline.yml
    :return:
    """

    files = read_files(vid_pth)         # 数据列表


    dataset = LoadImages(files, img_size=model_info['infer_size'], vid_width=mark_info.get('vid_width'))
    # 循环 视频-> frames

    data_dict = {}          # 用于存储有效数据信息
    session= onnxruntime.InferenceSession(model_info['yolo_pose'], providers=['CUDAExecutionProvider'])
    # assert len(session.get_providers()) == 2, f"@Moss: {session.get_providers()} may use CPU Infer,please check your ENV"
    for path, img, im0s, vid_id, frame_id in dataset:
        print(f'video {dataset.count + 1}/{dataset.nf} ({dataset.frame}/{dataset.video_frames}) {Path(path).name}: ', end=' ')
        # model_inference
        preds = pose_v8_infer(session, img=img)

        data_base = (path, im0s.shape, frame_id, dataset.video_frames)
        for i, det in enumerate(preds):  # detections per image
            if len(det) ==0:        # 'No person in Img'
                saveformed(data_dict, data_base, status=-1, skpt=None, img_save=False)      # -1: 无人状态, 无骨骼数据
                continue

            # Rescale boxes from img_size to im0 size
            det[:, :4] = scale_coords(img.shape[2:], det[:, :4], im0s.shape, kpt_label=False)
            det[:, 6:] = scale_coords(img.shape[2:], det[:, 6:], im0s.shape, kpt_label=True, step=3)

            _index = standjump_logic(mark_info, det=det)
            if _index is False:
                saveformed(data_dict, data_base, status=0, skpt=None, img_save=False)      # 0: 有人但不符合逻辑, 无骨骼数据
                continue
            else:       # 存储有效数据
                saveformed(data_dict, data_base, status=1, skpt=det[_index], img_save=im0s)  # 1: 有人且符合逻辑, 存储骨骼数据 和 img信息
                break

    return data_dict




if __name__ == '__main__':
    #  0

    data_cls = read_config(f"Config/baseline_standjump.yml")  # 读取所有配置内容, 如无必要，无需修改主函数

    # 获取 符合逻辑的关键帧idx和骨骼点
    data_dict = param_videos(vid_pth=data_cls.videos_pth, model_info=data_cls.model_info,
                mark_info=data_cls.mark_info)

    # 存储信息
    with open(f'{data_cls.videos_pth}/data_info.pkl', 'wb') as f:
        pickle.dump(data_dict, f)