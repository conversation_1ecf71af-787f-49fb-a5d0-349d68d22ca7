# -*-coding:utf-8-*-
"""
fixed by @Moss 20240604
# Step-2
读取生成的 data_info.pkl 有效帧等信息，生成出需要的样本及视频标签；
"""
import copy
import os
import pickle
import cv2
import numpy as np
from pathlib import Path

import yaml
import json
import logging
from tqdm import tqdm

from utils.general import LoadImages
from Config.ConfigDict import ConfigDict
from utils.logics import find_seqParts,read_markInfo, read_markInfo_standjump
from utils.plots import colors, plot_one_box,plot_skeleton_kpts

from Config.statistics.Mod_timer import mod_timer
from Config.statistics.Mod_logger import init_logger
logger = init_logger(log_dir='../logs', log_level=logging.INFO)      # logging.INFO, WARNING...


def read_vidConfig(config_pth: str):
    with open(file=config_pth, mode='r', encoding='utf-8') as f:
        data = yaml.safe_load(f)
        data_cls = ConfigDict(data)  # 参数变量转成可调用的方法

    # 读取配置文件——标定区域
    with open(file=Path(data_cls.mark_file).absolute(), mode='r', encoding='utf-8') as jf:
        mark_info = json.load(jf)
    data_cls['mark_info'] = mark_info       # 写入data_cls

    assert Path(f'{data_cls.videos_pth}/data_info.pkl').exists(), f"@Moss: No file in {data_cls.videos_pth}/data_info.pkl， Please <EMAIL>"
    with open(f'{data_cls.videos_pth}/data_info.pkl', 'rb') as dt:
        vid_info = pickle.load(dt)

    data_cls['vid_info'] = vid_info         # 写入大类

    return data_cls


def gen_samples(vid_info, total_frames, interval_frames, sample_rate) ->dict:
    """
     根据存储的关键点，汇总并生成需要的样本
    :param interval_frames:
    :param total_frames:
    :param sample_rate:
    :param vid_info:
    :return:
    """

    for path, data_info in vid_info.items():
        vid_info[path]['video_name'] = Path(path).name
        vid_info[path]['total_frames'] = total_frames           # 采集的总帧数
        vid_info[path]['interval_frames'] = interval_frames           # 最小采集间隔

        status_list = data_info['status']
        seq_parts = find_seqParts(status_list, total_frames, interval_frames)       # 划分有效帧片段
        vid_info[path]['seq_parts'] = seq_parts            # 添加字段: 待采样的备选序列
        # 间隔抽样
        idx_samples = [a_part[:: sample_rate] for a_part in seq_parts]
        vid_info[path]['idx_samples'] = idx_samples      # 添加字段: 采样后的样本序列下标

        # 生成 抽样后的关键点字段 (25, 57)
        skpt_samples, skpts = [np.empty((len(x), 57)) for x in idx_samples], vid_info[path]['skpt']         # 这里创建的是 空的数据位 len(x)容错每个样本不定长
        for id, idx_aSample in enumerate(idx_samples):
            for i, idx in enumerate(idx_aSample):
                skpt_samples[id][i] = skpts[idx]
        vid_info[path]['skpt_samples'] = skpt_samples

        # 同时保存一份未抽样的原始点序列 (75, 57)
        ori_samples, skpts = [np.empty((len(x), 57)) for x in seq_parts], vid_info[path]['skpt']         # 这里创建的是 空的数据位 len(x)容错每个样本不定长
        for id, idx_aSample in enumerate(seq_parts):
            for i, idx in enumerate(idx_aSample):
                ori_samples[id][i] = skpts[idx]
        vid_info[path]['ori_samples'] = ori_samples


    return vid_info


def plot_vidImgs(im0s, det_one,frame_id, pt_xyxy, mirror=None):
    """
    绘制单张图像作为视频标签的一部分
    :param mirror: 该视频是否需要镜像
    :param im0s: 单视频原始图像
    :param det_one: 单视频唯一检测姿态点
    :param frame_id: 当前图像对应的帧
    :param pt_xyxy: 标定xyxy
    :return:
    """

    pt_xl, pt_yl, pt_xr, pt_yr = pt_xyxy        # 解包 标定
    # 在原始图像上画人体和标定
    plot_one_box(det_one[frame_id - 1][:4], im0s, label=f'person {det_one[frame_id - 1][4]:.2f}',
                 color=colors(int(det_one[frame_id - 1][4]), True), line_thickness=2, kpt_label=False)
    plot_skeleton_kpts(im0s, kpts=det_one[frame_id - 1][6:], steps=3, orig_shape=im0s.shape[:2], kpt_num=17)  # 绘制姿态点
    cv2.rectangle(im0s, (pt_xl, pt_yl), (pt_xr, pt_yr), color=(248, 248, 255), thickness=2 * 1 // 3,
                  lineType=cv2.LINE_AA)  # 绘制 配置box

    # 在图像左上角1/x画一个黑色背景的点
    baseShape = (int(im0s.shape[0] / 2), int(im0s.shape[1] / 2))  # 1080/2, 1902/2
    black_img = np.zeros((1080, 1920, 3), dtype=np.uint8)
    plot_skeleton_kpts(black_img, kpts=det_one[frame_id - 1][6:], steps=3, orig_shape=black_img.shape[:2],
                       kpt_num=17)  # 绘制姿态点
    cv2.rectangle(black_img, (pt_xl, pt_yl), (pt_xr, pt_yr), color=(248, 248, 255), thickness=2 * 1 // 3,
                  lineType=cv2.LINE_AA)  # 绘制 配置box

    im0s = cv2.resize(im0s, (baseShape[1], baseShape[0]))
    if mirror is None:
        black_img[0:baseShape[0], 0:baseShape[1]] = im0s  # 贴图左上角
    else:
        black_img[0:baseShape[0], baseShape[1]:] = im0s  # 贴图右上角
        black_img = np.fliplr(black_img)

    return black_img



def gen_lab_vid(vid_info, data_cls):
    """
    生成样本和视频标签
    :param data_cls:  用于获取 样本保存文件夹data_cls.save_dir
    :param vid_info:
    :return: 返回无效视频路径
    """
    save_pth = data_cls.save_dir

    invalid_vids = []
    for path, sample_info in vid_info.items():
        # 提取基本信息
        vid_stem = Path(sample_info['video_name']).stem
        img_shape = sample_info['img_shape']
        total_frames, interval = sample_info['total_frames'], sample_info['interval_frames']
        seq_parts, idx_samples = sample_info['seq_parts'], sample_info['idx_samples']
        mark_info = data_cls.mark_info

        if len(seq_parts) == 0:
            logger.info(f"@Moss: No valid seq_parts in {path}")     # 针对手工录制的视频,跳过无效视频
            invalid_vids.append(path)
            continue

        logger.info(f"{str(Path(path).name)}: ")        # 当前 有效视频 loading:
        imgs_seq = deal_videoLabs(sample_info, video_pth=path, model_info=data_cls.model_info, mark_info=mark_info)        # 存储单个视频的姿态点到有效样本的图像上

        save_dir = save_pth + os.sep + Path(path).stem
        Path(save_dir).mkdir(exist_ok=True)
        for id, a_skpts in enumerate(sample_info['skpt_samples']):
            # 生成pkl
            sample_name = vid_stem + f'_total{total_frames}_loop{idx_samples[id][0]}_{idx_samples[id][-1]}.pkl'

            # TODO 加入预处理
            det_skpts = a_skpts[:, 6:]
            if mark_info.get('mirror') is not None:         # 'True' 镜像处理
                det_skpts[:, ::3] = mark_info.get('vid_width') - det_skpts[:, ::3]
            one_skpts_x, one_skpts_y = a_skpts[0, 6:][[15, 18]].mean(), a_skpts[0, 6:][[16, 19]].mean()        # 获取 首帧51个关键点的肩点中心
            det_skpts[:, ::3] -= one_skpts_x            # 以肩点中心为圆心的切换
            det_skpts[:, 1::3] -= one_skpts_y

            a_sample = {'img_shape':img_shape, 'vid_pth':path, 'total_frames':total_frames, 'interval':interval,
                        'sample_name': sample_name, 'pred_skpts':det_skpts, 'ori_all': sample_info['ori_samples'][id],
                        'seq_vid': seq_parts[id], 'idx_sample':idx_samples[id], 'ori_skpts': a_skpts}
            # 存储 姿态点序列 样本信息
            with open(save_dir+os.sep+sample_name, 'wb') as fs:
                pickle.dump(a_sample, fs)           # saved pkl

            # 生成vids & 存储
            lab_videoName = Path(sample_name).with_suffix('.avi').__str__()
            vid_writer = cv2.VideoWriter(save_dir+os.sep+lab_videoName, cv2.VideoWriter_fourcc(*'XVID'), 25, (1920, 1080))  # 创建保存视频的对象
            with mod_timer():
                for idf in a_sample['idx_sample']:
                    vid_writer.write(imgs_seq[idf])  # saving avi

            del sample_name, lab_videoName, vid_writer, a_sample,

    return invalid_vids



def deal_videoLabs(sample_info:dict, video_pth:str, model_info:dict, mark_info:dict) -> list:
    """
    生成pkl数据样本 和 视频标签
    :param sample_info: 样本信息
    :param model_info: 推理模型信息
    :param video_pth: 单个视频的路径
    :param mark_info: 标定信息
    :return:
    """
    dataset = LoadImages([video_pth], img_size=model_info['infer_size'], vid_width=mark_info.get('vid_width'))      # 单个视频
    det_one = sample_info['skpt']
    pt_xyxy = read_markInfo_standjump(mark_info)  # 左上点，右下点 标定

    imgs_seq = []
    for path, img, im0s, vid_id, frame_id in tqdm(dataset):
        if det_one[frame_id-1] is None:
            imgs_seq.append(None)
            continue
        black_img = plot_vidImgs(im0s, det_one, frame_id, pt_xyxy, mirror=mark_info.get('mirror'))          # 生成vid标签
        imgs_seq.append(black_img)       # 存储

    return imgs_seq






if __name__ == '__main__':
    #
    # 读取baseline
    data_cls = read_vidConfig(f"Config/baseline_standjump.yml")

    # 基于关键帧序列 采样生成 样本帧索引id
    standjump = data_cls.stand_jump
    vid_info = gen_samples(data_cls.vid_info, total_frames=standjump.total_frames,
                           interval_frames=standjump.interval_frames, sample_rate=standjump.sample_rate)

    # 生成单样本及单标签
    Path(data_cls.save_dir).mkdir(exist_ok=True, parents=True)  # 不存在则创建 存储路径
    invalid_vids = gen_lab_vid(vid_info, data_cls)

    if not len(invalid_vids) ==0:
        Path(data_cls.videos_pth + '_invalid_vids').mkdir(exist_ok=True)
        dst = Path(data_cls.videos_pth + '_invalid_vids')
        for src_pth in invalid_vids:
            dst_pth = str(Path(dst) / Path(src_pth).name)
            os.rename(src_pth, dst_pth)




