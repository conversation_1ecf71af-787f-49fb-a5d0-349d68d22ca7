# -*-coding:utf-8-*-
import cv2
import re
import pickle
import numpy as np
from tqdm import tqdm
from pathlib import Path


def read_file_pkl(pkl_name:str) -> dict:
    """读取pkl信息"""
    if not Path(pkl_name).exists():
        return {}

    with open(pkl_name, 'rb') as fi:
        data = pickle.load(fi)

    return data


def pose_getResult(anno_in, base_info):
    """
    将读取到的样本 -> 需要的数据字典
        num_person = 1      # 默认单人
    """
    if not len(anno_in):
        return {}
    anno = dict()

    frame_skpts = []         # 记录有效帧

    keypoint_lst = anno_in.get('pred_skpts', [])
    start_kpt_lst = anno_in.get('Start_run', {})

    # 未跟踪到 但在起点附近检测到的目标
    start_kpts, start_scores = [], []
    if len(start_kpt_lst):
        for _frame, a_skpt in start_kpt_lst.items():
            if a_skpt is None:
                continue
            a_skpt = a_skpt[6:].reshape(17,3)
            start_kpts.append(a_skpt[:,:2].unsqueeze(0))
            start_scores.append(a_skpt[:,2].unsqueeze(0))
            frame_skpts.append(_frame)

    if not len(frame_skpts):
        name = anno_in.get('sample_name', '')
        end_frame = int(re.search(r'_([\d]+)\.pkl', name).group(1))
        track_frame = end_frame - len(keypoint_lst) + 1
    else:
        track_frame = frame_skpts[0]

    # 跟踪到的目标
    skpts, scores, use_fid = [], [], []
    for k, skpt_ts in enumerate(keypoint_lst):
        if skpt_ts is None or len(skpt_ts) < 2:
            continue
        this_k = k + track_frame
        skpts.append(skpt_ts[1][..., :2])
        scores.append(skpt_ts[1][..., 2])
        use_fid.append(this_k)

    # 合并骨骼数据
    skpts = start_kpts + skpts
    scores = start_scores + scores
    # 合并获取的帧id
    frames_skpt = frame_skpts + use_fid

    anno['keypoint'] = np.stack(skpts, axis=1)           # (N, T, 17, 2)
    anno['keypoint_score'] = np.stack(scores, axis=1)           # (N, T, 17)
    anno['frames_skpt'] = frames_skpt           # [T]

    # 起跑线中心
    anno['start_pt'] = anno_in.get('start_pt', (None, None))
    # 其他信息汇总
    anno['img_shape'] = anno_in.get('img_shape')
    anno['original_shape'] = anno_in.get('oriVid_shape')
    anno['total_frames'] = len(skpts)
    anno['vid_frames'] = anno_in.get('total_frames')        # 视频部分的原始帧数(包含跟踪丢失和出画面的帧)

    anno.update(base_info)

    del anno_in
    return anno



def write_results(annos):
    results = []
    for anno_info in tqdm(annos):
        pkl_name = anno_info.get('filename', 'None')            # 很重要，测试时可以根据该元信息 获取到样本路径
        anno_dict = read_file_pkl(pkl_name)
        anno = pose_getResult(anno_dict, anno_info)

        results.append(anno)

    return results



def get_samles(sample_dir, out_put=False):
    annos = list({'filename': str(pth)} for pth in Path(sample_dir).rglob('*.pkl'))

    results = write_results(annos)

    for res in results:
        if out_put:
            save_pth = Path(res['filename']).with_suffix('.xml')
            # 输出第1个样本给C++联调
            cv_file = cv2.FileStorage(save_pth, cv2.FILE_STORAGE_WRITE)
            cv_file.write('filename', res['filename'])
            frames = res['keypoint'].shape[-3]
            cv_file.write('keypoint_score', res['keypoint_score'].reshape(frames, 17))
            cv_file.write('keypoint', res['keypoint'].reshape(frames, 17*2))
            cv_file.write('start_pt', res['start_pt'])
            cv_file.write('img_shape', res['img_shape'])
            cv_file.write('total_frames', res['total_frames'])

            cv_file.release()

    return results


def preprocess_data(test_pipeline, data):


    # return pred_res
    return






if __name__ == '__main__':
    sample_dir = "/root/share175/sport_datas/run_50M/classify_cheating/test_with_CPP"

    left_kp = [1, 3, 5, 7, 9, 11, 13, 15]
    right_kp = [2, 4, 6, 8, 10, 12, 14, 16]
    test_pipeline = [
        dict(type='UniformSampleFrames', clip_len=48, num_clips=1, test_mode=True),
        dict(type='PoseDecode'),
        dict(type='LocationCompact', use_start_pt=True),  # 新增 针对50m起点的原点切换
        dict(type='PoseCompact', hw_ratio=1., allow_imgpad=True),
        dict(type='Resize', scale=(-1, 64)),  # default=64
        dict(type='CenterCrop', crop_size=64),  # default=64
        dict(type='GeneratePoseTarget', sigma=0.6, use_score=True, with_kp=True, with_limb=False, double=True,
             left_kp=left_kp, right_kp=right_kp),
        dict(type='FormatShape', input_format='NCTHW_Heatmap'),
        dict(type='PackActionInputs')
    ]



    results = get_samles(sample_dir, out_put=True)

    # pred_res = preprocess_data(test_pipeline, data=results)



