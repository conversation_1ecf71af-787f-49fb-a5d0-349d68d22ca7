# -*-coding:utf-8-*-
"""
Info: created by @Moss 2024/08/01 16:38
Target: 采用onnx模型直接推理视频文件夹，批量获取作弊数据
"""
import copy
import math
import os
import sys

import torch
import torch.nn.functional as F
from typing import Tuple

# 跨项目导入 TCN模型、args配置
sys.path.append(os.path.abspath('..'))
from GCN_yoloPose.model.ALSTM_FCN import FCN_model
from GCN_yoloPose.config.args_situpAndreach_cheat  import get_parser
from GCN_yoloPose.feeders.feeder_TCN_cheat_situpAndreach import Feeder


from Config.devices_selected import select_device, get_free_gpu_ids
from GenVidInfo import *
from Gen_Samples import *
from utils.multiCard_processes import multiprocess_main_up


# ===============================0. 加载作弊分类模型、预处理 ==========================================================
def write_pred_info(flg_txt:str, preds_dict:dict) -> None:
    with open(flg_txt, 'a+') as flg:
        for path, preds_lst in preds_dict.items():
            print(path, preds_lst)
            vid_name = Path(path).name
            flg.write(f'{vid_name}:\n{preds_lst}\n\n')

    return



def main(arg, videos_pth=None):
    # ================================1. 先读配置项，读取视频, 保存data_info.pkl =====================================================================
    data_cls = read_config(f"Config/baseline_sitUp_fake.yml")           # 读取所有配置内容, 如无必要，无需修改主函数
    if videos_pth is not None:
        data_cls.videos_pth = videos_pth

    # 加载-TCN分类模型
    arg.weights = data_cls.pretrained_model['TCN_classify']  # 模型路径
    model = onnxruntime.InferenceSession(arg.weights, providers=['CUDAExecutionProvider'],
                                                      provider_options=[{'device_id': arg.card_id}])        # model = load_model(arg)

    # 设置onnx姿态推理模型-会话
    data_cls.session = onnxruntime.InferenceSession(data_cls.model_info['yolo_pose'],
                                                    providers=['CUDAExecutionProvider'],
                                                    provider_options=[{'device_id': arg.card_id}])

    # 获取 符合逻辑的关键帧idx和骨骼点
    data_dict = track_situp_videos(data_cls)

    data_cls['vid_info'] = data_dict  # 写入大类

    # 基于关键帧序列 采样生成 样本帧索引id
    proj_info = data_cls[data_cls.proj_name]
    vid_info = gen_sitUp_suspects_samples(data_cls.vid_info, len_sample=proj_info.len_sample, plot_waves=False)

    # 数据预处理和模型推理: 这里和正常的采样脚本走向了不同的分支
    preds_dict = dict()             # 作弊类
    preds_normal_dict, preds_single_dict = dict(), dict()       # 非作弊类
    for path, pkl_dict in vid_info.items():
        arg.logger.info(path)
        try:
            pred_labels = list()
            for pred_skpts in pkl_dict['skpt_samples']:
                a_data, len_frame = Feeder.data_union(pred_skpts)       # 数据预处理
                out = None      # use onnx infer
                for i in range(0, len(a_data)):
                    pred_np = model.run([model.get_outputs()[0].name], {model.get_inputs()[0].name: a_data[i][None]})[0]
                    pred = torch.tensor(pred_np)  # self.model is session
                    out = pred if out is None else torch.cat((out, pred), dim=0)

                all_prebs = F.softmax(out, dim=1)
                top2_preb, id_2th = torch.topk(all_prebs, 2, dim=1)
                pred_label = id_2th[0][0].item()

                pred_labels.append(pred_label)
            # 阈值条件
            if all(x==0 for x in pred_labels):
                preds_normal_dict[path] = f"{Path(path).name} Normal action..len({len(pred_labels)})"
            elif all(x==4 for x in pred_labels):        # 全体single
                preds_single_dict[path] = f"{Path(path).name} Single action..len({len(pred_labels)})"
            else:
                preds_dict[path] = pred_labels
        except Exception as e:
            continue
    # 写入
    write_pred_info(arg.flg_txt, preds_dict)
    write_pred_info(arg.flg_txt_normal, preds_normal_dict)
    write_pred_info(arg.flg_txt_single, preds_single_dict)
    del preds_dict, preds_normal_dict, preds_single_dict

    return


def process_subdirs(*args):
    arg, videos_pth = args  # 解包

    print(f'Start-SitUp:card-{arg.card_id}')
    main(arg, videos_pth=videos_pth)

    return








if __name__ == '__main__':
    # os.environ["CUDA_VISABLE_DEVICES"] = f'6,7'  # 选择空闲的卡
    arg = get_parser().parse_args()

    arg.process_num_CPU = 12        # 使用的进程数：参考双卡8进程，3卡12进程,
    num_gpus = 4
    arg.card_ids = get_free_gpu_ids(num_gpus=num_gpus, selected_device=[0,1,2,3])  # 用1张卡进行推理

    # TODO：多进程-夜跑模式[指 夜里用服务器不断跑的代码块]：
    arg.dirs = f'/root/persons/ai_group/Ronson/AA_TYdata/AutoCollectstandJump_originalDatas/collectData_sitUp_zuobi_sampled/2025_3_17_15_15_28_192.168.2.126_1_3_B__V40'


    arg.logger = init_logger(name='det_vid', log_dir=arg.dirs, log_level=logging.INFO)  # logging.INFO, WARNING...
    arg.flg_txt = arg.dirs + '/rec_pred_labels.txt'         # 仅包含 作弊类别
    arg.flg_txt_normal = Path(arg.flg_txt).with_name('rec_pred_labels_normal.txt').__str__()
    arg.flg_txt_single = Path(arg.flg_txt).with_name('rec_pred_labels_single.txt').__str__()

    multiprocess_main_up(arg, process_subdirs)

    # multiprocess_main(arg)


    '''
    拷贝操作备份：
            arg.logger.info(f"pred:{pred_label}, {all_prebs.tolist()[0]}")
        dst_dir = Path(path).parent / f'pred_{pred_label}_{data_cls.pred_labels[pred_label]}'
        dst_dir.mkdir(exist_ok=True)
        print('*****',dst_dir)
        if not pred_label == 0:  # 仅对作弊类别生效
            Path(path).rename(dst_dir / Path(path).name)  # 剪切到作弊文件夹
        elif top2_preb[0][1].item() >= 0.45:
            Path(path).rename(dst_dir / Path(path).name)  # 剪切到正常dir, 但概率值不高需要排查
    '''









