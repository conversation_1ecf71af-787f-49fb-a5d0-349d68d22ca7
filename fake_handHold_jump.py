"""
created by @Moss 20240416
# Step-1
读取mp4视频生成 data_info.pkl 骨骼点,有效状态帧等信息
"""
import copy
import json
import math
import os
import pickle
import re
import time
from typing import Union

import matplotlib.pyplot as plt
import torch
import onnxruntime
import numpy as np
import onnxruntime as ort
import yaml
import cv2
from pathlib import Path
from Config.ConfigDict import ConfigDict

from utils.logics import standjump_target_logic
from utils.general import LoadImages, scale_coords
from proj_skpt.getSkpt_onnx import pose_v8_infer, pose_v8_detFoot, detect_board_v8_infer
from trackers.byte_tracker import BYTETracker
from proj_skpt.engine.results import Results

from utils.plots import colors, plot_one_box, plot_skeleton_kpts
from PIL import Image
from torchvision.transforms import Compose, Normalize, Resize, ToTensor, CenterCrop

from GenVidInfo import read_files
from Gen_Samples import *

import os
from Config.devices_selected import select_device, get_free_gpu_ids
from GenVidInfo import read_config,saveformed,calculate_iou


def preprocess_func(resize=320, centerCrop=384):

    preprocess = Compose([
        lambda img: resize_with_max_size(img, resize, centerCrop),
        CenterCrop(centerCrop),
        ToTensor(),
        Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
    ])

    return preprocess


def track_standjump_videos(vid_pth, model_info=None, mark_info=None) -> dict:
    """
    created by @Avery
    use byteTrack 追踪算法
    :param model_info: 姿态模型信息 {pth:模型路径, imgsz:推理时使用的输入图像尺寸}
    :param mark_info: 标定信息 such {'vid_width': 1920, 'ReadyPt1_x': 左上, 'ReadyPt2_x': 右下}
    :param vid_pth: 视频数据路径 --来自配置文件Config/baseline.yml
    :return:
    """
    # todo 循环 视频-> frames
    files = read_files(vid_pth)  # 数据列表
    dataset = LoadImages(files, img_size=model_info['infer_size'], vid_width=mark_info.get('vid_width'))
    data_dict, use_id, vid_idx = {}, None, 0  # 用于存储有效数据信息, use_id 为跟踪目标id, vid_idx为第1个视频
    session = onnxruntime.InferenceSession(model_info['yolo_pose'], providers=['CUDAExecutionProvider'])
    # todo 加载onnx模型  记得每次换最新模型
    session_1 = ort.InferenceSession(model_info['classify_hand_hold'])
    fram_count = 0
    track_one_ly = 600  # 估计的初始值
    flag = 0
    vid_id_last = 1
    resize, centerCrop = model_info['hand_hold_size'][0], model_info['hand_hold_size'][1]
    preprocess = preprocess_func(resize, centerCrop)
    for path, img, im0s, vid_id, frame_id,_ in dataset:
        print(f'video {vid_id + 1}/{dataset.nf} ({dataset.frame}/{dataset.video_frames}) {Path(path).name}: ', end=' ')
        vid_id_current = vid_id + 1
        fram_count += 1
        if frame_id == 1:
            tracker = BYTETracker(frame_rate=25)  # 初始化追踪器
            rec_t = True  # 在每开始循环1个新视频，定位标记要清零

        #  todo  model_inference 姿态推理一帧图片

        preds = pose_v8_infer(session, img=img)

        data_base = (path, im0s.shape, frame_id, dataset.video_frames)
        for i, det in enumerate(preds):  # detections per image
            im0 = im0s.copy()
            if len(det) == 0:  # 'No person in Img'
                saveformed(data_dict, data_base, status=-1, skpt=None, img_save=False)  # -1: 无人状态, 无骨骼数据
                continue
            # 目标跟踪
            results = []
            det[:, :4] = scale_coords(img.shape[2:], det[:, :4], im0.shape, kpt_label=False).round()
            det[:, 6:] = scale_coords(img.shape[2:], det[:, 6:], im0.shape, kpt_label=True, step=3).round()
            pred_kpts = det[:, 6:].view(len(det), *[17, 3])  # ref_skpt = det[:, 6:].clone()
            img_path = None
            results.append(Results(im0s, path=img_path, names={0: 'person'}, boxes=det[:, :6], keypoints=pred_kpts))
            det_track = results[i].boxes.cpu().numpy()

            # print(det_track.xyxy.astype(int))
            tracks = tracker.update(det_track, im0s)  # 更新跟踪器
            # print(tracks.astype(int))

            if len(tracks) == 0:
                saveformed(data_dict, data_base, status=-2, skpt=None, img_save=False)  # -2: 跟踪丢失
                continue

            idx = tracks[:, -1].astype(int)
            results[i] = results[i][idx]
            results[i].update(boxes=torch.as_tensor(tracks[:, :-1]))  # results[i].boxes.boxes

            # todo 确定跟踪 主体目标
            init_idx = standjump_target_logic(mark_info, det_track=results[i].boxes.xyxy)  # 确定标定框内的跟踪目标所在的row
            track_id = results[i].boxes.id[init_idx]
            if init_idx is not False:  # 框内有人，但不一定是目标
                if rec_t:
                    use_id = track_id  # 存储该跟踪id 仅第1次储存的为目标,
                rec_t = False
            if (use_id is not None) and (use_id in results[i].boxes.id):  # if 跟踪目标已确定，且存在画面中
                # 判断跟踪目标是否在框内，此时只能传1个跟踪目标，防止框内有其他人
                use_idx = int(torch.where(use_id == results[i].boxes.id)[0])
                target_in_box = standjump_target_logic(mark_info, det_track=results[i].boxes.xyxy[use_idx].unsqueeze(
                    0))  # results[i].boxes.xyxy
            else:
                # 跟踪目标已确定，但跟踪丢失
                saveformed(data_dict, data_base, status=-2, skpt=None, img_save=im0s)
                continue

            # print(row_idx)
            track_one = results[i].boxes.data[use_idx][:-1]  # todo 目标人体框
            # todo 计算该目标框与标定框的重叠面积IOU，可以先计算一个初始面积，后续再计算这个IOU面积小于一个阈值就采集保存
            track_one_xyxy =track_one.tolist()[:4]

            pt_xl, pt_yl, pt_xr, pt_yr = read_markInfo_standjump(mark_info)  # 左上点，右下点
            pt_xyxy = [pt_xl, pt_yl, pt_xr, pt_yr]
            iou = calculate_iou(track_one_xyxy,pt_xyxy)
            # todo 取跟踪目标的鼻子关键点y坐标
            track_one_kpt_nose_y = results[i].keypoints.data[use_idx][0][1]
            # todo 计算每个视频开始的第20帧鼻子点高度作为起始帧
            if fram_count == 20:
                track_one_ly = results[i].keypoints.data[use_idx][0][1]
            # todo 判断是不是下一个视频了
            if vid_id_last != vid_id_current:
                # todo 某些变量恢复初值
                vid_id_last += 1
                fram_count = 1
                flag = 0
                track_one_ly = 600
            # todo 计算第20帧与当前帧判断为弯腰开始启动跳了
            if track_one_kpt_nose_y-track_one_ly > 100:
                # TODO 同时满足还在起跳中
                flag = 1
            if iou > 0 and flag == 1:
                # todo 生成保存路径
                path_split = path.split('/')
                last_slash_index = path.rfind('/')
                output_folder = path[:last_slash_index]
                filename = f'{path_split[-1]}_{frame_id}.jpg'
                filename_crop = f'{path_split[-1]}_{frame_id}_crop.jpg'
                # todo
                save_path = os.path.join(output_folder, filename)
                save_crop_path = os.path.join(output_folder, filename_crop)
                # todo 保存图片
                # cropped_img.save('cope_image_1.jpg')
                if im0s.dtype != np.uint8:
                    im0s = im0s.astype(np.uint8)
                if im0s is None or im0s.size == 0:
                    print('图像为空或尺寸为0，无法写入')
                else:
                    if fram_count % 3 == 0:
                        # TODO 借助垫子标定裁剪保存
                        biao_ding = mark_info
                        # 返回 【上终点x,y, 下终点x,y, 上平均y, 下平均y, 上起点xy, 下起点xy】
                        biao_ding_start_xia_x = biao_ding['ReadyPt2_x']
                        biao_ding_end_xia_x = biao_ding['EndPt2_x']
                        biao_ding_start_xia_y = biao_ding['ReadyPt2_y']
                        biao_ding_end_xia_y = biao_ding['EndPt2_y']
                        # todo 从左往右跳
                        if biao_ding_start_xia_x < biao_ding_end_xia_x:
                            crop_end_x = int(biao_ding_start_xia_x + 2 * (biao_ding_end_xia_x - biao_ding_start_xia_x) / 3)
                            crop_start_x = int(biao_ding_start_xia_x) - 250  # 向后扩展250
                            crop_end_y = int(biao_ding_start_xia_y)
                            crop_start_y = 80
                            # todo 裁剪图像
                            crop_image = im0s[crop_start_y:crop_end_y, crop_start_x:crop_end_x]
                            #cv2.imwrite(save_crop_path, crop_image)
                        # todo 从右往左跳
                        else:
                            crop_end_x = int(biao_ding_start_xia_x) + 250  # 向后扩展250
                            crop_start_x = int(
                                biao_ding_start_xia_x - 2 * (biao_ding_start_xia_x - biao_ding_end_xia_x) / 3)
                            crop_end_y = int(biao_ding_start_xia_y)
                            crop_start_y = 80
                            # todo 裁剪图像
                            crop_image = im0s[crop_start_y:crop_end_y, crop_start_x:crop_end_x]
                            #cv2.imwrite(save_crop_path, crop_image)


                        # todo 颜色转换 Opencv的BGR转PIL的RGB
                        resized_image = cv2.cvtColor(crop_image, cv2.COLOR_BGR2RGB)
                        resized_image_1 = Image.fromarray(resized_image)
                        # todo 裁剪后，图片推理

                        image = preprocess(resized_image_1).unsqueeze(0)
                        # todo 获取输入名称
                        input_name = session_1.get_inputs()[0].name
                        # 输入数据需numpy
                        input_data = {input_name: image.numpy()}
                        outputs = session_1.run(None, input_data)
                        # todo 输出概率分布并选最高的
                        pred_class = torch.softmax(torch.tensor(outputs[0][0]), 0)
                        predict_index = np.argmax(outputs[0])
                        if predict_index == 1:
                            # todo 保存为原图尺寸（1920×1080）
                            cv2.imwrite(save_path, im0s)
                            # todo 裁剪图像保存
                            cv2.imwrite(save_crop_path, crop_image)
                            print(f'该帧推理误判，预测类别为{predict_index}')
                        else:
                            print(f'该帧推理正确，预测类别为{predict_index}')



def read_markInfo_standjump(mark_info: dict):
    """
    获取单人立定跳远 标定信息，并返回 左上,右下的坐标组合
    :param mark_info: 认为统一朝左为基础方向， if mark_info.get('mirror')， 则朝向改变
    :return: 左上点，右下点
    """
    Pt1_x, Pt1_y = mark_info['ReadyPt1_x'], mark_info['ReadyPt1_y']
    Pt2_x, Pt2_y = mark_info['ReadyPt2_x'], mark_info['ReadyPt2_y']

    Pt1_x += 20 if mark_info.get('mirror') else -20  # 偏移20pix
    Pt2_x -= 200 if mark_info.get('mirror') else -200  # 偏移200pix
    Pt1_y -= int(1080 / 30)
    pt_xl, pt_yl = min(Pt1_x, Pt2_x), min(Pt1_y, Pt2_y)  # top_left
    pt_xr, pt_yr = max(Pt1_x, Pt2_x), max(Pt1_y, Pt2_y)  # bottom_right

    return pt_xl, pt_yl, pt_xr, pt_yr



def resize_with_max_size(image, size, max_size):
    # todo PIL的RGB转Opencv的BGR
    image_cv = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
    original_width, original_height = image.size
    # todo 先判断确定最终的width和height
    # todo 原始图片width>height
    if original_width > original_height:
        # TODO 长边超过max_size
        if max_size < size * (original_width / original_height):
            width = max_size
            height = int(max_size * (original_height / original_width))
        # TODO 长边不超过max_size
        else:
            height = size
            width = int(size * (original_width / original_height))
    # todo 原始图片height>width
    else:
        # TODO 长边超过max_size
        if max_size < size * (original_height / original_width):
            height = max_size
            width = int(max_size * (original_width / original_height))
        # TODO 长边不超过max_size
        else:
            width = size
            height = int(size * (original_height / original_width))
    new_size = (width, height)
    # TODO 用opencv的cv2.resize()函数调整图像大小
    resized_image = cv2.resize(image_cv, new_size, interpolation=cv2.INTER_LINEAR)
    # todo 颜色转换 Opencv的BGR转PIL的RGB
    resized_image = cv2.cvtColor(resized_image, cv2.COLOR_BGR2RGB)
    return Image.fromarray(resized_image)  # TODO 转回PIL图像对象



if __name__ == '__main__':
    device = select_device(get_free_gpu_ids(num_gpus=1, selected_device=[0, 1, 2, 3]), batch_size=128)  # 用1张卡进行推理
    os.environ["CUDA_VISABLE_DEVICES"] = f'{device}'  # 选择空闲的卡
    print(f"@Moss: we find and use device {device} for free cuda infer")

    # ================================1. 先读配置项，读取视频, 保存data_info.pkl =====================================================================
    # todo data_cls以字典形式保存baseline_standjump.yml中的内容
    data_cls = read_config(f"Config/baseline_handHoldStandjump_fake.yml")  # 读取所有配置内容, 如无必要，无需修改主函数
    if not Path(data_cls.get('save_dir')).exists():
        ori_save = data_cls['videos_pth']
        data_cls['save_dir'] = Path(ori_save).with_name(f"{Path(ori_save).name}_sampled").__str__()  # 采样数据存储位置

    # 获取 符合逻辑的关键帧idx和骨骼点
    data_dict = track_standjump_videos(vid_pth=data_cls.videos_pth,
                                       model_info=data_cls.model_info,
                                       mark_info=data_cls.mark_info)



