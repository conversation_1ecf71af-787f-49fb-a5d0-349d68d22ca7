# -*-coding:utf-8-*-
"""
created by @Moss 20250617 11:11
体前屈 yskj首个 双模态融合 数据集 采集脚本

"""
import copy

from GenVidInfo import *
from Gen_Samples import *

import os
from Config.devices_selected import select_device, get_free_gpu_ids

import faulthandler
faulthandler.enable()           # python -X faulthandler your_script.py


def main(data_cls):
    # ================================1. 先读配置项，读取视频, 保存data_info.pkl =====================================================================
    device = select_device(get_free_gpu_ids(num_gpus=1, selected_device=data_cls.devices), batch_size=128)            # 用1张卡进行推理
    os.environ["CUDA_VISABLE_DEVICES"] = f'{device}'                                # 选择空闲的卡
    print(f"@Moss: we find and use device {device} for free cuda infer")

    if not Path(data_cls.get('save_dir')).exists():
        ori_save = data_cls['videos_pth']
        data_cls['save_dir'] = Path(ori_save).with_name(f"{Path(ori_save).name}_sampled").__str__()         # 采样数据存储位置


    # 获取 符合逻辑的关键帧idx和骨骼点
    data_dict = track_situp_videos_fusion(data_cls)

    data_cls['vid_info'] = data_dict         # 写入大类
    data_cls_pkl = data_cls


    # 基于关键帧序列 采样生成 样本帧索引id
    proj_info = data_cls_pkl[data_cls_pkl.proj_name]
    vid_info = gen_sitUp_fusion_samples(data_cls_pkl.vid_info, len_sample=proj_info.len_sample, plot_waves=False)     # 默认不绘制波形图

    # 生成单样本及 单标签
    Path(data_cls_pkl.save_dir).mkdir(exist_ok=True, parents=True)  # 不存在则创建 存储路径
    invalid_vids = gen_sitUp_lab_fusion_vid(vid_info, data_cls_pkl)

    if not len(invalid_vids) == 0:
        Path(data_cls_pkl.videos_pth + '_invalid_vids').mkdir(exist_ok=True)
        dst = Path(data_cls_pkl.videos_pth + '_invalid_vids')
        for src_pth in invalid_vids:
            dst_pth = str(Path(dst) / Path(src_pth).name)
            os.rename(src_pth, dst_pth)

    return


if __name__ == '__main__':
    data_cls = read_config(f"Config/baseline_sitUp_fusion.yml")         # 读取所有配置内容, 如无必要，无需修改主函数

    data_cls_bp = copy.deepcopy(data_cls)

    for vid_dir in list(Path(data_cls_bp.videos_pth).rglob('*.mp4')):

        data_cls = data_cls_bp
        data_cls.videos_pth = str(vid_dir)

        print("data:",data_cls.videos_pth)
        try:
            main(data_cls)
        except Exception as e:
            dst_e = Path(data_cls.videos_pth).parent / '_except_vids'
            dst_e.mkdir(exist_ok=True)
            Path(data_cls.videos_pth).rename(dst_e / Path(data_cls.videos_pth).name)
            print(e)


        print(f"@Moss: {data_cls.videos_pth}")
        del data_cls

    # main(data_cls)