# -*-coding:utf-8-*-
"""

"""

from GenVidInfo import *
from Gen_Samples import *

import os
from Config.devices_selected import select_device, get_free_gpu_ids
device = select_device(get_free_gpu_ids(num_gpus=1, selected_device=[5]), batch_size=128)            # 用1张卡进行推理
# device = select_device(get_free_gpu_ids(num_gpus=1, selected_device=[4,5,6,7]), batch_size=128)            # 用1张卡进行推理
os.environ["CUDA_VISABLE_DEVICES"] = f'{device}'                                # 选择空闲的卡
print(f"@Moss: we find and use device {device} for free cuda infer")

# ================================1. 先读配置项，读取视频, 保存data_info.pkl =====================================================================

data_cls = read_config(f"Config/baseline_pullup_Ronson_test.yml")  # 读取所有配置内容, 如无必要，无需修改主函数
if not Path(data_cls.get('save_dir')).exists():
    ori_save = data_cls['videos_pth']
    data_cls['save_dir'] = Path(ori_save).with_name(f"{Path(ori_save).name}") / 'videos_clip_out'         # 采样数据存储位置
    Path(data_cls['save_dir']).mkdir(exist_ok=True)


# # 获取 符合逻辑的关键帧idx和骨骼点
data_dict = track_pullup_videos(data_cls)


# ----------- 功能整合：裁剪关键帧文件夹；对裁剪的视频抽取关键帧生成对应文件夹 ------------------------------

# 获取 符合逻辑的关键帧idx和骨骼点
data_dict_frames = track_pullup_videos_toframes(data_cls)
