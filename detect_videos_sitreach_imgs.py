# -*-coding:utf-8-*-
"""
Info: created by @Moss 2024/11/21 14:36
Target: 采用onnx模型直接推理视频文件夹中的图片，批量获取作弊数据
"""
# 跨项目导入 TCN模型、args配置
import os
import sys

import torch.nn.functional as F
from typing import Tuple

sys.path.append(os.path.abspath('..'))
from GCN_yoloPose.model.ALSTM_FCN import FCN_model
from GCN_yoloPose.config.args_situpAndreach_cheat  import get_parser
from GCN_yoloPose.feeders.feeder_TCN_cheat_situpAndreach import Feeder


from Config.devices_selected import select_device, get_free_gpu_ids
from GenVidInfo import *
from Gen_Samples import *
from utils.multiCard_processes import multiprocess_main_reach
from torchvision import transforms


# ===============================0. 加载作弊分类模型、预处理 ==========================================================



def main(arg, videos_pth=None):
    # ================================1. 先读配置项，读取视频, 保存data_info.pkl =====================================================================
    data_cls = arg.data_cls         # data_cls = read_config(f"Config/baseline_sitReach_fake.yml")  # 读取所有配置内容, 如无必要，无需修改主函数
    if videos_pth is not None:
        data_cls.videos_pth = videos_pth

    # 加载-图像分类模型, 设置onnx姿态推理模型-会话
    arg.weights = arg.data_cls.pretrained_model  # 模型路径
    data_cls.classify_model = onnxruntime.InferenceSession(arg.weights, providers=['CUDAExecutionProvider'],
                                        provider_options=[{'device_id': arg.card_id}])        # model = load_model(arg)

    # 设置onnx姿态推理模型-会话
    data_cls.session = onnxruntime.InferenceSession(arg.data_cls.model_info['yolo_pose'], providers=['CUDAExecutionProvider'],
                                            provider_options=[{'device_id': arg.card_id}])
    # 设置onnx板检测模型-会话
    data_cls.session_board = onnxruntime.InferenceSession(arg.data_cls.board_model['yolo_detect'], providers=['CUDAExecutionProvider'],
                                                 provider_options=[{'device_id': arg.card_id}])  # 板检测  出成绩需要用到

    # 获取 符合逻辑的关键帧idx和骨骼点
    data_dict = track_sitreach_videos_RunImgs(data_cls, multiprocess=True)
    # data_cls['vid_info'] = data_dict                            # 写入大类

    # 基于关键帧序列 采样生成 样本帧索引id
    # sitreach = data_cls[data_cls.proj_name]
    # vid_info = gen_sitReach_suspects_samples(data_cls.vid_info, len_min=sitreach.len_min)

    # 数据预处理和模型推理
    # preds_dict = dict()          # 预测结果的字典
    # for path, pkl_dict in vid_info.items():
    #     arg.logger.info(path)
    #     pkl_dict['pred_skpts'] = pkl_dict.pop('skpt_samples')       # 更新key name
    #     try:
    #         a_data, len_frame = Feeder.data_union(pkl_dict)
    #         out = None
    #         for i in range(0, len(a_data)):  # use onnx infer
    #             pred_np = model.run([model.get_outputs()[0].name], {model.get_inputs()[0].name: a_data[i][None]})[0]
    #             pred = torch.tensor(pred_np)  # self.model is session
    #             out = pred if out is None else torch.cat((out, pred), dim=0)
    #
    #         all_prebs = F.softmax(out, dim=1)
    #         top2_preb, id_2th = torch.topk(all_prebs, 2, dim=1)
    #         pred_label = int(id_2th[0][0].item())
    #
    #         # 记录类别结果
    #         arg.logger.info(f"pred:{pred_label}, {all_prebs.tolist()[0]}")
    #         if preds_dict.get(pred_label, False):
    #             preds_dict[pred_label].append(path)           # 输出类别作为key,视频路径作为val
    #         else:
    #             preds_dict[pred_label] = [path]
    #     except Exception as e:
    #         print(e)
    #         continue

    # 写入
    # preds_dict.pop(3, None)         # 阈值条件：删除3 single类别不需要写入
    # write_pred_info(arg.dirs, data_dict)


def process_func(*args):
    arg, videos_pth = args  # 解包

    print(f'Start-SitReach:card-{arg.card_id}')
    main(arg, videos_pth=videos_pth)

    return





if __name__ == '__main__':
    arg = get_parser().parse_args()

    arg.process_num_CPU = 2        # 使用的进程数：参考双卡8进程，3卡12进程,
    num_gpus = 2
    arg.card_ids = get_free_gpu_ids(num_gpus=num_gpus, selected_device=[2,3])   #用1张卡进行推理

    # TODO:多卡多进程-夜跑模式[指 用服务器不断跑的代码]：
    arg.dirs = r'/root/share175/sport_datas/sit_and_reach/classify_cheating/ori_videos/SitReach_oriVideos_1/Hunan_changshataiyuxiaoxue'
    arg.classify_model = f"/root/share175/sport_trains/sit_and_reach/classifiy_cheat_efficientnetV2/runs_crop/weights_center3_multi/best_model_1121.onnx"


    arg.logger = init_logger(name='det_vid.imgs', log_dir=arg.dirs, log_level=logging.INFO)  # logging.INFO, WARNING...
    # arg.flg_txt = arg.dirs + '/rec_pred_labels.txt'  # 仅包含 作弊类别
    arg.data_cls = read_config(f"Config/baseline_sitReach_fake.yml")  # 读取所有配置内容, 如无必要，无需修改主函数
    arg.data_cls.save_dir = arg.dirs
    # 模型及预处理
    arg.data_cls.pretrained_model = arg.classify_model
    arg.data_cls.data_transform = transforms.Compose([
                                    transforms.Resize((384, 384)),
                                    transforms.ToTensor(),                    # onnx推理使用的是np
                                    transforms.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
    ])

    multiprocess_main_reach(arg.dirs, process_func, arg)

    # multiprocess_main(arg)

    # sub_dirs = [str(sub) for sub in Path(dirs).glob('*') if sub.is_dir()]
    # for subdir in sub_dirs:
    #     try:
    #         main(arg, videos_pth=subdir.__str__())
    #     except Exception as e:
    #         arg.logger.info(f'{e}; \n@Moss{str(subdir)}')
    #         print(e)




