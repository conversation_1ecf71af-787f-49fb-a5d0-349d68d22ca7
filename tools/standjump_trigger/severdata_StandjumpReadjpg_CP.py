# -*-coding:utf-8-*-
"""
# 将/root/share175/sever_datas/
 学校 /StandAndJump_freeTest/ 日期 / Ready_*.jpg
剪切 到
 dst 路径中 按 学校 / 日期 / Ready_*.jpg
"""
import shutil
from pathlib import Path

import tqdm

sever_pth = f'/root/share175/sever_datas/合肥45中芙蓉南'
dst_path = f'/root/share175/sport_datas/stand_jump/classify_trigger/ori_imgs'

all_scls = list(Path(sever_pth).iterdir())


def loop_scl(a_scl):
    scl_ready = []
    print(a_scl)
    for a_proj in tqdm.tqdm(list(Path(a_scl).iterdir())):
        if 'StandAndJump' in str(a_proj):
            scl_ready += list(a_proj.rglob('ready_*.jpg'))

    print(f'len({len(scl_ready)})')
    if len(scl_ready):
        a_scl_dst = Path(dst_path) / a_scl.name
        a_scl_dst.mkdir(exist_ok=True)
        for a_sample in tqdm.tqdm(scl_ready):
            Y_M_D = a_sample.parent.name.split('_')
            data = Y_M_D[0] + '_' + Y_M_D[1]
            dst_file = a_scl_dst / data / a_sample.name
            if dst_file.is_file():
                a_sample.unlink()
                print(dst_file)
            else:
                (a_scl_dst / data).mkdir(exist_ok=True)
                shutil.move(a_sample, dst_file)          # +直接剪切走



if __name__ == '__main__':

    loop_scl(Path(sever_pth))

    # for a_scl in tqdm.tqdm(all_scls):
    #     loop_scl(a_scl)
