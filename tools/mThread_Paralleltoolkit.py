# -*-coding:utf-8-*-
"""
多线程处理 并行工具包: 适合
    文件操作: 移动、复制、重命名大量文件
    数据处理: 批量处理图片、视频、文档
    网络请求: 并行API调用
    计算任务: CPU密集型并行计算
Usage Example @Moss:
@parallel_process(fast_parallel(32))
def move_file(file_info):
    file, dst = file_info
    shutil.move(file, dst / file.name)

file_pairs = [(file, dst) for file in list(Path(path1).rglob('*.mp4'))]
results = move_file(file_pairs)

"""
import functools
import threading
import time
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor, as_completed
from typing import Callable, Any, List, Optional, Union
import os
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class ParallelConfig:
    """并行处理配置类"""

    def __init__(self,
                 max_workers: Optional[int] = None,
                 executor_type: str = 'thread',
                 chunk_size: int = 1,
                 show_progress: bool = True,
                 timeout: Optional[float] = None):
        self.max_workers = max_workers or min(32, os.cpu_count() * 2)
        self.executor_type = executor_type  # 'thread' or 'process'
        self.chunk_size = chunk_size
        self.show_progress = show_progress
        self.timeout = timeout


def parallel_process(config: Optional[ParallelConfig] = None):
    """
    并行处理装饰器

    使用示例:
    @parallel_process()
    def process_file(file_path):
        # 处理单个文件的逻辑
        return result

    # 调用时传入可迭代对象
    results = process_file(file_list)
    """
    if config is None:
        config = ParallelConfig()

    def decorator(func: Callable):
        @functools.wraps(func)
        def wrapper(iterable, *args, **kwargs):
            return _execute_parallel(func, iterable, config, *args, **kwargs)

        return wrapper

    return decorator


def _execute_parallel(func: Callable, iterable, config: ParallelConfig, *args, **kwargs):
    """执行并行处理的核心逻辑"""
    items = list(iterable)
    total_items = len(items)

    if total_items == 0:
        return []

    if config.show_progress:
        logger.info(f"开始并行处理 {total_items} 个项目...")

    # 选择执行器
    executor_class = ThreadPoolExecutor if config.executor_type == 'thread' else ProcessPoolExecutor

    results = []
    start_time = time.time()

    with executor_class(max_workers=config.max_workers) as executor:
        # 创建任务
        if args or kwargs:
            # 如果有额外参数，创建包装函数
            def task_wrapper(item):
                return func(item, *args, **kwargs)

            future_to_item = {executor.submit(task_wrapper, item): item for item in items}
        else:
            future_to_item = {executor.submit(func, item): item for item in items}

        # 收集结果
        completed_count = 0
        for future in as_completed(future_to_item, timeout=config.timeout):
            try:
                result = future.result()
                results.append(result)
                completed_count += 1

                if config.show_progress and completed_count % max(1, total_items // 10) == 0:
                    progress = (completed_count / total_items) * 100
                    logger.info(f"进度: {progress:.1f}% ({completed_count}/{total_items})")

            except Exception as e:
                item = future_to_item[future]
                logger.error(f"处理项目 {item} 时发生错误: {e}")
                results.append(None)  # 或者根据需要处理错误

    elapsed_time = time.time() - start_time
    if config.show_progress:
        logger.info(f"并行处理完成，耗时: {elapsed_time:.2f}秒")

    return results


class ParallelProcessor:
    """并行处理器类，用于更复杂的场景"""

    def __init__(self, config: Optional[ParallelConfig] = None):
        self.config = config or ParallelConfig()
        self._lock = threading.Lock()
        self._results = []
        self._errors = []

    def process(self, func: Callable, iterable, *args, **kwargs):
        """处理可迭代对象"""
        return _execute_parallel(func, iterable, self.config, *args, **kwargs)

    def batch_process(self, func: Callable, iterable, batch_size: int = 100):
        """批量处理，适合处理大量数据"""
        items = list(iterable)
        batches = [items[i:i + batch_size] for i in range(0, len(items), batch_size)]

        def process_batch(batch):
            return [func(item) for item in batch]

        batch_results = self.process(process_batch, batches)

        # 展平结果
        results = []
        for batch_result in batch_results:
            if batch_result:
                results.extend(batch_result)

        return results

    def process_with_callback(self, func: Callable, iterable,
                              success_callback: Optional[Callable] = None,
                              error_callback: Optional[Callable] = None):
        """带回调的处理"""

        def wrapper(item):
            try:
                result = func(item)
                if success_callback:
                    success_callback(item, result)
                return result
            except Exception as e:
                if error_callback:
                    error_callback(item, e)
                raise

        return self.process(wrapper, iterable)


# 便捷的预定义配置
def fast_parallel(max_workers: int = 32):
    """快速并行处理配置"""
    return ParallelConfig(max_workers=max_workers, show_progress=False)


def safe_parallel(max_workers: int = 8):
    """安全并行处理配置（较少线程）"""
    return ParallelConfig(max_workers=max_workers, show_progress=True)


def process_parallel(max_workers: int = None):
    """进程并行处理配置"""
    return ParallelConfig(
        max_workers=max_workers or os.cpu_count(),
        executor_type='process',
        show_progress=True
    )


# 使用示例
if __name__ == "__main__":
    from pathlib import Path
    import shutil


    # 示例1: 使用装饰器
    @parallel_process(fast_parallel())
    def move_file(file_info):
        file_path, dst_dir = file_info
        try:
            shutil.move(str(file_path), str(dst_dir / file_path.name))
            return f"成功移动: {file_path.name}"
        except Exception as e:
            return f"错误: {file_path.name} - {e}"


    # 示例2: 使用处理器类
    class FileProcessor:
        def __init__(self, destination):
            self.destination = Path(destination)
            self.processor = ParallelProcessor(safe_parallel())

        def process_files(self, source_path, pattern="*.mp4"):
            files = list(Path(source_path).rglob(pattern))
            file_pairs = [(file, self.destination) for file in files]

            def move_single_file(file_info):
                file_path, dst_dir = file_info
                shutil.move(str(file_path), str(dst_dir / file_path.name))
                return file_path.name

            return self.processor.process(move_single_file, file_pairs)

        @parallel_process(fast_parallel(16))                # 在此处配置
        def batch_rename(self, file_info):
            file_path, new_name = file_info
            old_path = Path(file_path)
            new_path = old_path.parent / new_name
            old_path.rename(new_path)
            return f"重命名: {old_path.name} -> {new_name}"


    # 示例3: 在任意函数中使用
    def process_data():
        # 创建处理器
        processor = ParallelProcessor()

        # 定义处理函数
        def process_item(item):
            # 你的处理逻辑
            time.sleep(0.1)  # 模拟处理时间
            return item * 2

        # 并行处理
        data = range(100)
        results = processor.process(process_item, data)
        return results

    # 使用示例
    # results = process_data()
    # print(f"处理完成，结果数量: {len(results)}")