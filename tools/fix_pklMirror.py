# -*-coding:utf-8-*-
"""
针对立定跳远作弊项目
@Moss 修复数据镜像问题
"""
from pathlib import Path
import pickle
import json

import re


# read all pkl
# path = r"/root/share175/sport_datas/action_recongnition/base_skeleton/train/standjump_videos/train_cheat/skl_points/foul"
path = r"/root/persons/ai_group/Andy/stand_jump_understanding/sample_Zhejiang_wenzhouyuying"
pkl_pth = list(Path(path).rglob('*.pkl'))

mark = r"/root/share175/sport_trains/action_recongnition/base_skeleton/dataGeter_yoloPose/Config/mark_standjump"
mark_dict = {}
scls = []
for file in list(Path(mark).rglob('*.json')):
    with open(file=file, mode='r', encoding='utf-8') as jf:
        mark_info = json.load(jf)
        mirror = mark_info.get('mirror')
        scl = file.stem.rsplit('_',1)[0]
        scls.append(scl)
        ip = file.stem.rsplit('_', 1)[1]
        mark_dict[file.stem] = mirror

scl_ips = list(mark_dict.keys())


for pkl in pkl_pth:
    with open(str(pkl), 'rb') as dat:
        try:
            pkl_dict = pickle.load(dat)
        except Exception as e:
            print(pkl)
            breakpoint()
        det_skpts = pkl_dict['pred_skpts']

        pkl_dict['det_skpts'] = det_skpts              # 备份1下原来的数据
        match = re.search(r'(?<=\.)\d+(?=_)', str(pkl)).group()
        if match in ['100', '206', '101', '102', '103']:
            match_ip = match
        for scl in scls:
            if scl in str(pkl):
                match_scl_ip = scl + '_' + match_ip
        # 拿到了镜像选项
        try:
            if mark_dict[match_scl_ip] == 'True':
                # 这个原始视频 从左往右的
                det_skpts[:, :3] = mark_info.get('vid_width') - det_skpts[:, :3]  # 仅在x方向对称
                print('做复原', pkl.stem)
            else:
                det_skpts[:, ::3] = mark_info.get('vid_width') - det_skpts[:, ::3]  # 仅在x方向对称
                print('做镜像',pkl.stem)
        except:
            print(pkl)

    pkl_dict['pred_skpts'] = det_skpts          # 重新写入正确的 统一镜像到从左边跳的数据

    with open(str(pkl), 'wb') as f:
        pickle.dump(pkl_dict, f)

