# -*-coding:utf-8-*-
"""
将在path_ori中出现的文件，若在dst
"""
import os
import shutil
from pathlib import Path


def move_1():
    path_ori = f"/root/share175/sport_datas/sit_and_reach/classify_cheating/ori_videos/SitReach_oriVideos/Zhengzhou_hangtianhangkongxueyuan/14_sitForward_C_2023_10-1/2023_1019_1520/pred_1_backside_push"   #  pred_1_backside_push
    path_dst = f"/root/share175/sport_datas/sit_and_reach/classify_cheating/ori_videos/SitReach_oriVideos/Zhengzhou_hangtianhangkongxueyuan/14_sitForward_C_2023_10-1"      # pred_2_hand_pull

    lst = [p for p in list(Path(path_ori).rglob('*.mp4'))]

    for p in lst:
        dst = Path(path_dst) / p.name
        shutil.move(str(p),dst)


def move_2():
    path_ori = f"/root/share175/sport_test/sit_and_reach/classify_cheat/model_check_skl_points_tiny_Copy/pre_2_hand_pull/0_normal"  # pred_1_backside_push
    path_dst = f"/root/share175/sport_test/sit_and_reach/classify_cheat/skl_points_tiny/0_normal"  # pred_2_hand_pull

    lst_ori = [p for p in list(Path(path_ori).rglob('*.avi'))]
    lst_dst = [p for p in list(Path(path_dst).rglob('*.avi'))]


    for p in lst_ori:
        for d in lst_dst:
            if p.name == d.name:
                dst = path_dst + os.sep + str(Path(path_ori).parent.stem) + os.sep + str(d).split('/')[-3] + os.sep + str(d).split('/')[-2]
                Path(dst).mkdir(exist_ok=True, parents=True)
                shutil.move(str(d.with_suffix('.pkl')), dst)    # 移动pkl
                shutil.move(str(d), dst)                    # 移动avi
                print(d)

        # dst = Path(path_dst) / p.name
        # shutil.move(str(p), dst)


if __name__ == '__main__':
    pass

    move_2()