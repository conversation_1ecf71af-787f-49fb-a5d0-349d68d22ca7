import os
import re
import shutil
from pathlib import Path



def move_files_by_pattern(source_dir):
    # 确保源目录存在
    if not os.path.exists(source_dir):
        print(f"源目录 {source_dir} 不存在。")
        return

    pattern = re.compile(r'(\.\d+_)')

    # 遍历源目录中的所有文件
    for filename in os.listdir(source_dir):
        # 检查文件名是否符合模式
        match = pattern.search(filename)
        if match:
            folder_name = match.group(1)[1:-1]
            # 构建目标文件夹路径
            target_folder = os.path.join(source_dir, folder_name)
            # 如果目标文件夹不存在，则创建它
            if not os.path.exists(target_folder):
                os.makedirs(target_folder)
            # 构建源文件和目标文件的完整路径
            source_file = os.path.join(source_dir, filename)
            target_file = os.path.join(target_folder, filename)
            # 移动文件
            shutil.move(source_file, target_file)
            print(f"文件 {filename} 已移动到 {target_folder}")



if __name__ == '__main__':

    ori_pth = f"/root/share175/sport_datas/stand_jump/classify_cheating/train/skl_points_vids/fake/stride_jump/Anhui_hefei45zhong"

    for dir in list(Path(ori_pth).glob('*')):
        print(dir)
        move_files_by_pattern(dir)

    # 指定你的源目录路径
    # source_directory = '/root/share175/sport_datas/stand_jump/classify_cheating/train/skl_points_vids/fake/outside_jump/Guangdong_shenzhenlianhuazhongxuenanqu'
    # move_files_by_pattern(source_directory)
