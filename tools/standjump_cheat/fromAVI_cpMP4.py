"""
从avi数据中匹配mp4文件，将同名的MP4文件拷贝到保存路径

# 新增可以匹配到子文件夹    @Moss  2024/11/22  17:29
"""
import json
import shutil
from pathlib import Path

import os
from concurrent.futures import ThreadPoolExecutor, as_completed
from collections import defaultdict


def find_mp4_files(path_mp4):
    # 创建字典 写入
    mp4_dict = {}
    for file_path in Path(path_mp4).rglob('*.mp4'):
        mp4_dict[file_path.stem] = str(file_path)
    return mp4_dict


def multi_find_mp4File(root_dir):
    # 限制并行数量
    num_threads = 8
    with ThreadPoolExecutor(max_workers=num_threads) as exector:
        subdirs = [d for d in os.scandir(root_dir) if d.is_dir()]
        future_to_dir = {exector.submit(find_mp4_files, subdir):subdir for subdir in subdirs}

        all_mp4_file = {}
        for future in as_completed(future_to_dir):
            subdir = future_to_dir[future]
            try:
                files = future.result()
            except Exception as exc:
                print(f"{subdir} generated as exception: {exc}")
            else:
                all_mp4_file.update(files)
        all_mp4_file.update(find_mp4_files(root_dir))

    return all_mp4_file




def find_move_files(path_avi:str, path_mp4:str, path_save:str, match_suffix=None):
    #
    if match_suffix is None:
        match_suffix = f"_len"


    all_mp4s = [str(file.stem) for file in list(Path(path_mp4).rglob('*.mp4'))]
    all_avis = [Path(str(avi_file).split(match_suffix)[0]).name for avi_file in list(Path(path_avi).rglob('*.avi'))]

    match_mp4 = [Path(path_mp4) / Path(str(file)+'.mp4') for file in all_avis if file in all_mp4s]

    for mp4_file in match_mp4:

        shutil.copy(str(mp4_file), path_save)
        print(mp4_file)

    return




def save_path_mp4_pth_dict(path_mp4):
    """
    读取mp4所在的路径，获取所有mp4的路径存成字典
    {mp4_name: mp4_path}
    """
    mp4_dict_name = f'{Path(path_mp4).name}_mp4_dict.json'
    mp4_dict_pth = Path(path_mp4).parent / mp4_dict_name
    # 判断字典是否存在
    if mp4_dict_pth.exists():
        with open(mp4_dict_pth, 'r', encoding='utf-8') as fd:
            mp4_dict = json.load(fd)
            print(f"@Moss:字典加载-----okk")

    else:
        print(f"@Moss: 字典不存在重新创建中----")
        mp4_dict = multi_find_mp4File(path_mp4)
        print(f"字典加载---okk，储存中----")
        with open(mp4_dict_pth, 'w', encoding='utf-8') as fw:
            json.dump(mp4_dict, fw, indent=4)
        print(f"@Moss:字典储存---okk, {mp4_dict_pth}")

    return mp4_dict


def search_aviName_fromeMp4Dict(mp4_dict:dict, path_avi:str, path_save=None, match_suffix='_len'):
    """
    读取保存的mp4名称-路径字典，从中获取mp4路径
    """
    all_names = mp4_dict.keys()
    for avi_file in list(Path(path_avi).rglob('*.avi')):
        avi_name = Path(str(avi_file).split(match_suffix)[0]).name
        if avi_name in all_names:
            mp4_pth = mp4_dict[avi_name]
            dst_pth = Path(str(avi_file.parent))            # 直接在原始avi样本的路径下 将mp4原始视频拷贝过来
            dst_pth.mkdir(exist_ok=True)
            shutil.copy(mp4_pth, dst_pth / Path(mp4_pth).name)

            # 删除pkl文件
            try:
                avi_file.with_suffix('.pkl').unlink()
            except:
                continue

            print(avi_name)
        else:
            print(avi_file)

    print(f'原始视频拷贝-----okk')
    return






if __name__ == '__main__':
    path_avi = r'/root/share175/sport_test/stand_jump/classify_action/skl_points_vids'

    path_mp4 = r'/root/share175/sport_test/stand_jump/classify_action/ori_vids'
    path_save = None
    match_suffix = f"_total"

    # 1. 从给出的路径中 获取到所有的mp4文件
    mp4_dict = save_path_mp4_pth_dict(path_mp4)

    # 2. avi数据匹配 MP4
    search_aviName_fromeMp4Dict(mp4_dict, path_avi, path_save, match_suffix=match_suffix)

    # find_move_files(path_avi, path_mp4, path_save, match_suffix)