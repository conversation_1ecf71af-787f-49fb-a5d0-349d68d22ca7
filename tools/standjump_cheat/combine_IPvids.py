"""
立定跳远 将路径下相同ip的视频合并
"""

import shutil
from pathlib import Path


def combine_IPs(dirs, ip=None):
    dst = Path(dirs).parent / (Path(dirs).name + '_ips') / ip.__str__()
    dst.mkdir(exist_ok=True, parents=True)

    all_vids =[vid for vid in list(Path(dirs).rglob('*.mp4')) if f'.{ip}_' in str(vid)]

    for vid_ip in all_vids:
        print(vid_ip.name)
        shutil.move(vid_ip, dst / vid_ip.name)




if __name__ == '__main__':
    dirs = f"/root/share175/sport_datas/stand_jump/classify_cheating/action_ori_vids_1/Shenzhen_longgangtianyushiyanxuexiao/StandAndJump_freeTest"

    combine_IPs(dirs, ip=207)
