# -*-coding:utf-8-*-
"""
将生成好的样本(avi+pkl), 进行2次划分；
根据落地情况 加入逻辑划分跳到远近的不同区域，以及筛选出脚滑的数据
"""
import pickle
import shutil
from pathlib import Path
import json
import math
from typing import <PERSON><PERSON>


def compute_angle(foot:<PERSON><PERSON>, waist:<PERSON><PERSON>, knee:<PERSON><PERSON>):
    # 仰卧起坐 计算肩-腰-膝 的夹角
    x_S, y_S = foot
    x_W, y_W = knee
    x_K, y_K = waist

    # 计算向量 SW、KW
    SW = (x_W - x_S, y_W - y_S)
    KW = (x_W - x_K, y_W - y_K)
    dot_product = SW[0] * KW[0] + SW[1] * KW[1]             # 计算点积

    # 计算向量的模
    norm_SW = math.sqrt(SW[0] ** 2 + SW[1] ** 2)
    norm_KW = math.sqrt(KW[0] ** 2 + KW[1] ** 2)

    # 计算夹角的余弦值
    cos_theta = dot_product / (norm_SW * norm_KW)
    angle_theta = math.acos(cos_theta) if -1 < cos_theta < 1 else math.acos(-1)              # 计算夹角
    angle_SWK = math.degrees(angle_theta)           # 将弧度转换为度

    return angle_SWK



def predel_marks_func(mark_info):
    # 找到垫子最远的M，N点，MN线段的方程, mark_info['mirror'] = True表示从左向右跳
    # 返回 垫子最远的上、下点，以及上下点的线段方程
    data_Pts = mark_info['MarkPts']
    sum_x, sum_y = 0, 0
    points = []
    for MarkPt in data_Pts:
        key_x = [k for k in MarkPt if k.endswith('_x')][0]
        key_y = [k for k in MarkPt if k.endswith('_y')][0]
        x, y = int(MarkPt[key_x]), int(MarkPt[key_y])
        sum_x += x;     sum_y += y
        points.append((x, y))            # 存储点

    avg_y = sum_y / len(data_Pts)       # 计算y平均
    # 根据y平均分组
    group_up = [p for p in points if p[1] < avg_y]
    group_down = [p for p in points if p[1] > avg_y]
    # 计算y平均
    avg_y_up = sum(p[1] for p in group_up) / len(group_up)
    avg_y_down = sum(p[1] for p in group_down) / len(group_down)

    # 在组里找max和min的x值
    max_, min_ = float('inf'), float('-inf')

    if mark_info.get('mirror')=='True':         # 从左向右跳
        max_x_up = max(group_up, key=lambda p:p[0] if group_up else (min_, min_))
        max_x_down = max(group_down, key=lambda p: p[0] if group_down else (min_, min_))        # z终点

        up_startLine = min(group_up, key=lambda p: p[0] if group_up else (min_, min_))
        down_startLine = min(group_down, key=lambda p: p[0] if group_down else (min_, min_))    # 起跳线

        return [max_x_up[0], max_x_up[1], max_x_down[0], max_x_down[1], avg_y_up, avg_y_down, up_startLine, down_startLine]
    else:
        min_x_up = min(group_up, key=lambda p: p[0] if group_up else (max_, max_))
        min_x_down = min(group_down, key=lambda p: p[0] if group_down else (max_, max_))

        up_startLine = max(group_up, key=lambda p: p[0] if group_up else (max_, max_))
        down_startLine = max(group_down, key=lambda p: p[0] if group_down else (max_, max_))
        # 返回 【上终点x,y, 下终点x,y, 上平均y, 下平均y, 上起点xy, 下起点xy】
        return [min_x_up[0], min_x_up[1], min_x_down[0], min_x_down[1], avg_y_up, avg_y_down, up_startLine, down_startLine]



def foot_score_split(per_seq, mark_info, a_pkl):
    footX_pix = (per_seq[-1][45]+per_seq[-1][48]) / 2
    up_down_ptList = predel_marks_func(mark_info)
    pix_len_scales = abs(up_down_ptList[0] - up_down_ptList[-2][0])  # 跳远垫子像素长度
    score_pix = abs((up_down_ptList[0] + up_down_ptList[2]) / 2 - footX_pix)

    dst_small = a_pkl.parent / 'large_jump'
    dst_middle = a_pkl.parent / 'middle_jump'
    dst_large = a_pkl.parent / 'small_jump'
    dst_slip = a_pkl.parent / 'slip_jump'
    dst_odd = a_pkl.parent / 'handboard_jump'

    a_avi = a_pkl.with_suffix('.avi')
    if score_pix <= 0.33 * pix_len_scales:
        falled_stauts = 4  # 4 表示 落地帧 跳的很远2m及以上
        dst_large.mkdir(exist_ok=True)

        shutil.move(a_pkl, dst_large / a_pkl.name)
        shutil.move(a_avi, dst_large / a_avi.name)
        print(a_pkl.name)
    elif pix_len_scales / 3 < score_pix <= pix_len_scales / 1.5:
        falled_stauts = 3  # 3 表示 落地帧 正常落地区间
        dst_middle.mkdir(exist_ok=True)
        shutil.move(a_pkl, dst_middle / a_pkl.name)
        shutil.move(a_avi, dst_middle / a_avi.name)
        print(a_pkl.name)

    else:
        falled_stauts = 2  # 跳的很近，1.2m以内
        dst_small.mkdir(exist_ok=True)
        target_fall = per_seq[-1]

        foot = ((target_fall[48]+target_fall[45]) / 2, (target_fall[49]+target_fall[46]) / 2)
        waist = ((target_fall[33]+target_fall[36]) / 2, (target_fall[34]+target_fall[37]) / 2)
        knee = ((target_fall[39]+target_fall[42]) / 2, (target_fall[40]+target_fall[43]) / 2)
        shoulder = ((target_fall[15]+target_fall[18]) / 2, (target_fall[16]+target_fall[19]) / 2)
        wrist = ((target_fall[27]+target_fall[30]) / 2, (target_fall[28]+target_fall[31]) / 2)      # 手腕
        angle_FKW = compute_angle(foot, waist, knee)
        lay_warist = knee[1] - waist[1] < 0
        layed = shoulder[0] < waist[0]             #  if mark_info.get('mirror')=="True" else shoulder[0] > waist[0]

        if angle_FKW >= 115 and lay_warist:
            dst_slip.mkdir(exist_ok=True)
            shutil.move(a_pkl, dst_slip / a_pkl.name)
            shutil.move(a_avi, dst_slip / a_avi.name)
        elif abs(waist[0]-wrist[0]) - abs(foot[0]-waist[0]) > 0 and layed and lay_warist:
            dst_odd.mkdir(exist_ok=True)
            shutil.move(a_pkl, dst_odd / a_pkl.name)
            shutil.move(a_avi, dst_odd / a_avi.name)
        else:
            shutil.move(a_pkl, dst_small / a_pkl.name)
            shutil.move(a_avi, dst_small / a_avi.name)

        print(a_pkl.name)


path = f"/root/share175/sport_datas/stand_jump/classify_cheating/train/side_jump_待排查/Jump______Unchecked/Fujian_fuzhougezhizhongxuegushanxiaoqu_100/large_jump"
mark_file= f"/root/share175/sport_trains/action_recongnition/base_skeleton/dataGeter_yoloPose/Config/mark_standjump/Fujian_fuzhougezhizhongxuegushanxiaoqu_100.json"

with open(file=Path(mark_file).absolute(), mode='r', encoding='utf-8') as jf:
    mark_info = json.load(jf)

all_pkls = list(Path(path).rglob('*.pkl'))

for a_pkl in all_pkls:
    with open(str(a_pkl), 'rb') as file:
        data_dict = pickle.load(file)
    per_seq = data_dict['pred_skpts']
    foot_score_split(per_seq, mark_info, a_pkl)
