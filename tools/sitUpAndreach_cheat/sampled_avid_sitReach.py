# -*-coding:utf-8-*-
"""
坐位体前屈
从指定的路径中抽取 指定比例的 样本(avi + pkl)
放到 默认的新路径中
"""
import copy
import random
from pathlib import Path

give_path = \
f"/root/share175/sport_datas/sit_and_reach/classify_cheating/ori_videos/SitUp_oriVideos/Hebei_xiangyangdongjinxingquhaorangzhongxue/AVI_data_Matched/5_lookers_nearby/sampled"

rate = 0.10

dst_pth = Path(give_path).parent / 'Moss_sampled_forTestset'

dst_pth.mkdir(exist_ok=True)


avi_file = list(Path(give_path).rglob('*.avi'))

num_Selected = int(len(avi_file) * rate)


selected_file = random.sample(avi_file, num_Selected)

# 移动到 默认路径中
for file in selected_file:
    file_pkl = copy.deepcopy(file)

    target = dst_pth / file.name
    target_pkl = dst_pth / file_pkl.with_suffix('.pkl').name

    file.rename(target)
    file_pkl.with_suffix('.pkl').rename(target_pkl)

    print(file)


