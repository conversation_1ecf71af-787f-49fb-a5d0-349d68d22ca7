# -*-coding:utf-8-*-
"""
从样本中过滤出 时长较长的pkl
从样本中过滤出 时长较短的pkl

"""
import pickle
import os
from pathlib import Path
import multiprocessing as mp
from typing import List, Set, Generator, Tuple
from concurrent.futures import ProcessPoolExecutor
from tqdm import tqdm

from ReadTypeFile_Speed import rglob_optimized_generator, rglob_optimized
from multiprocessing import Pool
from multiParallel_toolkit import parallel_process, process_parallel
from functools import partial


def deal_a_sample(a_pkl, limit):
    with open(a_pkl, "rb") as fp:
        data = pickle.load(fp)
    time = data["end"] - data["start"]
    max_val, min_val = limit[-1], limit[0]
    if time >= max_val:
        move_file(a_pkl, name_pre=f"LongTime_{max_val}")
        print(a_pkl.name, time)
    elif time <= min_val:
        move_file(a_pkl, name_pre=f"ShortTime_{min_val}")
        print(a_pkl.name, time)
    else:
        skpts = data["ori_skpts"]
        person = 0
        for skpt_lst in skpts:
            if skpt_lst[1] is not None or skpts[-1] is not None:
                person += 1  # 有人，位置未知
        if person < data["len_status"] * 0.35:
            move_file(a_pkl, name_pre="One_Person")
            print(a_pkl.name, f"Pn{person}")
        else:
            print(a_pkl.name, f"rate{person}")


def _process_single_directory(args: Tuple[str, Set[str]]) -> List[str]:
    """处理单个目录的全局函数"""
    directory, extensions = args
    local_files = []

    for dirpath, dirnames, filenames in os.walk(directory):
        dirnames[:] = [d for d in dirnames if not d.startswith(".")]
        for filename in filenames:
            if any(filename.lower().endswith(ext) for ext in extensions):
                local_files.append(os.path.join(dirpath, filename))

    return local_files


def find_files_multiprocess(
    root_directory: str, file_extensions: List[str], max_workers: int = None
) -> List[str]:
    """
    多进程超高速文件搜索 - 适合大型目录

    Args:
        root_directory: 根目录路径
        file_extensions: 文件扩展名列表
        max_workers: 最大进程数，默认为CPU核心数

    Returns:
        匹配文件的完整路径列表
    """
    if not os.path.exists(root_directory):
        return []

    # 预处理扩展名
    extensions = {
        ext.lower() if ext.startswith(".") else f".{ext.lower()}"
        for ext in file_extensions
    }
    max_workers = max_workers or min(mp.cpu_count(), 8)  # 限制最大进程数

    # 获取顶层目录
    # try:
    with os.scandir(root_directory) as entries:
        top_dirs = [
            entry.path
            for entry in entries
            if entry.is_dir() and not entry.name.startswith(".")
        ]
        root_files = []

        # 处理根目录文件
        for entry in entries:
            if entry.is_file() and any(
                entry.name.lower().endswith(ext) for ext in extensions
            ):
                root_files.append(os.path.join(root_directory, entry.name))
    # except (PermissionError, OSError):
    #     return []

    if not top_dirs:
        return root_files

    # 准备多进程参数
    args_list = [(directory, extensions) for directory in top_dirs]
    all_files = root_files[:]

    # try:
    with ProcessPoolExecutor(max_workers=max_workers) as executor:
        results = executor.map(_process_single_directory, args_list)
        for result in tqdm(results):
            all_files.extend(result)
    # except Exception as e:
    #     print(f"多进程处理出错，回退到单线程: {e}")
    #     # 回退到单线程处理
    #     for directory in top_dirs:
    #         try:
    #             for dirpath, dirnames, filenames in os.walk(directory):
    #                 dirnames[:] = [d for d in dirnames if not d.startswith('.')]
    #                 for filename in filenames:
    #                     if any(filename.lower().endswith(ext) for ext in extensions):
    #                         all_files.append(os.path.join(dirpath, filename))
    #         except (PermissionError, OSError):
    #             continue

    return all_files


def move_file(a_pkl, name_pre="LongTime"):
    src3 = str(a_pkl.with_suffix(".avi"))
    src2_lst = list((p := a_pkl).parent.glob(f"{p.stem}-*.jpg"))  # 海象运算

    # model-check / 模型认为的标签 / 真实类别
    key_dst = a_pkl.parents[1] / name_pre / a_pkl.stem
    key_dst.mkdir(exist_ok=True, parents=True)
    Path(src3).rename(key_dst / Path(src3).name)
    a_pkl.rename(key_dst / a_pkl.name)  # txt last
    [src2.rename(key_dst / src2.name) for src2 in src2_lst]

    try:
        a_pkl.parent.rmdir()  # 删除空文件夹
    except:
        pass


def process_pkl_with_limit(a_pkl: Path, limit: Tuple[int, int]) -> None:
    """
    处理单个pkl文件的包装函数，用于多进程

    Args:
        a_pkl: pkl文件路径
        limit: 时长限制元组 (min_time, max_time)
    """
    # 在子进程中处理torch依赖问题
    try:
        return deal_a_sample(a_pkl, limit)
    except ModuleNotFoundError as e:
        if 'torch' in str(e):
            # 如果是torch相关错误，尝试使用安全的pickle加载方式
            return deal_a_sample_safe(a_pkl, limit)
        else:
            raise e


def deal_a_sample_safe(a_pkl: Path, limit: Tuple[int, int]) -> None:
    """
    安全版本的样本处理函数，处理包含torch张量的pkl文件
    """
    import pickle
    import sys

    # 创建一个安全的unpickler，忽略torch相关的对象
    class SafeUnpickler(pickle.Unpickler):
        def find_class(self, module, name):
            # 如果遇到torch相关的类，返回一个占位符
            if module.startswith('torch'):
                return lambda *args, **kwargs: None
            return super().find_class(module, name)

    try:
        with open(a_pkl, "rb") as fp:
            # 使用安全的unpickler
            unpickler = SafeUnpickler(fp)
            data = unpickler.load()

        # 检查数据结构是否完整
        if not isinstance(data, dict) or 'end' not in data or 'start' not in data:
            print(f"跳过文件 {a_pkl.name}: 数据结构不完整")
            return

        time = data["end"] - data["start"]
        max_val, min_val = limit[-1], limit[0]

        if time >= max_val:
            move_file(a_pkl, name_pre=f"LongTime_{max_val}")
            print(a_pkl.name, time)
        elif time <= min_val:
            move_file(a_pkl, name_pre=f"ShortTime_{min_val}")
            print(a_pkl.name, time)
        else:
            # 对于包含torch张量的ori_skpts，我们简化处理逻辑
            if "ori_skpts" in data and "len_status" in data:
                skpts = data["ori_skpts"]
                person = 0
                try:
                    for skpt_lst in skpts:
                        if skpt_lst is not None and len(skpt_lst) > 1:
                            if skpt_lst[1] is not None or (len(skpts) > 0 and skpts[-1] is not None):
                                person += 1

                    if person < data["len_status"] * 0.35:
                        move_file(a_pkl, name_pre="One_Person")
                        print(a_pkl.name, f"Pn{person}")
                    else:
                        print(a_pkl.name, f"rate{person}")
                except Exception as e:
                    print(f"处理骨架点数据时出错 {a_pkl.name}: {e}")
            else:
                print(a_pkl.name, "normal_time")

    except Exception as e:
        print(f"处理文件 {a_pkl.name} 时出错: {e}")


if __name__ == "__main__":
    import multiprocessing as mp
    from concurrent.futures import ProcessPoolExecutor, as_completed
    from tqdm import tqdm

    path = f"/media/pyl/WD_Blue_1T/All_proj/pose_rgb/0_normal/Normal_scls_onePerson"

    # pkls = list(Path(path).rglob('*.pkl'))
    # pkls = find_files_multiprocess(path, ['*.pkl'])  # 类别下的所有样本
    pkls = rglob_optimized(path, f"*.pkl", max_workers=4)
    pkls = [Path(pkl) for pkl in pkls]  # 确保是Path对象

    limit = (20, 45)

    print(f"找到 {len(pkls)} 个pkl文件")

    # 先测试单进程版本，确认是否有torch依赖问题
    print("测试单进程处理...")
    test_pkl = pkls[0] if pkls else None
    if test_pkl:
        try:
            deal_a_sample(test_pkl, limit)
            print(f"单进程测试成功: {test_pkl.name}")
        except Exception as e:
            print(f"单进程测试失败: {e}")
            print("错误可能是由于pkl文件包含torch张量，需要在子进程中导入torch")

    # 方案A：使用ProcessPoolExecutor（推荐）
    max_workers = min(mp.cpu_count(), len(pkls), 8)  # 限制最大进程数
    print(f"开始多进程处理，使用 {max_workers} 个进程...")

    with ProcessPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务
        future_to_pkl = {
            executor.submit(process_pkl_with_limit, pkl, limit): pkl
            for pkl in pkls
        }

        # 处理结果
        completed = 0
        for future in tqdm(as_completed(future_to_pkl), total=len(pkls), desc="处理进度"):
            pkl = future_to_pkl[future]
            try:
                future.result()  # 获取结果，如果有异常会抛出
                completed += 1
            except Exception as e:
                print(f"处理文件 {pkl.name} 时出错: {e}")

    print(f"处理完成！成功处理 {completed}/{len(pkls)} 个文件")
