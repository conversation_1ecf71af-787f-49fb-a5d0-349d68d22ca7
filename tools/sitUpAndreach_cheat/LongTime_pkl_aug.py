# -*-coding:utf-8-*-
"""
从样本中过滤出 时长较长的pkl
从样本中过滤出 时长较短的pkl

修复版本：解决多进程序列化问题
"""
import pickle
import os
from pathlib import Path
import multiprocessing as mp
from typing import List, Set, Generator, Tuple
from concurrent.futures import ProcessPoolExecutor
from tqdm import tqdm

from ReadTypeFile_Speed import rglob_optimized
from multiprocessing import Pool
from functools import partial
from multiParallel_toolkit_aug import simple_parallel_process


def deal_a_sample(a_pkl: Path, limit: Tuple[int, int]) -> None:
    """
    处理单个pkl文件的函数，分析时长并移动到相应文件夹

    Args:
        a_pkl: pkl文件路径
        limit: 时长限制范围 (min_val, max_val)
    """
    with open(a_pkl, "rb") as fp:
        data = pickle.load(fp)
    time = data["end"] - data["start"]
    max_val, min_val = limit[-1], limit[0]
    if time >= max_val:
        move_file(a_pkl, name_pre=f"LongTime_{max_val}")
        print(a_pkl.name, time)
    elif time <= min_val:
        move_file(a_pkl, name_pre=f"ShortTime_{min_val}")
        print(a_pkl.name, time)
    else:
        skpts = data["ori_skpts"]
        person = 0
        for skpt_lst in skpts:
            if skpt_lst[1] is not None or skpts[-1] is not None:
                person += 1  # 有人，位置未知
        if person < data["len_status"] * 0.35:
            move_file(a_pkl, name_pre="One_Person")
            print(a_pkl.name, f"Pn{person}")
        else:
            print(a_pkl.name, f"rate{person}")


def _process_single_directory(args: Tuple[str, Set[str]]) -> List[str]:
    """处理单个目录的全局函数"""
    directory, extensions = args
    local_files = []

    for dirpath, dirnames, filenames in os.walk(directory):
        dirnames[:] = [d for d in dirnames if not d.startswith(".")]
        for filename in filenames:
            if any(filename.lower().endswith(ext) for ext in extensions):
                local_files.append(os.path.join(dirpath, filename))

    return local_files


def find_files_multiprocess(
    root_directory: str, file_extensions: List[str], max_workers: int = None
) -> List[str]:
    """
    多进程超高速文件搜索 - 适合大型目录

    Args:
        root_directory: 根目录路径
        file_extensions: 文件扩展名列表
        max_workers: 最大进程数，默认为CPU核心数

    Returns:
        匹配文件的完整路径列表
    """
    if not os.path.exists(root_directory):
        return []

    # 预处理扩展名
    extensions = {
        ext.lower() if ext.startswith(".") else f".{ext.lower()}"
        for ext in file_extensions
    }
    max_workers = max_workers or min(mp.cpu_count(), 8)  # 限制最大进程数

    # 获取顶层目录
    with os.scandir(root_directory) as entries:
        top_dirs = [
            entry.path
            for entry in entries
            if entry.is_dir() and not entry.name.startswith(".")
        ]
        root_files = []

        # 处理根目录文件
        for entry in entries:
            if entry.is_file() and any(
                entry.name.lower().endswith(ext) for ext in extensions
            ):
                root_files.append(os.path.join(root_directory, entry.name))

    if not top_dirs:
        return root_files

    # 准备多进程参数
    args_list = [(directory, extensions) for directory in top_dirs]
    all_files = root_files[:]

    with ProcessPoolExecutor(max_workers=max_workers) as executor:
        results = executor.map(_process_single_directory, args_list)
        for result in tqdm(results):
            all_files.extend(result)

    return all_files


def move_file(a_pkl: Path, name_pre: str = "LongTime") -> None:
    """
    移动文件到指定目录

    Args:
        a_pkl: pkl文件路径
        name_pre: 目录前缀名
    """
    src3 = str(a_pkl.with_suffix(".avi"))
    src2_lst = list((p := a_pkl).parent.glob(f"{p.stem}-*.jpg"))  # 海象运算

    # model-check / 模型认为的标签 / 真实类别
    key_dst = a_pkl.parents[1] / name_pre / a_pkl.stem
    key_dst.mkdir(exist_ok=True, parents=True)
    Path(src3).rename(key_dst / Path(src3).name)
    a_pkl.rename(key_dst / a_pkl.name)  # txt last
    [src2.rename(key_dst / src2.name) for src2 in src2_lst]

    try:
        a_pkl.parent.rmdir()  # 删除空文件夹
    except:
        pass


def deal_a_sample_with_limit(pkl_path: str, limit: Tuple[int, int]) -> None:
    """
    用于多进程的包装函数 - 这个函数是模块级别的，可以被pickle序列化

    Args:
        pkl_path: pkl文件路径字符串
        limit: 时长限制范围
    """
    return deal_a_sample(Path(pkl_path), limit)


def process_pkls_multiprocessing_pool(
    pkls: List[Path], limit: Tuple[int, int], max_workers: int = None) -> None:
    """
    使用multiprocessing.Pool的简单多进程处理方案

    Args:
        pkls: pkl文件路径列表
        limit: 时长限制范围
        max_workers: 最大进程数
    """
    if not pkls:
        print("没有找到pkl文件")
        return

    max_workers = max_workers or mp.cpu_count()
    pkl_paths = [str(pkl) for pkl in pkls]

    print(
        f"开始使用multiprocessing.Pool处理 {len(pkl_paths)} 个文件，使用 {max_workers} 个进程..."
    )

    # 创建带参数的函数
    process_func = partial(deal_a_sample_with_limit, limit=limit)

    with Pool(max_workers) as pool:
        # 使用tqdm显示进度
        list(tqdm(pool.imap(process_func, pkl_paths), total=len(pkl_paths)))

    print(f"处理完成！")


def process_pkls_processpool_executor(
    pkls: List[Path], limit: Tuple[int, int], max_workers: int = None
) -> None:
    """
    使用ProcessPoolExecutor的多进程处理方案

    Args:
        pkls: pkl文件路径列表
        limit: 时长限制范围
        max_workers: 最大进程数
    """
    if not pkls:
        print("没有找到pkl文件")
        return

    max_workers = max_workers or mp.cpu_count()
    pkl_paths = [str(pkl) for pkl in pkls]

    print(
        f"开始使用ProcessPoolExecutor处理 {len(pkl_paths)} 个文件，使用 {max_workers} 个进程..."
    )

    with ProcessPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务
        futures = [
            executor.submit(deal_a_sample_with_limit, pkl_path, limit)
            for pkl_path in pkl_paths
        ]

        # 使用tqdm显示进度
        for future in tqdm(futures, total=len(futures)):
            try:
                future.result()  # 获取结果，如果有异常会抛出
            except Exception as e:
                print(f"处理文件时出错: {e}")

    print(f"处理完成！")


def process_pkls_sequential(pkls: List[Path], limit: Tuple[int, int]) -> None:
    """
    顺序处理方案（用于对比或调试）

    Args:
        pkls: pkl文件路径列表
        limit: 时长限制范围
    """
    print(f"开始顺序处理 {len(pkls)} 个文件...")

    for pkl in tqdm(pkls):
        try:
            deal_a_sample(pkl, limit)
        except Exception as e:
            print(f"处理文件 {pkl} 时出错: {e}")

    print("处理完成！")


if __name__ == "__main__":
    path = f"/media/pyl/WD_Blue_1T/All_proj/pose_rgb/0_normal/Normal_scls_onePerson"

    # pkls = list(Path(path).rglob('*.pkl'))
    # pkls = find_files_multiprocess(path, ['*.pkl'])  # 类别下的所有样本
    pkls = rglob_optimized(path, f"*.pkl", max_workers=4)
    # pkls = [Path(p) for p in pkls]  # 转换为Path对象

    limit = (20, 45)

    print(f"找到 {len(pkls)} 个pkl文件")

    # 选择处理方式 - 推荐使用方案1或方案2
    process_func = partial(deal_a_sample, limit=limit)
    results = simple_parallel_process(process_func, pkls, max_workers=4)

    # 方案1：使用multiprocessing.Pool（推荐）
    # process_pkls_multiprocessing_pool(pkls, limit, max_workers=4)

    # 方案2：使用ProcessPoolExecutor（备选）
    # process_pkls_processpool_executor(pkls, limit, max_workers=4)

    # 方案3：顺序处理（调试用）
    # process_pkls_sequential(pkls, limit)
