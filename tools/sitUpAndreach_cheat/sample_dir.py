# -*-coding:utf-8-*-
"""
将脚本采集的样本 按样本名 建立文件夹分组
1个文件夹即1个样本，仍保存在原目录
"""

from pathlib import Path



samples_pth = f"/root/share175/sport_datas/sit_up/classify_cheating/ori_vids_fusion/武汉工商学院/已抽样/2025_4_26_14_39_49_192"

samples_pth = Path(samples_pth)

all_samples = list(samples_pth.rglob('*.pkl'))

for a_sample in all_samples:
    sample_jpgs = [p for p in a_sample.parent.rglob('*.jpg') if a_sample.stem in p.name]
    sample_avi = a_sample.with_suffix('.avi')

    dir_name = a_sample.stem
    dst = a_sample.parent / dir_name
    dst.mkdir(exist_ok=True)

    for pic in sample_jpgs:
        pic.rename(dst / pic.name)
    sample_avi.rename(dst / sample_avi.name)
    a_sample.rename(dst / a_sample.name)

    print(a_sample.name)


