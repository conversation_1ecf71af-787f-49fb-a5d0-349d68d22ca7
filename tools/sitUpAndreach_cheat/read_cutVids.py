# -*-coding:utf-8-*-
"""
坐位体前屈 数据拷贝脚本
将夜跑脚本生成的txt文档，文档中的视频剪切到 txt路径下的文件夹中
"""
import json
import os
import shutil
from pathlib import Path
import ast

import chardet


def read_move_sitReach(rec_pred_dir):


    rec_pred_dir = rec_pred_dir.replace('\\', '/')
    # 获取路径下  所有以rec开头 的txt文件
    rec_pred_txt = [path for path in Path(rec_pred_dir).rglob('rec_*.txt')]

    # print(rec_pred_txt)
    for rec_txt in rec_pred_txt:
        print(rec_txt.name)
        with open(rec_txt, 'r', encoding=encode_method, errors='ignore') as ft:
            vid_path = ft.readlines()
        dst = (rec_txt.parent / rec_txt.stem)
        dst.mkdir(exist_ok=True)
        for vid in vid_path:
            if vid.startswith('Z:'):
                sub_pth = vid.split(Path(rec_pred_dir).name)[-1].replace("\\", "/")
                vid = rec_pred_dir + sub_pth

            src = vid.rsplit('\n')[0]
            if not Path(src).exists():
                print(f"exists False: {src}")
                continue
            try:
                shutil.move(src, dst)
            except:
                print(f"No file: {src}")
                continue
            print(src)

    return


def read_move_sitUp(rec_pred_txt):
    rec_pred_txt = rec_pred_txt.replace('\\', '/')
    label_name = Path(rec_pred_txt).stem.split('_')[-1]

    with open(rec_pred_txt, 'r') as ft:
        vid_path = ft.readlines()

    all_vids = {str(p.name): str(p) for p in Path(rec_pred_txt).parent.rglob('*.mp4')}

    rec_pred = Path(rec_pred_txt).parent / Path(rec_pred_txt).stem
    rec_pred.mkdir(exist_ok=True, parents=True)
    if label_name == 'labels':
        dst_filter = rec_pred / 'filter'
        dst_normal = rec_pred / 'normal'
        dst_violate = rec_pred / 'violate'
        dst_single = rec_pred / 'single'
        dst_near = rec_pred / 'single'
        dst_filter.mkdir(exist_ok=True, parents=True)
        dst_normal.mkdir(exist_ok=True, parents=True)
        dst_violate.mkdir(exist_ok=True, parents=True)
        dst_single.mkdir(exist_ok=True, parents=True)
        dst_near.mkdir(exist_ok=True, parents=True)
        filter_dict, normal_dict, other_dict = {}, {}, {}
        violate_dict, single_dict, near_dict = {}, {}, {}

        for idx, val in enumerate(vid_path):
            if idx % 3 in [0,2]:
                continue        # \n
            elif idx % 3 == 1:
                # 1. 0的数量>=len/2 && 2的数量>2 && len > 10
                lst_str = ast.literal_eval(val)
                len_lst = len(lst_str)
                count_1, count_2 = lst_str.count(1), lst_str.count(2)
                count_0, count_3, count_4, count_5 = lst_str.count(0), lst_str.count(3), lst_str.count(4), lst_str.count(5)
                mp4_path = all_vids.get(vid_path[idx - 1].split(':')[0], False)
                if not mp4_path:
                    continue
                if len_lst <= 10:
                    filter_dict[mp4_path] = val.split('\n')
                    shutil.move(mp4_path, dst_filter)
                    print(mp4_path)
                    continue
                elif count_0 > len_lst / 2 and count_2 >2:
                    normal_dict[mp4_path] = val.split('\n')         # 要求至少一半是正常做， 且中间出现至少2个pull
                    shutil.move(mp4_path, dst_normal)
                    print(mp4_path)
                    continue
                elif count_3 >= len_lst * 0.5:
                    violate_dict[mp4_path] = val.split('\n')        # 违规项目
                    shutil.move(mp4_path, dst_violate)
                    print(mp4_path)
                    continue
                elif count_4 >= len_lst * 0.75:
                    single_dict[mp4_path] = val.split('\n')        # single
                    shutil.move(mp4_path, dst_single)
                    print(mp4_path)
                    continue
                elif count_5 >= len_lst / 3:
                    near_dict[mp4_path] = val.split('\n')        # near
                    shutil.move(mp4_path, dst_near)
                    print(mp4_path)
                    continue
                else:
                    other_dict[mp4_path] = val.split('\n')

        with open(rec_pred / 'dst_filter.json', 'w') as fs:
            json.dump(filter_dict, fs, indent=4)                # 过滤 少于10个的
        with open(rec_pred / 'dst_normal.json', 'w') as f0:
            json.dump(normal_dict, f0, indent=4)                # 大部分都是正常做
        with open(rec_pred / 'dst_violate.json', 'w') as f3:
            json.dump(violate_dict, f3, indent=4)                # 大部分都是违规
        with open(rec_pred / 'dst_single.json', 'w') as f4:
            json.dump(single_dict, f4, indent=4)                # 大部分都是single
        with open(rec_pred / 'dst_near.json', 'w') as f5:
            json.dump(near_dict, f5, indent=4)                # 大部分都是near
        with open(rec_pred / 'other_dict.json', 'w') as fN:
            json.dump(other_dict, fN, indent=4)

    elif label_name in ['normal', 'single']:
        dst_normal = rec_pred
        for idx, val in enumerate(vid_path):
            if idx % 3 in [0,2]:
                continue        # \n
            elif idx % 3 == 1:
                name = vid_path[idx - 1].split(':')[0]
                mp4_path = all_vids.get(name, False)
                print(name)
                if mp4_path is False:
                    continue
                shutil.move(mp4_path, dst_normal)
                print(mp4_path)

    return




if __name__ == '__main__':
    # TODO：体前屈项目————给txt所在 文件夹的路径
    rec_pred_dir = f'/root/share175/sport_datas/sit_and_reach/classify_cheating/ori_videos' + os.sep +\
                   r'SitReach_oriVideos_1/Hubei_wuhangongshangxueyuan/SitBodyForward_freeTest'

    # TODO：仰卧起坐项目————给txt 文件路径
    rec_pred_txt = r'/root/share175/sport_datas/sit_and_reach/classify_cheating/ori_videos' \
                   r'/SitUp_oriVideos/Haerbing_jihongxiaoxuehaxixiaoqu/SitAndUp_freeTest_' \
                   r'/rec_pred_labels.txt'

# =====================================================================================

    # _Select = 'reach'
    _Select = input('请输入所选项目[reach|up]:')

    encode_method = 'GBK'       # 'utf-8'

    if _Select in ['reach','Reach']:
        print(rec_pred_dir)
        read_move_sitReach(rec_pred_dir)  # 体前屈
    elif _Select in ['up', 'Up']:
        print(rec_pred_txt)
        read_move_sitUp(rec_pred_txt)           # 仰卧起坐
    else:
        raise ValueError(f'no proj named {_Select}')


