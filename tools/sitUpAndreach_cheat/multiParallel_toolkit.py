# -*-coding:utf-8-*-
"""
多线程/进程 并行工具包
多线程处理: 适合
    文件操作: 移动、复制、重命名大量文件: 建议32-64线程最佳 ———— 核心数 × 2-8
    数据处理: 批量处理图片、视频、文档
    网络请求: 并行API调用：              建议50-100线程最佳 ———— 核心数 × 5-12
    计算任务: CPU密集型并行计算：           建议8-16线程最佳 ———— 核心数 × 1-2
多进程 (适合CPU密集型):
    数据处理: 批量处理图片、视频、文档
    计算任务: CPU密集型并行计算 (建议使用CPU核心数)

Usage Example:
# 1. 多线程示例
@parallel_process(fast_parallel(32))
def move_file(file_info):
    file, dst = file_info
    shutil.move(file, dst / file.name)

file_pairs = [(file, dst) for file in list(Path(path1).rglob('*.mp4'))]
results = move_file(file_pairs)

# 2. CPU密集型任务 (使用多进程)
@parallel_process(process_parallel())
def complex_calculation(number):
    # ... heavy calculation logic

"""
import functools
import time
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor, as_completed
from typing import Callable, Optional
import os
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class ParallelConfig:
    """并行处理配置类"""

    def __init__(self,
                 max_workers: Optional[int] = None,
                 executor_type: str = 'thread',
                 chunk_size: int = 1,
                 show_progress: bool = True,
                 timeout: Optional[float] = None):
        if max_workers is None:
            # 为多进程和多线程提供更合理的默认工作数
            if executor_type == 'process':
                self.max_workers = os.cpu_count() or 1
            else:
                self.max_workers = min(32, (os.cpu_count() or 1) * 5)
        else:
            self.max_workers = max_workers

        self.executor_type = executor_type  # 'thread' or 'process'
        self.chunk_size = chunk_size
        self.show_progress = show_progress
        self.timeout = timeout


def parallel_process(config: Optional[ParallelConfig] = None):
    """
    并行处理装饰器
    """
    if config is None:
        config = ParallelConfig()

    def decorator(func: Callable):
        @functools.wraps(func)
        def wrapper(iterable, *args, **kwargs):
            # 确保在Windows或macOS上使用spawn或forksafe方式启动多进程
            # 这是为了兼容性和避免潜在的死锁问题
            if config.executor_type == 'process':
                import multiprocessing
                if multiprocessing.get_start_method(allow_none=True) is None:
                    multiprocessing.set_start_method('spawn', force=True)
            return _execute_parallel(func, iterable, config, *args, **kwargs)

        return wrapper

    return decorator


def _task_wrapper_with_args(item_and_args):
    """模块级别的任务包装函数，用于多进程序列化"""
    item, func, args, kwargs = item_and_args
    return func(item, *args, **kwargs)


def _execute_parallel(func: Callable, iterable, config: ParallelConfig, *args, **kwargs):
    """执行并行处理的核心逻辑"""
    items = list(iterable)
    total_items = len(items)

    if total_items == 0:
        return []

    if config.show_progress:
        logger.info(f"开始使用 [{config.executor_type}] 模式并行处理 {total_items} 个项目...")

    # 根据配置选择线程池或进程池
    executor_class = ThreadPoolExecutor if config.executor_type == 'thread' else ProcessPoolExecutor

    results = []
    start_time = time.time()

    with executor_class(max_workers=config.max_workers) as executor:
        if args or kwargs:
            # 为多进程准备数据
            items_with_args = [(item, func, args, kwargs) for item in items]
            future_to_item = {executor.submit(_task_wrapper_with_args, item_args): item_args[0] 
                            for item_args in items_with_args}
        else:
            future_to_item = {executor.submit(func, item): item for item in items}

        completed_count = 0
        for future in as_completed(future_to_item, timeout=config.timeout):
            try:
                result = future.result()
                results.append(result)
                completed_count += 1

                if config.show_progress and completed_count % max(1, total_items // 20) == 0:
                    progress = (completed_count / total_items) * 100
                    logger.info(f"进度: {progress:.1f}% ({completed_count}/{total_items})")

            except Exception as e:
                item = future_to_item[future]
                logger.error(f"@Moss func _execute_parallel: 处理项目 {item} 时发生错误: {e}")
                results.append(None)

    elapsed_time = time.time() - start_time
    if config.show_progress:
        logger.info(f"并行处理完成，耗时: {elapsed_time:.2f}秒")

    return results


# --- 便捷的预定义配置 ---

def fast_parallel(max_workers: int = 32):
    """(多线程) 快速并行配置，适合I/O密集型任务"""
    return ParallelConfig(max_workers=max_workers, executor_type='thread', show_progress=True)


def safe_parallel(max_workers: int = 8):
    """(多线程) 安全并行配置（较少线程）"""
    return ParallelConfig(max_workers=max_workers, executor_type='thread', show_progress=True)


def process_parallel(max_workers: Optional[int] = None):
    """(多进程) 并行配置，适合CPU密集型任务"""
    num_workers = max_workers or os.cpu_count() or 1
    return ParallelConfig(
        max_workers=num_workers,
        executor_type='process',
        show_progress=True
    )


# --- 使用示例 ---
# **重要[为了防止在创建子进程时无限递归地执行代码]**: 使用多进程时，必须将主程序逻辑放在 `if __name__ == "__main__":` 块内。
if __name__ == "__main__":
    # --- 示例1: 多进程处理CPU密集型任务 ---
    print("=" * 20, "多进程CPU密集型任务示例", "=" * 20)


    @parallel_process(process_parallel())  # <-- 使用多进程配置
    def cpu_bound_task(number):
        """一个模拟CPU密集型计算的函数"""
        # print(f"进程 {os.getpid()} 正在计算 {number}...")
        result = sum(i * i for i in range(number))
        return result


    # 一组需要大量计算的数字
    numbers_to_process = [100000, 200000, 300000, 400000] * 10
    process_results = cpu_bound_task(numbers_to_process)
    # print("多进程计算结果（部分）:", process_results[:5])
    print("\n")

    # --- 示例2: 多线程处理I/O密集型任务 (模拟) ---
    print("=" * 20, "多线程I/O密集型任务示例", "=" * 20)


    @parallel_process(fast_parallel(16))  # <-- 使用多线程配置
    def io_bound_task(url):
        """一个模拟文件下载的函数 (I/O密集型)"""
        # print(f"线程 {threading.get_ident()} 开始下载 {url}")
        time.sleep(1)  # 模拟网络延迟或文件读写耗时
        return f"Downloaded {url}"


    urls = [f"file_{i}.dat" for i in range(20)]
    thread_results = io_bound_task(urls)
    # print("多线程下载结果（部分）:", thread_results[:5])