# 使用示例 - 数据集长宽比统计工具

## 快速开始

### 1. 运行测试验证功能

```bash
# 运行内置测试
./tools/sitUpAndreach_cheat/run_statistic_WH_aug.sh --test

# 或者直接运行测试脚本
python3 tools/sitUpAndreach_cheat/test_statistic_WH_aug.py
```

### 2. 使用默认配置

```bash
# 使用默认参数运行（需要修改脚本中的默认路径）
python3 tools/sitUpAndreach_cheat/statistic_WH_aug.py
```

### 3. 指定数据集路径

```bash
# 基本使用
python3 tools/sitUpAndreach_cheat/statistic_WH_aug.py \
    --data_path /path/to/your/dataset

# 使用运行脚本
./tools/sitUpAndreach_cheat/run_statistic_WH_aug.sh \
    --data_path /path/to/your/dataset
```

## 高级使用示例

### 1. 自定义阈值范围

```bash
# 设置更严格的阈值范围
python3 tools/sitUpAndreach_cheat/statistic_WH_aug.py \
    --data_path /path/to/dataset \
    --threshold_min 1.2 \
    --threshold_max 1.8
```

### 2. 处理特定类别

```bash
# 自动从labels.txt读取类别（推荐）
python3 tools/sitUpAndreach_cheat/statistic_WH_aug.py \
    --data_path /path/to/dataset

# 手动指定类别列表
python3 tools/sitUpAndreach_cheat/statistic_WH_aug.py \
    --data_path /path/to/dataset \
    --categories 0_normal 1_fake 2_headErr

# 指定自定义labels文件路径
python3 tools/sitUpAndreach_cheat/statistic_WH_aug.py \
    --data_path /path/to/dataset \
    --labels_file /path/to/custom_labels.txt
```

### 3. 自定义输出目录

```bash
python3 tools/sitUpAndreach_cheat/statistic_WH_aug.py \
    --data_path /path/to/dataset \
    --output_dir /path/to/custom/output
```

### 4. 调整性能参数

```bash
# 使用更多进程加速处理
python3 tools/sitUpAndreach_cheat/statistic_WH_aug.py \
    --data_path /path/to/dataset \
    --max_workers 16

# 适合小内存系统
python3 tools/sitUpAndreach_cheat/statistic_WH_aug.py \
    --data_path /path/to/dataset \
    --max_workers 2
```

## 预设配置示例

### 1. 仰卧起坐数据集

```bash
./tools/sitUpAndreach_cheat/run_statistic_WH_aug.sh --preset situp
```

### 2. 坐位体前屈数据集

```bash
./tools/sitUpAndreach_cheat/run_statistic_WH_aug.sh --preset sitreach
```

### 3. 自定义交互配置

```bash
./tools/sitUpAndreach_cheat/run_statistic_WH_aug.sh --preset custom
```

## 实际应用场景

### 场景1：数据质量检查

```bash
# 检查训练数据集的图片长宽比分布
python3 tools/sitUpAndreach_cheat/statistic_WH_aug.py \
    --data_path /root/share175/sport_datas/sit_up/classify_cheating/train/pose_rgb \
    --threshold_min 1.0 \
    --threshold_max 2.0 \
    --output_dir ./quality_check_results
```

### 场景2：寻找异常样本

```bash
# 使用更严格的阈值找出异常比例的图片
python3 tools/sitUpAndreach_cheat/statistic_WH_aug.py \
    --data_path /path/to/dataset \
    --threshold_min 1.3 \
    --threshold_max 1.7 \
    --output_dir ./outlier_analysis
```

### 场景3：批量处理多个数据集

```bash
#!/bin/bash
# 批处理脚本示例

datasets=(
    "/path/to/dataset1"
    "/path/to/dataset2" 
    "/path/to/dataset3"
)

for dataset in "${datasets[@]}"; do
    echo "处理数据集: $dataset"
    python3 tools/sitUpAndreach_cheat/statistic_WH_aug.py \
        --data_path "$dataset" \
        --output_dir "./batch_results/$(basename $dataset)" \
        --threshold_min 1.0 \
        --threshold_max 2.0
done
```

## 输出文件说明

### 1. 详细统计报告

文件名格式：`detailed_statistics_YYYYMMDD_HHMMSS.txt`

包含内容：
- 总体统计信息
- 各类别详细数据
- 差异化样本比例

### 2. 差异化样本列表

文件名格式：`outlier_samples_threshold_MIN_MAX_YYYYMMDD_HHMMSS.txt`

包含内容：
- 超出阈值的样本路径
- 图片尺寸信息
- 长宽比数值
- 按类别分组显示

## 性能优化建议

### 1. 大型数据集处理

```bash
# 对于包含数万张图片的大型数据集
python3 tools/sitUpAndreach_cheat/statistic_WH_aug.py \
    --data_path /path/to/large/dataset \
    --max_workers 8 \
    --output_dir /fast/ssd/output
```

### 2. 内存受限环境

```bash
# 在内存较小的系统上运行
python3 tools/sitUpAndreach_cheat/statistic_WH_aug.py \
    --data_path /path/to/dataset \
    --max_workers 2
```

### 3. 网络存储优化

```bash
# 先复制到本地再处理
cp -r /network/storage/dataset /tmp/local_dataset
python3 tools/sitUpAndreach_cheat/statistic_WH_aug.py \
    --data_path /tmp/local_dataset \
    --output_dir /local/output
```

## 故障排除

### 1. 权限问题

```bash
# 确保有读取权限
chmod -R 755 /path/to/dataset
```

### 2. Python路径问题

```bash
# 使用绝对路径
/usr/bin/python3 /full/path/to/statistic_WH_aug.py --data_path /path/to/dataset
```

### 3. 依赖问题

```bash
# 安装必要的依赖
pip3 install pillow numpy
```

## 结果分析建议

### 1. 正常范围判断

- 一般图片长宽比在 1.0-2.0 之间较为正常
- 超过 2.5 的图片可能是超宽屏或拼接图
- 小于 0.5 的图片可能是超高屏或裁剪异常

### 2. 差异化样本处理

- 检查差异化样本是否为数据错误
- 考虑是否需要重新裁剪或预处理
- 评估对模型训练的影响

### 3. 数据集平衡性

- 各类别的长宽比分布应该相对均匀
- 避免某个类别的图片尺寸过于集中
- 考虑数据增强来平衡分布
