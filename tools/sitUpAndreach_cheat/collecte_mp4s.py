# -*-coding:utf-8-*-
"""
将文件夹中所有mp4数据，放到1个文件夹中
"""
import shutil
from pathlib import Path

from tools.mThread_Paralleltoolkit import parallel_process, fast_parallel
from tools.ReadTypeFile_Speed import rglob_optimized


def a_scl_Main(path):
    # dst = Path(path).parent / (Path(path).stem + '_videos')
    dst = Path(path).parent / (Path(path).stem + '_videos')

    dst.mkdir(exist_ok=True)

    rglob_mp4 = rglob_optimized(path, '*.mp4', max_workers=4)
    file_info = [(file, dst) for file in rglob_mp4]

    @parallel_process(fast_parallel(16))
    def move_file(file_info):
        file, dst = file_info
        shutil.move(file, dst / file.name)
        print(file.name)

    move_file(file_info)




if __name__ == '__main__':

    dirs = f"/root/share175/sport_datas/sit_up/classify_cheating/ori_vids_fusion/进才中学北校/SitAndUp_freeTest_"
    a_scl_Main(dirs)

    exit()

    for path in list(Path(dirs).glob('*')):
        if path.is_dir():
            a_scl_Main(path)

