# -*-coding:utf-8-*-
"""
统计分类模型数据集中图像的长宽比
增强版本 - 支持多类别统计和差异化样本输出

功能：
1. 遍历数据集的所有类别（0、1、2）
2. 统计每个类别中所有jpg图片的长宽比
3. 计算平均值和中位数
4. 输出差异化样本（W/H超过阈值的样本）到txt文件
"""

import os
import statistics
import argparse
from typing import List, Set, Tuple, Dict, Optional
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor
from pathlib import Path
from dataclasses import dataclass
import numpy as np
from PIL import Image
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


@dataclass
class Config:
    """配置参数类"""
    data_path: str
    threshold_min: float = 1.0
    threshold_max: float = 2.0
    categories: List[str] = None
    output_dir: Optional[str] = None
    max_workers: Optional[int] = None
    labels_file: Optional[str] = None

    def __post_init__(self):
        if self.categories is None:
            self.categories = self._load_categories_from_labels()
        if self.output_dir is None:
            self.output_dir = str(Path(self.data_path).parent / "statistics_output")
        if self.max_workers is None:
            self.max_workers = min(mp.cpu_count(), 8)

    def _load_categories_from_labels(self) -> List[str]:
        """从labels.txt文件中加载类别名称"""
        # 确定labels.txt文件路径
        if self.labels_file:
            labels_path = self.labels_file
        else:
            labels_path = os.path.join(self.data_path, "labels.txt")

        # 尝试读取labels.txt文件
        if os.path.exists(labels_path):
            try:
                with open(labels_path, 'r', encoding='utf-8') as f:
                    categories = [line.strip() for line in f.readlines() if line.strip()]
                logger.info(f"从 {labels_path} 加载了 {len(categories)} 个类别: {categories}")
                return categories
            except Exception as e:
                logger.warning(f"读取labels文件失败 {labels_path}: {e}")
        else:
            logger.warning(f"labels文件不存在: {labels_path}")

        # 如果读取失败，尝试自动检测目录
        return self._auto_detect_categories()

    def _auto_detect_categories(self) -> List[str]:
        """自动检测数据集中的类别目录"""
        if not os.path.exists(self.data_path):
            logger.warning(f"数据集路径不存在: {self.data_path}")
            return ["0", "1", "2"]  # 返回默认值

        try:
            categories = []
            with os.scandir(self.data_path) as entries:
                for entry in entries:
                    if entry.is_dir() and not entry.name.startswith('.'):
                        categories.append(entry.name)

            # 按名称排序
            categories.sort()

            if categories:
                logger.info(f"自动检测到 {len(categories)} 个类别: {categories}")
                return categories
            else:
                logger.warning("未检测到任何类别目录，使用默认值")
                return ["0", "1", "2"]

        except Exception as e:
            logger.error(f"自动检测类别失败: {e}")
            return ["0", "1", "2"]


@dataclass
class AspectRatioStats:
    """长宽比统计数据类"""
    category: str
    mean: float
    median: float
    min_ratio: float
    max_ratio: float
    total_images: int
    outlier_count: int


@dataclass
class ImageInfo:
    """图片信息数据类"""
    path: str
    width: int
    height: int
    aspect_ratio: float
    category: str
    sample_dir: str


def _process_single_directory(args: Tuple[str, Set[str]]) -> List[str]:
    """处理单个目录的全局函数"""
    directory, extensions = args
    local_files = []

    try:
        for dirpath, dirnames, filenames in os.walk(directory):
            dirnames[:] = [d for d in dirnames if not d.startswith(".")]
            for filename in filenames:
                if any(filename.lower().endswith(ext) for ext in extensions):
                    local_files.append(os.path.join(dirpath, filename))
    except (PermissionError, OSError) as e:
        logger.warning(f"无法访问目录 {directory}: {e}")

    return local_files


def find_files_multiprocess(
    root_directory: str, file_extensions: List[str], max_workers: int = None
) -> List[str]:
    """
    多进程超高速文件搜索 - 适合大型目录

    Args:
        root_directory: 根目录路径
        file_extensions: 文件扩展名列表
        max_workers: 最大进程数，默认为CPU核心数

    Returns:
        匹配文件的完整路径列表
    """
    if not os.path.exists(root_directory):
        logger.error(f"目录不存在: {root_directory}")
        return []

    # 预处理扩展名
    extensions = {
        ext.lower() if ext.startswith(".") else f".{ext.lower()}"
        for ext in file_extensions
    }
    max_workers = max_workers or min(mp.cpu_count(), 8)  # 限制最大进程数

    # 获取顶层目录
    try:
        with os.scandir(root_directory) as entries:
            top_dirs = [
                entry.path
                for entry in entries
                if entry.is_dir() and not entry.name.startswith(".")
            ]
            root_files = []

            # 处理根目录文件
            for entry in entries:
                if entry.is_file() and any(
                    entry.name.lower().endswith(ext) for ext in extensions
                ):
                    root_files.append(os.path.join(root_directory, entry.name))
    except (PermissionError, OSError) as e:
        logger.error(f"无法扫描根目录 {root_directory}: {e}")
        return []

    if not top_dirs:
        return root_files

    # 准备多进程参数
    args_list = [(directory, extensions) for directory in top_dirs]
    all_files = root_files[:]

    try:
        with ProcessPoolExecutor(max_workers=max_workers) as executor:
            results = executor.map(_process_single_directory, args_list)
            for result in results:
                all_files.extend(result)
    except Exception as e:
        logger.error(f"多进程处理出错: {e}")

    return all_files


def get_image_dimensions(image_path: str) -> Optional[Tuple[int, int]]:
    """
    获取图片尺寸

    Args:
        image_path: 图片路径

    Returns:
        (width, height) 或 None（如果读取失败）
    """
    try:
        with Image.open(image_path) as img:
            return img.size  # PIL返回(width, height)
    except Exception as e:
        logger.warning(f"无法读取图片 {image_path}: {e}")
        return None


def process_category_images(category_path: str, category_name: str) -> List[ImageInfo]:
    """
    处理单个类别的所有图片

    Args:
        category_path: 类别目录路径
        category_name: 类别名称

    Returns:
        图片信息列表
    """
    logger.info(f"开始处理类别 {category_name}: {category_path}")
    
    # 查找所有jpg文件
    jpg_files = find_files_multiprocess(category_path, [".jpg", ".jpeg"])
    logger.info(f"类别 {category_name} 找到 {len(jpg_files)} 张图片")
    
    image_infos = []
    
    for jpg_path in jpg_files:
        dimensions = get_image_dimensions(jpg_path)
        if dimensions:
            width, height = dimensions
            if height > 0:  # 避免除零错误
                aspect_ratio = width / height
                sample_dir = str(Path(jpg_path).parent)
                
                image_info = ImageInfo(
                    path=jpg_path,
                    width=width,
                    height=height,
                    aspect_ratio=aspect_ratio,
                    category=category_name,
                    sample_dir=sample_dir
                )
                image_infos.append(image_info)
    
    logger.info(f"类别 {category_name} 成功处理 {len(image_infos)} 张图片")
    return image_infos


def calculate_statistics(image_infos: List[ImageInfo]) -> AspectRatioStats:
    """
    计算长宽比统计信息

    Args:
        image_infos: 图片信息列表

    Returns:
        统计结果
    """
    if not image_infos:
        return AspectRatioStats("", 0, 0, 0, 0, 0, 0)
    
    aspect_ratios = [info.aspect_ratio for info in image_infos]
    category = image_infos[0].category
    
    return AspectRatioStats(
        category=category,
        mean=statistics.mean(aspect_ratios),
        median=statistics.median(aspect_ratios),
        min_ratio=min(aspect_ratios),
        max_ratio=max(aspect_ratios),
        total_images=len(image_infos),
        outlier_count=0  # 将在后续计算
    )


def find_outlier_samples(
    image_infos: List[ImageInfo], 
    threshold_min: float = 1.0, 
    threshold_max: float = 2.0
) -> List[ImageInfo]:
    """
    找到差异化样本（长宽比超出阈值范围的样本）

    Args:
        image_infos: 图片信息列表
        threshold_min: 最小阈值
        threshold_max: 最大阈值

    Returns:
        差异化样本列表
    """
    outliers = []
    for info in image_infos:
        if info.aspect_ratio < threshold_min or info.aspect_ratio > threshold_max:
            outliers.append(info)
    
    return outliers


def save_outliers_to_txt(
    outliers: List[ImageInfo], 
    output_path: str, 
    threshold_min: float, 
    threshold_max: float
) -> None:
    """
    保存差异化样本到txt文件

    Args:
        outliers: 差异化样本列表
        output_path: 输出文件路径
        threshold_min: 最小阈值
        threshold_max: 最大阈值
    """
    try:
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(f"差异化样本统计报告\n")
            f.write(f"阈值范围: [{threshold_min:.2f}, {threshold_max:.2f}]\n")
            f.write(f"总计差异化样本: {len(outliers)}\n")
            f.write("=" * 80 + "\n\n")
            
            # 按类别分组
            category_outliers = {}
            for outlier in outliers:
                if outlier.category not in category_outliers:
                    category_outliers[outlier.category] = []
                category_outliers[outlier.category].append(outlier)
            
            for category, cat_outliers in sorted(category_outliers.items()):
                f.write(f"类别 {category} - 差异化样本数量: {len(cat_outliers)}\n")
                f.write("-" * 50 + "\n")
                
                for outlier in cat_outliers:
                    f.write(f"样本目录: {outlier.sample_dir}\n")
                    f.write(f"图片路径: {outlier.path}\n")
                    f.write(f"尺寸: {outlier.width}x{outlier.height}\n")
                    f.write(f"长宽比: {outlier.aspect_ratio:.4f}\n")
                    f.write("\n")
                
                f.write("\n")
        
        logger.info(f"差异化样本已保存到: {output_path}")
    except Exception as e:
        logger.error(f"保存差异化样本文件失败: {e}")


def print_statistics_summary(stats_list: List[AspectRatioStats]) -> None:
    """
    打印统计结果摘要

    Args:
        stats_list: 统计结果列表
    """
    print("\n" + "=" * 80)
    print("数据集长宽比统计结果")
    print("=" * 80)
    
    for stats in stats_list:
        print(f"\n类别 {stats.category}:")
        print(f"  总图片数量: {stats.total_images}")
        print(f"  长宽比平均值: {stats.mean:.4f}")
        print(f"  长宽比中位数: {stats.median:.4f}")
        print(f"  长宽比范围: [{stats.min_ratio:.4f}, {stats.max_ratio:.4f}]")
        print(f"  差异化样本数量: {stats.outlier_count}")
    
    print("\n" + "=" * 80)


def save_detailed_statistics(
    stats_list: List[AspectRatioStats],
    all_image_infos: List[ImageInfo],
    output_path: str
) -> None:
    """
    保存详细统计信息到文件

    Args:
        stats_list: 统计结果列表
        all_image_infos: 所有图片信息
        output_path: 输出文件路径
    """
    try:
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write("数据集长宽比详细统计报告\n")
            f.write("=" * 80 + "\n\n")

            # 总体统计
            total_images = sum(stats.total_images for stats in stats_list)
            total_outliers = sum(stats.outlier_count for stats in stats_list)

            f.write(f"总体统计:\n")
            f.write(f"  总图片数量: {total_images}\n")
            f.write(f"  总差异化样本数量: {total_outliers}\n")
            f.write(f"  差异化样本比例: {total_outliers/total_images*100:.2f}%\n\n")

            # 各类别详细统计
            for stats in stats_list:
                f.write(f"类别 {stats.category} 详细统计:\n")
                f.write(f"  图片数量: {stats.total_images}\n")
                f.write(f"  长宽比平均值: {stats.mean:.6f}\n")
                f.write(f"  长宽比中位数: {stats.median:.6f}\n")
                f.write(f"  长宽比最小值: {stats.min_ratio:.6f}\n")
                f.write(f"  长宽比最大值: {stats.max_ratio:.6f}\n")
                f.write(f"  差异化样本数量: {stats.outlier_count}\n")
                f.write(f"  差异化样本比例: {stats.outlier_count/stats.total_images*100:.2f}%\n")
                f.write("\n")

        logger.info(f"详细统计信息已保存到: {output_path}")
    except Exception as e:
        logger.error(f"保存详细统计信息失败: {e}")


def parse_arguments() -> Config:
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="统计分类模型数据集中图像的长宽比",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )

    parser.add_argument(
        "--data_path",
        type=str,
        default="/media/pyl/WD_Blue_1T/All_proj/pose_rgb",
        help="数据集根路径"
    )

    parser.add_argument(
        "--threshold_min",
        type=float,
        default=1.0,
        help="长宽比最小阈值"
    )

    parser.add_argument(
        "--threshold_max",
        type=float,
        default=2.0,
        help="长宽比最大阈值"
    )

    parser.add_argument(
        "--categories",
        nargs="+",
        default=None,
        help="要处理的类别列表（如果不指定，将从labels.txt文件或自动检测获取）"
    )

    parser.add_argument(
        "--labels_file",
        type=str,
        default=None,
        help="labels文件路径（默认为数据集根路径下的labels.txt）"
    )

    parser.add_argument(
        "--output_dir",
        type=str,
        default=None,
        help="输出目录路径（默认为数据集父目录下的statistics_output）"
    )

    parser.add_argument(
        "--max_workers",
        type=int,
        default=None,
        help="最大进程数（默认为CPU核心数和8的最小值）"
    )

    args = parser.parse_args()

    return Config(
        data_path=args.data_path,
        threshold_min=args.threshold_min,
        threshold_max=args.threshold_max,
        categories=args.categories,
        labels_file=args.labels_file,
        output_dir=args.output_dir,
        max_workers=args.max_workers
    )


def run_statistics(config: Config) -> None:
    """运行统计分析"""
    # 检查数据集路径是否存在
    if not os.path.exists(config.data_path):
        logger.error(f"数据集路径不存在: {config.data_path}")
        return

    # 创建输出目录
    output_dir = Path(config.output_dir)
    output_dir.mkdir(exist_ok=True)

    # 生成带时间戳的文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    outliers_file = output_dir / f"outlier_samples_threshold_{config.threshold_min}_{config.threshold_max}_{timestamp}.txt"
    detailed_stats_file = output_dir / f"detailed_statistics_{timestamp}.txt"

    logger.info(f"开始统计数据集: {config.data_path}")
    logger.info(f"阈值范围: [{config.threshold_min}, {config.threshold_max}]")
    logger.info(f"处理类别: {config.categories}")
    logger.info(f"输出目录: {output_dir}")
    logger.info(f"最大进程数: {config.max_workers}")

    all_stats = []
    all_outliers = []
    all_image_infos = []

    # 处理每个类别
    for category in config.categories:
        category_path = os.path.join(config.data_path, category)

        if not os.path.exists(category_path):
            logger.warning(f"类别目录不存在: {category_path}")
            continue

        # 处理类别图片
        image_infos = process_category_images(category_path, category)

        if not image_infos:
            logger.warning(f"类别 {category} 没有找到有效图片")
            continue

        all_image_infos.extend(image_infos)

        # 计算统计信息
        stats = calculate_statistics(image_infos)

        # 找到差异化样本
        outliers = find_outlier_samples(image_infos, config.threshold_min, config.threshold_max)
        stats.outlier_count = len(outliers)

        all_stats.append(stats)
        all_outliers.extend(outliers)

    # 检查是否有有效数据
    if not all_stats:
        logger.error("没有找到任何有效的类别数据")
        return

    # 打印统计结果
    print_statistics_summary(all_stats)

    # 保存详细统计信息
    save_detailed_statistics(all_stats, all_image_infos, str(detailed_stats_file))

    # 保存差异化样本
    if all_outliers:
        save_outliers_to_txt(all_outliers, str(outliers_file), config.threshold_min, config.threshold_max)
        logger.info(f"发现 {len(all_outliers)} 个差异化样本")
    else:
        logger.info("没有找到差异化样本")

    # 输出处理摘要
    total_images = sum(stats.total_images for stats in all_stats)
    logger.info(f"统计完成! 总计处理 {total_images} 张图片，涉及 {len(all_stats)} 个类别")


def main():
    """主函数"""
    try:
        config = parse_arguments()
        run_statistics(config)
    except KeyboardInterrupt:
        logger.info("用户中断程序执行")
    except Exception as e:
        logger.error(f"程序执行出错: {e}")
        raise


if __name__ == "__main__":
    main()
