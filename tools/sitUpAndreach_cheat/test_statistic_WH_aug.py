#!/usr/bin/env python3
# -*-coding:utf-8-*-
"""
测试脚本 - 验证 statistic_WH_aug.py 的功能
创建测试数据集并运行统计分析
"""

import os
import shutil
import tempfile
from pathlib import Path
from PIL import Image
import numpy as np
import pickle
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def create_test_image(width: int, height: int, color: tuple = (255, 0, 0)) -> Image.Image:
    """
    创建测试图片
    
    Args:
        width: 图片宽度
        height: 图片高度
        color: RGB颜色值
    
    Returns:
        PIL Image对象
    """
    # 创建纯色图片
    img = Image.new('RGB', (width, height), color)
    return img


def create_test_video(output_path: str, width: int = 640, height: int = 480, fps: int = 30, duration: int = 1):
    """
    创建测试视频文件（简化版本，创建空文件）

    Args:
        output_path: 输出视频路径
        width: 视频宽度
        height: 视频高度
        fps: 帧率
        duration: 持续时间（秒）
    """
    # 创建一个空的avi文件作为占位符
    # 在实际应用中，这个文件不会被统计脚本使用
    with open(output_path, 'wb') as f:
        # 写入一些基本的AVI文件头信息（简化版）
        f.write(b'RIFF')  # RIFF标识
        f.write((36).to_bytes(4, 'little'))  # 文件大小
        f.write(b'AVI ')  # AVI标识
        f.write(b'LIST')  # LIST标识
        f.write((28).to_bytes(4, 'little'))  # LIST大小
        f.write(b'hdrlavi ')  # 头部信息
        f.write((width).to_bytes(4, 'little'))  # 宽度
        f.write((height).to_bytes(4, 'little'))  # 高度
        f.write((fps).to_bytes(4, 'little'))  # 帧率


def create_test_pkl(output_path: str):
    """
    创建测试pkl文件
    
    Args:
        output_path: 输出pkl文件路径
    """
    # 创建测试数据
    test_data = {
        'sample_id': 'test_sample',
        'keypoints': np.random.rand(17, 3).tolist(),  # 17个关键点，每个3维
        'bbox': [100, 100, 200, 200],
        'confidence': 0.95,
        'timestamp': '2024-01-01 12:00:00'
    }
    
    with open(output_path, 'wb') as f:
        pickle.dump(test_data, f)


def create_test_dataset(base_dir: str) -> str:
    """
    创建测试数据集

    Args:
        base_dir: 基础目录路径

    Returns:
        测试数据集路径
    """
    dataset_path = os.path.join(base_dir, "test_dataset")
    os.makedirs(dataset_path, exist_ok=True)

    # 创建labels.txt文件
    labels_content = """0_normal
1_fake
2_headErr"""

    labels_path = os.path.join(dataset_path, "labels.txt")
    with open(labels_path, 'w', encoding='utf-8') as f:
        f.write(labels_content)

    logger.info(f"创建labels.txt文件: {labels_path}")

    # 定义测试样本配置
    test_samples = [
        # 类别0_normal - 正常比例图片
        {
            'category': '0_normal',
            'samples': [
                {'name': 'sample_001', 'images': [(640, 480), (800, 600), (1024, 768)]},  # 4:3比例
                {'name': 'sample_002', 'images': [(1920, 1080), (1280, 720), (854, 480)]},  # 16:9比例
                {'name': 'sample_003', 'images': [(1200, 800), (900, 600), (600, 400)]},  # 3:2比例
            ]
        },
        # 类别1_fake - 包含一些异常比例
        {
            'category': '1_fake',
            'samples': [
                {'name': 'sample_001', 'images': [(640, 480), (800, 600), (1024, 768)]},
                {'name': 'sample_002', 'images': [(2560, 1080), (1920, 800), (1600, 600)]},  # 超宽比例
                {'name': 'sample_003', 'images': [(480, 640), (600, 800), (768, 1024)]},  # 竖屏比例
            ]
        },
        # 类别2_headErr - 更多异常比例
        {
            'category': '2_headErr',
            'samples': [
                {'name': 'sample_001', 'images': [(3440, 1440), (2560, 1080), (1920, 800)]},  # 超宽屏
                {'name': 'sample_002', 'images': [(480, 1920), (600, 2400), (720, 2880)]},  # 超高屏
                {'name': 'sample_003', 'images': [(1000, 1000), (800, 800), (600, 600)]},  # 正方形
            ]
        }
    ]
    
    logger.info(f"开始创建测试数据集: {dataset_path}")
    
    # 创建测试数据
    for category_info in test_samples:
        category = category_info['category']
        category_path = os.path.join(dataset_path, category)
        os.makedirs(category_path, exist_ok=True)
        
        logger.info(f"创建类别 {category}")
        
        for sample_info in category_info['samples']:
            sample_name = sample_info['name']
            sample_path = os.path.join(category_path, sample_name)
            os.makedirs(sample_path, exist_ok=True)
            
            # 创建图片文件
            for i, (width, height) in enumerate(sample_info['images']):
                img_name = f"image_{i+1}.jpg"
                img_path = os.path.join(sample_path, img_name)
                
                # 根据类别使用不同颜色
                colors = {
                    '0': (255, 0, 0),    # 红色
                    '1': (0, 255, 0),    # 绿色
                    '2': (0, 0, 255),    # 蓝色
                }
                color = colors.get(category, (128, 128, 128))
                
                img = create_test_image(width, height, color)
                img.save(img_path, 'JPEG', quality=95)
            
            # 创建pkl文件
            pkl_path = os.path.join(sample_path, "data.pkl")
            create_test_pkl(pkl_path)
            
            # 创建avi文件
            avi_path = os.path.join(sample_path, "video.avi")
            create_test_video(avi_path, 640, 480, 30, 1)
            
            logger.info(f"  创建样本: {sample_name}")
    
    logger.info(f"测试数据集创建完成: {dataset_path}")
    return dataset_path


def run_test():
    """运行测试"""
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        logger.info(f"使用临时目录: {temp_dir}")
        
        # 创建测试数据集
        test_dataset_path = create_test_dataset(temp_dir)
        
        # 导入并运行统计脚本
        try:
            # 动态导入模块
            import sys
            sys.path.insert(0, str(Path(__file__).parent))
            
            from statistic_WH_aug import Config, run_statistics
            
            # 创建配置（不指定categories，让它从labels.txt自动读取）
            config = Config(
                data_path=test_dataset_path,
                threshold_min=1.0,
                threshold_max=2.0,
                categories=None,  # 让它自动从labels.txt读取
                output_dir=os.path.join(temp_dir, "test_output"),
                max_workers=2
            )
            
            logger.info("开始运行统计分析...")
            
            # 运行统计分析
            run_statistics(config)
            
            logger.info("测试完成!")
            
            # 检查输出文件
            output_dir = Path(config.output_dir)
            if output_dir.exists():
                output_files = list(output_dir.glob("*.txt"))
                logger.info(f"生成的输出文件: {[f.name for f in output_files]}")
                
                # 显示部分输出内容
                for output_file in output_files:
                    logger.info(f"\n--- {output_file.name} 内容预览 ---")
                    with open(output_file, 'r', encoding='utf-8') as f:
                        lines = f.readlines()[:20]  # 只显示前20行
                        for line in lines:
                            print(line.rstrip())
                    if len(lines) >= 20:
                        print("... (更多内容请查看完整文件)")
                    print()
            
        except ImportError as e:
            logger.error(f"无法导入统计模块: {e}")
        except Exception as e:
            logger.error(f"测试运行出错: {e}")
            raise


def main():
    """主函数"""
    logger.info("开始测试 statistic_WH_aug.py")
    
    try:
        run_test()
        logger.info("所有测试通过!")
    except Exception as e:
        logger.error(f"测试失败: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
