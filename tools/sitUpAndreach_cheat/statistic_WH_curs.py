# -*-coding:utf-8-*-
"""
统计分类模型数据集中图片的长宽比
功能：
1. 遍历数据集的所有类别（0、1、2）
2. 读取每个样本中的jpg图片，获取宽度和高度
3. 计算长宽比（W/H）
4. 统计每个类别的平均值和中位数
5. 输出差异化样本到txt文档

使用方法：
python statistic_WH_curs.py [--data_path PATH] [--min_threshold 1.0] [--max_threshold 2.0] [--output OUTPUT.txt]
"""
import os
import cv2
import numpy as np
import argparse
from typing import List, Dict, Tuple, Set, Optional
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor
from pathlib import Path
import statistics
from collections import defaultdict
import time
import json


def _process_single_directory(args: Tuple[str, Set[str]]) -> List[str]:
    """处理单个目录的全局函数"""
    directory, extensions = args
    local_files = []

    for dirpath, dirnames, filenames in os.walk(directory):
        dirnames[:] = [d for d in dirnames if not d.startswith(".")]
        for filename in filenames:
            if any(filename.lower().endswith(ext) for ext in extensions):
                local_files.append(os.path.join(dirpath, filename))

    return local_files


def find_files_multiprocess(
    root_directory: str, file_extensions: List[str], max_workers: int = None
) -> List[str]:
    """
    多进程超高速文件搜索 - 适合大型目录

    Args:
        root_directory: 根目录路径
        file_extensions: 文件扩展名列表
        max_workers: 最大进程数，默认为CPU核心数

    Returns:
        匹配文件的完整路径列表
    """
    if not os.path.exists(root_directory):
        return []

    # 预处理扩展名
    extensions = {
        ext.lower() if ext.startswith(".") else f".{ext.lower()}"
        for ext in file_extensions
    }
    max_workers = max_workers or min(mp.cpu_count(), 8)  # 限制最大进程数

    # 获取顶层目录
    with os.scandir(root_directory) as entries:
        top_dirs = [
            entry.path
            for entry in entries
            if entry.is_dir() and not entry.name.startswith(".")
        ]
        root_files = []

        # 处理根目录文件
        for entry in entries:
            if entry.is_file() and any(
                entry.name.lower().endswith(ext) for ext in extensions
            ):
                root_files.append(os.path.join(root_directory, entry.name))

    if not top_dirs:
        return root_files

    # 准备多进程参数
    args_list = [(directory, extensions) for directory in top_dirs]
    all_files = root_files[:]

    with ProcessPoolExecutor(max_workers=max_workers) as executor:
        results = executor.map(_process_single_directory, args_list)
        for result in results:
            all_files.extend(result)

    return all_files


def get_image_dimensions(image_path: str) -> Tuple[int, int]:
    """
    获取图片的宽度和高度

    Args:
        image_path: 图片路径

    Returns:
        (width, height) 图片尺寸，失败时返回 (0, 0)
    """
    try:
        img = cv2.imread(image_path)
        if img is not None:
            height, width = img.shape[:2]
            return width, height
        else:
            return 0, 0
    except Exception as e:
        print(f"读取图片失败 {image_path}: {e}")
        return 0, 0


def process_category_images(
    category_path: str, category_name: str, show_progress: bool = True
) -> Dict[str, any]:
    """
    处理单个类别的所有图片

    Args:
        category_path: 类别目录路径
        category_name: 类别名称
        show_progress: 是否显示进度信息

    Returns:
        包含统计信息的字典
    """
    if show_progress:
        print(f"处理类别 {category_name}: {category_path}")

    # 查找所有jpg图片
    jpg_files = find_files_multiprocess(category_path, [".jpg", ".jpeg"])

    if not jpg_files:
        if show_progress:
            print(f"类别 {category_name} 中未找到jpg图片")
        return {
            "category": category_name,
            "total_images": 0,
            "failed_images": 0,
            "aspect_ratios": [],
            "mean_ratio": 0,
            "median_ratio": 0,
            "min_ratio": 0,
            "max_ratio": 0,
            "std_ratio": 0,
            "samples_info": {},
        }

    aspect_ratios = []
    samples_info = defaultdict(list)  # 记录每个样本的信息
    failed_images = []

    if show_progress:
        print(f"找到 {len(jpg_files)} 张图片，开始计算长宽比...")

    # 使用多线程处理图片尺寸获取
    with ThreadPoolExecutor(max_workers=min(mp.cpu_count(), 16)) as executor:
        # 提交所有任务
        future_to_path = {
            executor.submit(get_image_dimensions, img_path): img_path
            for img_path in jpg_files
        }

        # 收集结果
        processed_count = 0
        for future in future_to_path:
            img_path = future_to_path[future]
            try:
                width, height = future.result()
                if width > 0 and height > 0:
                    ratio = width / height
                    aspect_ratios.append(ratio)

                    # 记录样本信息
                    sample_dir = Path(img_path).parent.name
                    samples_info[sample_dir].append(
                        {
                            "image": Path(img_path).name,
                            "width": width,
                            "height": height,
                            "ratio": ratio,
                            "path": img_path,
                        }
                    )
                else:
                    failed_images.append(img_path)
            except Exception as e:
                if show_progress:
                    print(f"处理图片出错 {img_path}: {e}")
                failed_images.append(img_path)

            processed_count += 1
            if show_progress and processed_count % 100 == 0:
                print(f"  已处理: {processed_count}/{len(jpg_files)} 张图片")

    if failed_images and show_progress:
        print(f"类别 {category_name} 中有 {len(failed_images)} 张图片读取失败")

    # 计算统计信息
    if aspect_ratios:
        mean_ratio = statistics.mean(aspect_ratios)
        median_ratio = statistics.median(aspect_ratios)
        min_ratio = min(aspect_ratios)
        max_ratio = max(aspect_ratios)
        std_ratio = statistics.stdev(aspect_ratios) if len(aspect_ratios) > 1 else 0
    else:
        mean_ratio = median_ratio = min_ratio = max_ratio = std_ratio = 0

    return {
        "category": category_name,
        "total_images": len(aspect_ratios),
        "failed_images": len(failed_images),
        "aspect_ratios": aspect_ratios,
        "mean_ratio": mean_ratio,
        "median_ratio": median_ratio,
        "min_ratio": min_ratio,
        "max_ratio": max_ratio,
        "std_ratio": std_ratio,
        "samples_info": dict(samples_info),
    }


def find_outlier_samples(
    samples_info: Dict[str, List[Dict]],
    min_threshold: float = 1.0,
    max_threshold: float = 2.0,
) -> List[Dict[str, any]]:
    """
    找出长宽比超出阈值范围的样本

    Args:
        samples_info: 样本信息字典
        min_threshold: 最小阈值
        max_threshold: 最大阈值

    Returns:
        异常样本信息列表
    """
    outlier_samples = []

    for sample_name, images_info in samples_info.items():
        for img_info in images_info:
            ratio = img_info["ratio"]
            if ratio < min_threshold or ratio > max_threshold:
                outlier_samples.append(
                    {
                        "sample": sample_name,
                        "image": img_info["image"],
                        "width": img_info["width"],
                        "height": img_info["height"],
                        "ratio": ratio,
                        "path": img_info["path"],
                        "type": "小于阈值" if ratio < min_threshold else "大于阈值",
                    }
                )

    return outlier_samples


def save_outliers_to_file(
    outliers_by_category: Dict[str, List[Dict]],
    min_threshold: float,
    max_threshold: float,
    output_path: str = "outlier_samples.txt",
):
    """
    将异常样本保存到txt文件

    Args:
        outliers_by_category: 按类别分组的异常样本
        min_threshold: 最小阈值
        max_threshold: 最大阈值
        output_path: 输出文件路径
    """
    with open(output_path, "w", encoding="utf-8") as f:
        f.write("数据集图片长宽比异常样本统计报告\n")
        f.write("=" * 60 + "\n")
        f.write(f"阈值范围: [{min_threshold:.2f}, {max_threshold:.2f}]\n")
        f.write(f"生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write("=" * 60 + "\n\n")

        total_outliers = sum(
            len(outliers) for outliers in outliers_by_category.values()
        )
        f.write(f"总异常样本数: {total_outliers}\n\n")

        for category, outliers in outliers_by_category.items():
            f.write(f"类别 {category}:\n")
            f.write(f"异常样本数量: {len(outliers)}\n")
            f.write("-" * 40 + "\n")

            if outliers:
                for outlier in outliers:
                    f.write(f"  样本: {outlier['sample']}\n")
                    f.write(f"  图片: {outlier['image']}\n")
                    f.write(f"  尺寸: {outlier['width']}x{outlier['height']}\n")
                    f.write(f"  长宽比: {outlier['ratio']:.4f} ({outlier['type']})\n")
                    f.write(f"  路径: {outlier['path']}\n")
                    f.write("\n")
            else:
                f.write("  无异常样本\n\n")
            f.write("\n")

    print(f"异常样本报告已保存到: {output_path}")


def save_statistics_to_json(
    all_results: Dict[str, Dict], output_path: str = "statistics_summary.json"
):
    """
    将统计结果保存为JSON格式

    Args:
        all_results: 所有类别的统计结果
        output_path: 输出文件路径
    """
    # 准备要保存的数据，排除aspect_ratios数组（太大）
    export_data = {}
    for category, result in all_results.items():
        export_data[category] = {
            k: v
            for k, v in result.items()
            if k not in ["aspect_ratios", "samples_info"]
        }
        # 添加一些额外的统计信息
        export_data[category]["sample_count"] = len(result["samples_info"])

    with open(output_path, "w", encoding="utf-8") as f:
        json.dump(export_data, f, ensure_ascii=False, indent=2)

    print(f"统计摘要已保存到: {output_path}")


def parse_arguments() -> argparse.Namespace:
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="统计分类模型数据集中图片的长宽比",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python statistic_WH_curs.py
  python statistic_WH_curs.py --data_path /path/to/dataset
  python statistic_WH_curs.py --min_threshold 0.8 --max_threshold 2.5
  python statistic_WH_curs.py --output my_outliers.txt --json_output my_stats.json
        """,
    )

    parser.add_argument(
        "--data_path",
        type=str,
        default="/media/pyl/WD_Blue_1T/All_proj/pose_rgb",
        help="数据集根目录路径",
    )

    parser.add_argument(
        "--min_threshold", type=float, default=1.0, help="长宽比最小阈值 (默认: 1.0)"
    )

    parser.add_argument(
        "--max_threshold", type=float, default=2.0, help="长宽比最大阈值 (默认: 2.0)"
    )

    parser.add_argument(
        "--output",
        type=str,
        default="outlier_samples.txt",
        help="异常样本输出文件路径 (默认: outlier_samples.txt)",
    )

    parser.add_argument(
        "--json_output",
        type=str,
        default="statistics_summary.json",
        help="统计摘要JSON输出文件路径 (默认: statistics_summary.json)",
    )

    parser.add_argument(
        "--max_workers",
        type=int,
        default=None,
        help="最大并行工作进程数 (默认: 自动检测)",
    )

    parser.add_argument("--quiet", action="store_true", help="静默模式，减少输出信息")

    return parser.parse_args()


def main():
    """主函数"""
    args = parse_arguments()

    # 验证参数
    if args.min_threshold >= args.max_threshold:
        print("错误: min_threshold 必须小于 max_threshold")
        return

    if not os.path.exists(args.data_path):
        print(f"错误: 数据集路径不存在: {args.data_path}")
        return

    show_progress = not args.quiet

    if show_progress:
        print(f"开始统计数据集: {args.data_path}")
        print(f"长宽比阈值范围: [{args.min_threshold}, {args.max_threshold}]")
        print(f"最大并行进程数: {args.max_workers or '自动'}")
        print("=" * 60)

    start_time = time.time()

    # 查找所有类别目录 (0, 1, 2)
    category_dirs = []
    if os.path.exists(args.data_path):
        with os.scandir(args.data_path) as entries:
            for entry in entries:
                if entry.is_dir() and entry.name.isdigit():
                    category_dirs.append((entry.path, entry.name))

    if not category_dirs:
        print(f"在路径 {args.data_path} 中未找到类别目录")
        return

    category_dirs.sort(key=lambda x: int(x[1]))  # 按类别编号排序

    if show_progress:
        print(
            f"找到 {len(category_dirs)} 个类别目录: {[name for _, name in category_dirs]}"
        )

    # 统计结果
    all_results = {}
    outliers_by_category = {}

    # 处理每个类别
    for i, (category_path, category_name) in enumerate(category_dirs):
        if show_progress:
            print(f"\n[{i+1}/{len(category_dirs)}] 处理类别 {category_name}...")

        result = process_category_images(
            category_path, category_name, show_progress=show_progress
        )
        all_results[category_name] = result

        # 查找异常样本
        outliers = find_outlier_samples(
            result["samples_info"], args.min_threshold, args.max_threshold
        )
        outliers_by_category[category_name] = outliers

        if show_progress:
            print(
                f"类别 {category_name} 处理完成 - 图片: {result['total_images']}, 异常: {len(outliers)}"
            )

    # 输出统计结果
    if show_progress:
        print("\n" + "=" * 60)
        print("统计结果汇总")
        print("=" * 60)

        for category_name, result in all_results.items():
            print(f"\n类别 {category_name}:")
            print(f"  总图片数: {result['total_images']}")
            if result["failed_images"] > 0:
                print(f"  失败图片数: {result['failed_images']}")
            if result["total_images"] > 0:
                print(f"  平均长宽比: {result['mean_ratio']:.4f}")
                print(f"  中位数长宽比: {result['median_ratio']:.4f}")
                print(f"  最小长宽比: {result['min_ratio']:.4f}")
                print(f"  最大长宽比: {result['max_ratio']:.4f}")
                print(f"  标准差: {result['std_ratio']:.4f}")

            outlier_count = len(outliers_by_category[category_name])
            print(f"  异常样本数: {outlier_count}")

    # 保存异常样本到文件
    if any(outliers_by_category.values()):
        save_outliers_to_file(
            outliers_by_category, args.min_threshold, args.max_threshold, args.output
        )
    else:
        if show_progress:
            print("\n未发现长宽比异常的样本")

    # 保存统计摘要
    save_statistics_to_json(all_results, args.json_output)

    end_time = time.time()
    if show_progress:
        print(f"\n总处理时间: {end_time - start_time:.2f}秒")


if __name__ == "__main__":
    main()
