# -*-coding:utf-8-*-
"""
created by @Moss 20250627
基于 模态模型的数据采集 后细分
单人仰卧起坐 促膝分类         # 用于裁剪后的图像2次裁剪
"""
import os
import pickle
import cv2
from PIL import Image
from pathlib import Path
import numpy as np
from torchvision.transforms import Compose, Normalize, Resize, ToTensor, CenterCrop
import onnxruntime as ort


preprocess_knee = Compose([
    Resize(192, max_size=256),
    CenterCrop(256),
    ToTensor(),
    Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])             # RGB

])

preprocess_head = Compose([Resize(224, ),
     ToT<PERSON>or(),
     Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])])  # [0.5, 0.5, 0.5], [0.5, 0.5, 0.5]

preprocess_lie = Compose([
    <PERSON>size(196, max_size=224),
    CenterCrop(224),
    <PERSON><PERSON><PERSON><PERSON>(),
    Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])             # RGB

])

preprocess_leg = preprocess_lie


def cut_box(image_pth, xyxy):
    img = cv2.imread(str(image_pth))  # 读取原图片
    h1, w1 = img.shape[:-1]

    x_min = int(float(xyxy[0]))  # - 1.5*r
    y_min = int(float(xyxy[1]))  # - 1.5*r
    x_max = int(float(xyxy[2]))  # + 1.5*r
    y_max = int(float(xyxy[3]))  # + 1.5*r

    crop_y = 1080 - h1
    crop_x = x_max - w1

    x1 = x_min - crop_x
    y1 = y_min - crop_y
    x2 = x_max - crop_x
    y2 = y_max - crop_y

    obj_img = img[int(y1):int(y2), int(x1):int(x2)]  # cv2裁剪出目标框中的图片

    return obj_img




def readpkl_cutImage(pkl_pth):

    with open(pkl_pth, 'rb') as fp:
        data = pickle.load(fp)

        image_end = Path(pkl_pth).parent / (Path(pkl_pth).stem + f'-{data["end"]}.jpg')
        xyxy_end = data['pred_skpts'][-1][0][:4]

        image_start = Path(pkl_pth).parent / (Path(pkl_pth).stem + f'-{data["start"]}.jpg')
        xyxy_start = data['pred_skpts'][0][0][:4]
        lie_body_left = data['pred_skpts'][0][0][[51, 54]].mean() > 1920/2

        for f in Path(pkl_pth).parent.rglob('*.jpg'):
            if f not in {image_start, image_end}:
                image_mid = f
                mid = int(image_mid.stem.split('-')[-1])
                idx = data["seq_vid"].index(mid)
                xyxy_mid = data['pred_skpts'][idx][0][:4]
                body_left = data['pred_skpts'][idx][0][[51, 54]].mean() > 1920/2



    obj_img0 = cut_box(image_start, xyxy_start)
    obj_img1 = cut_box(image_mid, xyxy_mid)
    obj_img2 = cut_box(image_end, xyxy_end)


    return obj_img0, lie_body_left, obj_img1, body_left, obj_img2


def head_foul_cut(image, body_left):
    h, w = image.shape[:-1]
    img_cv = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    img = Image.fromarray(img_cv)
    if body_left:
        box = (0, 0, h, h) if w >= h else (0, 0, w, h)    # box元组内分别是 所处理图片中想要截取的部分的 左上角和右下角的坐标
    else:
        box = (w - h, 0, w, h) if w >= h else (0, 0, w, h)

    img_cuted = img.crop(box)  # print('正在截取左半张图...')

    return img_cuted


def lie_foul_cut(image, body_left):
    h, w = image.shape[:-1]
    img_cv = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    img = Image.fromarray(img_cv)
    if body_left:
        box = (0, 0, h*1.1, h) if w >= h else (0, 0, w, h)    # box元组内分别是 所处理图片中想要截取的部分的 左上角和右下角的坐标
    else:
        box = (w-(1.1*h), 0, w, h) if w >= h else (0, 0, w, h)

    img_cuted = img.crop(box)  # print('正在截取左半张图...')

    return img_cuted


def leg_foul_cut(image, body_left):
    h, w = image.shape[:-1]
    img_cv = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    img = Image.fromarray(img_cv)
    if body_left:
        box = (0, 0, w*0.65, h)    # box元组内分别是 所处理图片中想要截取的部分的 左上角和右下角的坐标
    else:
        box = (w*0.35, 0, w, h)

    img_cuted = img.crop(box)  # print('正在截取左半张图...')

    return img_cuted


def classify_head_foul_onnx(session_head, image):
    image = preprocess_head(image).unsqueeze(0)      # batch +

    input_data = {session_head.get_inputs()[0].name: image.numpy()}
    outputs = session_head.run(None, input_data)
    pred_indx = np.argmax(outputs[0])       # 0: ， 1: 抱头

    return pred_indx


def classify_lie_foul_onnx(session_head, image):
    try:
        image = preprocess_lie(image).unsqueeze(0)      # batch +
    except:
        return 0

    input_data = {session_head.get_inputs()[0].name: image.numpy()}
    outputs = session_head.run(None, input_data)
    pred_indx = np.argmax(outputs[0])       # 0: ， 1: 躺平

    return pred_indx


def classify_leg_foul_onnx(session_head, image):
    image = preprocess_lie(image).unsqueeze(0)      # batch +

    input_data = {session_head.get_inputs()[0].name: image.numpy()}
    outputs = session_head.run(None, input_data)
    pred_indx = np.argmax(outputs[0])       # 0: 腿弯曲  ， 1: 腿伸直
    pred_indx = 1 if pred_indx == 0 else 0      # 切换为 0: 腿伸直

    return pred_indx


def classify_knee_foul_onnx(session_knee, image):
    try:
        img_cv = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    except:
        return 0
    image = Image.fromarray(img_cv)
    image = preprocess_knee(image).unsqueeze(0)      # batch +

    input_data = {session_knee.get_inputs()[0].name: image.numpy()}
    outputs = session_knee.run(None, input_data)
    pred_indx = np.argmax(outputs[0])       # 0: 未促膝， 1: 促膝

    return pred_indx


def make_label(pred_indx, pkl_pth, str_lab):
    if pred_indx == 0:
        label_true = pkl_pth.parent / str_lab
        label_true.touch()
        print('\n', pkl_pth.parent.name, str_lab)


def main(data_pth, session_knee, session_head, session_lie, session_leg):
    all_pkls = list(Path(data_pth).rglob('*.pkl'))
    num = len(all_pkls)
    print(f"all pkl num is {num}")
    for pkl_pth in all_pkls:
        try:
            obj_img0, lie_body_left, obj_img1, body_left, obj_img2 = readpkl_cutImage(pkl_pth)
            # 促膝
            pred_indx_knee = classify_knee_foul_onnx(session_knee, obj_img2)
            make_label(pred_indx_knee, pkl_pth, str_lab='knee_false_im2')

            # 抱头
            img_head = head_foul_cut(obj_img1, body_left)
            pred_indx_head = classify_head_foul_onnx(session_head, img_head)
            make_label(pred_indx_head, pkl_pth, str_lab='head_false_im1')

            # 躺平
            img_lie = lie_foul_cut(obj_img0, lie_body_left)
            pred_indx_lie = classify_lie_foul_onnx(session_lie, img_lie)
            make_label(pred_indx_lie, pkl_pth, str_lab='Lie_false_im0')



            # 单手抱头
            img_head0 = head_foul_cut(obj_img0, body_left)
            pred_indx_head0 = classify_head_foul_onnx(session_head, img_head0)
            make_label(pred_indx_head0, pkl_pth, str_lab='SingleHead_im0')


            # 腿弯曲 is True
            img_leg = leg_foul_cut(obj_img0, lie_body_left)
            pred_indx_leg = classify_leg_foul_onnx(session_leg, img_leg)
            make_label(pred_indx_leg, pkl_pth, str_lab='Leg_false_im0')
        except:
            pass

        print(num, end=',')


        num -= 1


    return


def knee_move(ori_pth):
    pth = Path(ori_pth)
    for knee in list(pth.rglob('knee_false*')):
        tar_dir = knee.parent
        tar_dir.rename(str(tar_dir) + '_kneeFalse')
        print(tar_dir.stem)


def head_move(ori_pth):
    pth = Path(ori_pth)
    for knee in list(pth.rglob('head_false*')):
        tar_dir = knee.parent
        tar_dir.rename(str(tar_dir) + '_headFalse')
        print(tar_dir.stem)


def model_loader():
    providers = ['CUDAExecutionProvider','CPUExecutionProvider']        # 'CUDAExecutionProvider',

    knee_onnx_pth = '/root/share175/sport_models/sit_up/detect_knee_foul/SitUp_knee_foul__20250606_V11_256_256.onnx'
    session_knee = ort.InferenceSession(knee_onnx_pth, providers=providers)

    head_onnx_pth = '/root/share175/sport_models/sit_up/detect_head_foul/SitUp_head_foul__20250527_V16_224_224.onnx'
    session_head = ort.InferenceSession(head_onnx_pth, providers=providers)  # 'CUDAExecutionProvider',


    lie_onnx_pth = '/root/share175/sport_models/sit_up/detect_lie_foul/SitUp_lie_foul__20250516_V12_224_224.onnx'
    session_lie = ort.InferenceSession(lie_onnx_pth, providers=providers)  # 'CUDAExecutionProvider',

    leg_onnx_pth = '/root/share175/sport_models/sit_up/detect_leg_foul/SitUp_leg_foul__20240604_V9_224_224.onnx'
    session_leg = ort.InferenceSession(leg_onnx_pth, providers=providers)  # 'CUDAExecutionProvider',

    return session_knee, session_head, session_lie, session_leg





if __name__ == '__main__':
    # ori_pth = "/root/share175/sport_datas/sit_up/classify_cheating/ori_vids_fusion/Hunan_changshataiyushiyanxiaoxue/2024_sampled_Matched/1_fake_sample"
    # knee_move(ori_pth)
    # head_move(ori_pth)
    # exit()


    # data_pth = '/root/share175/sport_datas/sit_up/classify_cheating/ori_vids_fusion/1_8_荆州经济技术开发区实验中学/1234/2025_2_sampled_Matched/0_normal_sample'
    data_0 = '/root/share175/sport_datas/sit_up/classify_cheating/ori_vids_fusion/Zhejiang_zhejiangkejixueyuan/1_sampled_sample_Matched/0_normal'

    session_knee, session_head, session_lie, session_leg = model_loader()



    # 这是单文件夹
    main(data_0, session_knee, session_head, session_lie, session_leg)

    # "/root/share175/sport_trains/action_recongnition/base_skeleton/dataGeter_yoloPose/tools/sitUpAndreach_cheat/kneefoul_Classify.py"
    # 这是针对 多文件夹，对文件夹中的 ‘*_sample’子文件夹进行处理
    # for data_pth in list(Path(data_0).glob('*_sample')):
    #     if data_pth.is_dir():
    #         print(data_pth)
    #
    #         main(data_pth, session_knee, session_head, session_lie, session_leg)
    #
    # exit()


