#!/bin/bash
# -*-coding:utf-8-*-
# 运行脚本 - 数据集长宽比统计工具
# 提供常用的运行配置和示例

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PYTHON_SCRIPT="$SCRIPT_DIR/statistic_WH_aug.py"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "数据集长宽比统计工具 - 运行脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help              显示此帮助信息"
    echo "  -t, --test              运行测试模式"
    echo "  -d, --data_path PATH    指定数据集路径"
    echo "  -o, --output_dir PATH   指定输出目录"
    echo "  --threshold_min FLOAT   设置最小阈值 (默认: 1.0)"
    echo "  --threshold_max FLOAT   设置最大阈值 (默认: 2.0)"
    echo "  --categories LIST       指定类别列表 (默认: 0 1 2)"
    echo "  --max_workers INT       设置最大进程数"
    echo ""
    echo "预设配置:"
    echo "  $0 --preset situp       仰卧起坐数据集配置"
    echo "  $0 --preset sitreach    坐位体前屈数据集配置"
    echo "  $0 --preset custom      自定义配置"
    echo ""
    echo "示例:"
    echo "  $0 --data_path /path/to/dataset"
    echo "  $0 --data_path /path/to/dataset --threshold_min 1.2 --threshold_max 1.8"
    echo "  $0 --preset situp"
    echo ""
}

# 检查Python脚本是否存在
check_script() {
    if [ ! -f "$PYTHON_SCRIPT" ]; then
        print_error "Python脚本不存在: $PYTHON_SCRIPT"
        exit 1
    fi
}

# 运行测试
run_test() {
    print_info "运行测试模式..."
    
    local test_script="$SCRIPT_DIR/test_statistic_WH_aug.py"
    if [ ! -f "$test_script" ]; then
        print_error "测试脚本不存在: $test_script"
        exit 1
    fi
    
    python3 "$test_script"
    if [ $? -eq 0 ]; then
        print_success "测试完成!"
    else
        print_error "测试失败!"
        exit 1
    fi
}

# 预设配置
run_preset() {
    local preset="$1"
    
    case "$preset" in
        "situp")
            print_info "使用仰卧起坐数据集预设配置..."
            python3 "$PYTHON_SCRIPT" \
                --data_path "/root/share175/sport_test/sit_up/classify_cheat/pose_rgb" \
                --threshold_min 1.0 \
                --threshold_max 2.0 \
                --max_workers 8
            ;;
        "sitreach")
            print_info "使用坐位体前屈数据集预设配置..."
            python3 "$PYTHON_SCRIPT" \
                --data_path "/root/share175/sport_test/sit_reach/classify_cheat/pose_rgb" \
                --threshold_min 1.0 \
                --threshold_max 2.0 \
                --max_workers 8
            ;;
        "custom")
            print_info "使用自定义配置..."
            echo "请输入数据集路径:"
            read -r data_path
            echo "请输入最小阈值 (默认: 1.0):"
            read -r threshold_min
            threshold_min=${threshold_min:-1.0}
            echo "请输入最大阈值 (默认: 2.0):"
            read -r threshold_max
            threshold_max=${threshold_max:-2.0}
            echo "是否使用自定义类别列表? (y/N, 默认从labels.txt读取):"
            read -r use_custom_categories

            if [[ "$use_custom_categories" =~ ^[Yy]$ ]]; then
                echo "请输入类别列表，用空格分隔:"
                read -r categories
                python3 "$PYTHON_SCRIPT" \
                    --data_path "$data_path" \
                    --threshold_min "$threshold_min" \
                    --threshold_max "$threshold_max" \
                    --categories $categories
            else
                python3 "$PYTHON_SCRIPT" \
                    --data_path "$data_path" \
                    --threshold_min "$threshold_min" \
                    --threshold_max "$threshold_max"
            fi
            ;;
        *)
            print_error "未知的预设配置: $preset"
            print_info "可用的预设配置: situp, sitreach, custom"
            exit 1
            ;;
    esac
}

# 主函数
main() {
    # 检查Python脚本
    check_script
    
    # 解析命令行参数
    local args=()
    local preset=""
    local test_mode=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -t|--test)
                test_mode=true
                shift
                ;;
            --preset)
                preset="$2"
                shift 2
                ;;
            *)
                args+=("$1")
                shift
                ;;
        esac
    done
    
    # 运行测试模式
    if [ "$test_mode" = true ]; then
        run_test
        exit 0
    fi
    
    # 运行预设配置
    if [ -n "$preset" ]; then
        run_preset "$preset"
        exit 0
    fi
    
    # 运行普通模式
    if [ ${#args[@]} -eq 0 ]; then
        print_info "使用默认配置运行..."
        python3 "$PYTHON_SCRIPT"
    else
        print_info "使用自定义参数运行..."
        python3 "$PYTHON_SCRIPT" "${args[@]}"
    fi
    
    # 检查执行结果
    if [ $? -eq 0 ]; then
        print_success "统计分析完成!"
    else
        print_error "统计分析失败!"
        exit 1
    fi
}

# 脚本入口
if [ "${BASH_SOURCE[0]}" == "${0}" ]; then
    main "$@"
fi
