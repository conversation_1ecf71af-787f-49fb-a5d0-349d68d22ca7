# 数据集长宽比统计工具 (statistic_WH_aug.py)

## 功能描述

这是一个用于统计分类模型数据集中图像长宽比的增强版工具。主要功能包括：

1. **多类别统计**: 遍历数据集的所有类别（0、1、2等）
2. **长宽比分析**: 计算每个类别中所有jpg图片的长宽比统计信息
3. **统计指标**: 计算平均值、中位数、最小值、最大值
4. **差异化样本检测**: 识别长宽比超出设定阈值范围的样本
5. **结果输出**: 生成详细的统计报告和差异化样本列表

## 数据集结构要求

```
data_pth/
├── labels.txt            # 类别标签文件（可选）
├── 0_normal/             # 类别目录（名称从labels.txt读取）
│   ├── sample_001/       # 样本目录
│   │   ├── image1.jpg    # 图片文件
│   │   ├── image2.jpg
│   │   ├── image3.jpg
│   │   ├── data.pkl      # pkl文件
│   │   └── video.avi     # 视频文件
│   └── sample_002/
├── 1_fake/               # 类别目录
└── 2_headErr/            # 类别目录
```

### labels.txt文件格式

```
0_normal
1_fake
2_headErr
```

**注意**:
- 如果存在`labels.txt`文件，工具会自动读取其中的类别名称
- 如果不存在`labels.txt`文件，工具会自动检测数据集中的目录作为类别
- 也可以通过`--categories`参数手动指定类别列表

## 使用方法

### 基本使用

```bash
# 使用默认参数
python statistic_WH_aug.py

# 指定数据集路径
python statistic_WH_aug.py --data_path /path/to/your/dataset
```

### 高级使用

```bash
# 自定义阈值范围
python statistic_WH_aug.py \
    --data_path /path/to/dataset \
    --threshold_min 1.2 \
    --threshold_max 1.8

# 指定处理的类别
python statistic_WH_aug.py \
    --data_path /path/to/dataset \
    --categories 0 1 2 3

# 自定义输出目录和进程数
python statistic_WH_aug.py \
    --data_path /path/to/dataset \
    --output_dir /path/to/output \
    --max_workers 16
```

## 命令行参数

| 参数 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `--data_path` | str | `/root/share175/sport_test/sit_up/classify_cheat/pose_rgb` | 数据集根路径 |
| `--threshold_min` | float | 1.0 | 长宽比最小阈值 |
| `--threshold_max` | float | 2.0 | 长宽比最大阈值 |
| `--categories` | list | None | 要处理的类别列表（如果不指定，将从labels.txt文件或自动检测获取） |
| `--labels_file` | str | None | labels文件路径（默认为数据集根路径下的labels.txt） |
| `--output_dir` | str | None | 输出目录路径（默认为数据集父目录下的statistics_output） |
| `--max_workers` | int | None | 最大进程数（默认为CPU核心数和8的最小值） |

## 输出文件

工具会在输出目录中生成以下文件：

1. **详细统计报告** (`detailed_statistics_YYYYMMDD_HHMMSS.txt`)
   - 总体统计信息
   - 各类别详细统计数据
   - 差异化样本比例

2. **差异化样本列表** (`outlier_samples_threshold_MIN_MAX_YYYYMMDD_HHMMSS.txt`)
   - 超出阈值范围的样本详细信息
   - 按类别分组显示
   - 包含样本路径、图片尺寸、长宽比等信息

## 输出示例

### 控制台输出

```
================================================================================
数据集长宽比统计结果
================================================================================

类别 0:
  总图片数量: 1500
  长宽比平均值: 1.3333
  长宽比中位数: 1.3125
  长宽比范围: [0.8750, 2.1250]
  差异化样本数量: 45

类别 1:
  总图片数量: 1200
  长宽比平均值: 1.4167
  长宽比中位数: 1.4000
  长宽比范围: [0.9375, 1.9500]
  差异化样本数量: 23

类别 2:
  总图片数量: 800
  长宽比平均值: 1.2500
  长宽比中位数: 1.2500
  长宽比范围: [1.0000, 1.8750]
  差异化样本数量: 12
```

### 差异化样本文件示例

```
差异化样本统计报告
阈值范围: [1.00, 2.00]
总计差异化样本: 80
================================================================================

类别 0 - 差异化样本数量: 45
--------------------------------------------------
样本目录: /path/to/dataset/0/sample_001
图片路径: /path/to/dataset/0/sample_001/image1.jpg
尺寸: 1920x2400
长宽比: 0.8000

样本目录: /path/to/dataset/0/sample_002
图片路径: /path/to/dataset/0/sample_002/image2.jpg
尺寸: 2560x1080
长宽比: 2.3704
```

## 技术特性

- **多进程处理**: 使用多进程加速大型数据集的文件搜索和处理
- **内存优化**: 采用生成器模式处理大量文件，避免内存溢出
- **异常处理**: 完善的错误处理机制，确保程序稳定运行
- **类型提示**: 完整的Python类型提示，提高代码可维护性
- **日志记录**: 详细的日志输出，便于调试和监控处理进度

## 依赖要求

```python
# 标准库
import os
import statistics
import argparse
import multiprocessing
from concurrent.futures import ProcessPoolExecutor
from pathlib import Path
from dataclasses import dataclass
from datetime import datetime
import logging

# 第三方库
import numpy as np
from PIL import Image
```

## 性能优化建议

1. **调整进程数**: 根据系统配置调整 `--max_workers` 参数
2. **SSD存储**: 使用SSD存储可显著提升文件读取速度
3. **内存配置**: 确保系统有足够内存处理大型数据集
4. **网络存储**: 避免在网络存储上直接运行，建议先复制到本地

## 故障排除

### 常见问题

1. **内存不足**: 减少 `max_workers` 参数值
2. **权限错误**: 确保对数据集目录有读取权限
3. **图片格式错误**: 检查图片文件是否损坏
4. **路径不存在**: 验证数据集路径是否正确

### 日志级别调整

```python
# 在脚本开头修改日志级别
logging.basicConfig(level=logging.DEBUG)  # 更详细的日志
logging.basicConfig(level=logging.WARNING)  # 只显示警告和错误
```

## 版本历史

- **v1.0**: 基础功能实现
- **v1.1**: 添加命令行参数支持
- **v1.2**: 优化多进程处理和错误处理
