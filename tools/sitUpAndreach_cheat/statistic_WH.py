# -*-coding:utf-8-*-
"""
统计双流模型 训练集中的图像长宽比
"""
import os
from typing import List, Set, Generator, Tuple
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor
from pathlib import Path
from ReadTypeFile_Speed import rglob_optimized_generator


def _process_single_directory(args: Tuple[str, Set[str]]) -> List[str]:
    """处理单个目录的全局函数"""
    directory, extensions = args
    local_files = []

    for dirpath, dirnames, filenames in os.walk(directory):
        dirnames[:] = [d for d in dirnames if not d.startswith(".")]
        for filename in filenames:
            if any(filename.lower().endswith(ext) for ext in extensions):
                local_files.append(os.path.join(dirpath, filename))

    return local_files


def find_files_multiprocess(
    root_directory: str, file_extensions: List[str], max_workers: int = None
) -> List[str]:
    """
    多进程超高速文件搜索 - 适合大型目录

    Args:
        root_directory: 根目录路径
        file_extensions: 文件扩展名列表
        max_workers: 最大进程数，默认为CPU核心数

    Returns:
        匹配文件的完整路径列表
    """
    if not os.path.exists(root_directory):
        return []

    # 预处理扩展名
    extensions = {
        ext.lower() if ext.startswith(".") else f".{ext.lower()}"
        for ext in file_extensions
    }
    max_workers = max_workers or min(mp.cpu_count(), 8)  # 限制最大进程数

    # 获取顶层目录
    # try:
    with os.scandir(root_directory) as entries:
        top_dirs = [
            entry.path
            for entry in entries
            if entry.is_dir() and not entry.name.startswith(".")
        ]
        root_files = []

        # 处理根目录文件
        for entry in entries:
            if entry.is_file() and any(
                entry.name.lower().endswith(ext) for ext in extensions
            ):
                root_files.append(os.path.join(root_directory, entry.name))
    # except (PermissionError, OSError):
    #     return []

    if not top_dirs:
        return root_files

    # 准备多进程参数
    args_list = [(directory, extensions) for directory in top_dirs]
    all_files = root_files[:]

    # try:
    with ProcessPoolExecutor(max_workers=max_workers) as executor:
        results = executor.map(_process_single_directory, args_list)
        for result in results:
            all_files.extend(result)
    # except Exception as e:
    #     print(f"多进程处理出错，回退到单线程: {e}")
    #     # 回退到单线程处理
    #     for directory in top_dirs:
    #         try:
    #             for dirpath, dirnames, filenames in os.walk(directory):
    #                 dirnames[:] = [d for d in dirnames if not d.startswith('.')]
    #                 for filename in filenames:
    #                     if any(filename.lower().endswith(ext) for ext in extensions):
    #                         all_files.append(os.path.join(dirpath, filename))
    #         except (PermissionError, OSError):
    #             continue

    return all_files


# data_pth = f"/root/share175/sport_datas/sit_up/classify_cheating/train/pose_rgb/1_fake"
data_pth = f"/root/share175/sport_test/sit_up/classify_cheat/pose_rgb/1_fake"


for a_pkl in find_files_multiprocess(data_pth, [".pkl"]):
    dir_sample = Path(a_pkl).parent

    print(dir_sample)

"""
# 快速测试
./tools/sitUpAndreach_cheat/run_statistic_WH_aug.sh --test

# 基本使用
python3 tools/sitUpAndreach_cheat/statistic_WH_aug.py --data_path /path/to/dataset

# 自定义配置
python3 tools/sitUpAndreach_cheat/statistic_WH_aug.py \
    --data_path /path/to/dataset \
    --threshold_min 1.2 \
    --threshold_max 1.8 \
    --categories 0 1 2
"""
