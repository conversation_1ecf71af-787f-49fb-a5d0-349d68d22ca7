# -*-coding:utf-8-*-
"""
仰卧起坐
指定文件夹中， 将属于相同原始视频的样本放在一起
"""

import os
import shutil
import random
import re
from pathlib import Path
from mThread_Paralleltoolkit import parallel_process, fast_parallel


def extract_loop_numbers(filename):
    """从文件名中提取loop_数字_数字部分"""
    match = re.search(r'loop(\d+)_(\d+)', filename)
    if match:
        return int(match.group(1)), int(match.group(2))
    return None, None


def get_base_name(filename):
    """获取文件的基础名称（不含扩展名）"""
    return os.path.splitext(filename)[0]


def find_matching_files(pkl_file, all_files):
    """根据pkl文件查找匹配的avi和jpg文件"""
    base_name = get_base_name(pkl_file)

    # 查找对应的avi文件
    avi_file = None
    for file in all_files:
        if file.endswith('.avi') and get_base_name(file) == base_name:
            avi_file = file
            break

    if not avi_file:
        return None, []

    # 提取loop数字
    start_num, end_num = extract_loop_numbers(base_name)
    if start_num is None or end_num is None:
        return None, []

    # 查找所有匹配的jpg文件
    jpg_files = []
    jpg_pattern = f"{base_name}-"

    for file in all_files:
        if file.endswith('.jpg') and file.startswith(jpg_pattern):
            # 提取jpg文件名中的数字
            number_match = re.search(r'-(\d+)\.jpg$', file)
            if number_match:
                number = int(number_match.group(1))
                jpg_files.append((file, number))

    if len(jpg_files) < 3:
        return None, []

    # 按数字排序
    jpg_files.sort(key=lambda x: x[1])

    # 查找start_num和end_num对应的jpg文件
    start_jpg = None
    end_jpg = None
    middle_candidates = []

    for jpg_file, number in jpg_files:
        if number == start_num:
            start_jpg = jpg_file
        elif number == end_num:
            end_jpg = jpg_file
        elif start_num < number < end_num:
            middle_candidates.append((jpg_file, number))

    # 检查是否找到了start和end对应的jpg
    if not start_jpg or not end_jpg:
        return None, []

    # 选择中间的jpg文件
    if not middle_candidates:
        return None, []

    # 如果有多个中间候选，选择最靠近end_num的
    middle_jpg = max(middle_candidates, key=lambda x: x[1])[0]

    selected_jpgs = [start_jpg, end_jpg, middle_jpg]

    return avi_file, selected_jpgs


def process_directory(input_dir, output_dir):
    """处理目录中的文件"""
    input_path = Path(input_dir)
    output_path = Path(output_dir)

    # 创建输出目录
    output_path.mkdir(parents=True, exist_ok=True)

    processed_count = 0



        # 遍历所有子文件夹
    for subfolder in input_path.iterdir():
        if not subfolder.is_dir():
            continue

        print(f"处理文件夹: {subfolder.name}")

        # 获取子文件夹中的所有文件
        all_files = [f.name for f in subfolder.iterdir() if f.is_file()]

        # 找到所有pkl文件
        pkl_files = [f for f in all_files if f.endswith('.pkl')]

        if len(pkl_files) == 0:
            print(f"  跳过 - 没有找到pkl文件")
            continue

        # 根据pkl文件数量决定处理方式
        if len(pkl_files) == 1:
            selected_pkl = pkl_files[0]
            print(f"  只有1个pkl文件: {selected_pkl}")
        else:
            # 随机选择一个pkl文件
            selected_pkl = random.choice(pkl_files)
            print(f"  从{len(pkl_files)}个pkl文件中随机选择: {selected_pkl}")

        # 查找匹配的文件
        avi_file, jpg_files = find_matching_files(selected_pkl, all_files)

        if not avi_file or len(jpg_files) != 3:
            print(f"  跳过 - 匹配文件不完整 (avi: {avi_file is not None}, jpg: {len(jpg_files) if jpg_files else 0})")
            continue

        # 创建目标文件夹
        base_name = get_base_name(selected_pkl)
        target_folder = output_path / base_name
        target_folder.mkdir(parents=True, exist_ok=True)

        # 移动文件
        try:
            # 移动pkl文件
            shutil.move(str(subfolder / selected_pkl), str(target_folder / selected_pkl))
            print(f"  移动: {selected_pkl}")

            # 移动avi文件
            shutil.move(str(subfolder / avi_file), str(target_folder / avi_file))
            print(f"  移动: {avi_file}")

            # 移动jpg文件
            for jpg_file in jpg_files:
                shutil.move(str(subfolder / jpg_file), str(target_folder / jpg_file))
                print(f"  移动: {jpg_file}")

            processed_count += 1
            print(f"  成功处理并移动到: {target_folder}")

        except Exception as e:
            print(f"  错误 - 移动文件时出现问题: {e}")
            # 如果移动失败，尝试清理已创建的目标文件夹
            if target_folder.exists():
                try:
                    shutil.rmtree(target_folder)
                except:
                    pass

    print(f"\n处理完成！总共成功处理了 {processed_count} 组数据")


@parallel_process(fast_parallel(64))
def samples_del(subfolders, output_path, processed_count):
    subfolder = subfolders

    # 获取子文件夹中的所有文件
    all_files = [f.name for f in subfolder.iterdir() if f.is_file()]

    # 找到所有pkl文件
    pkl_files = [f for f in all_files if f.endswith('.pkl')]

    if len(pkl_files) == 0:
        print(f"  跳过 - 没有找到pkl文件")
        return

    # 根据pkl文件数量决定处理方式
    if len(pkl_files) == 1:
        selected_pkl = pkl_files[0]
        print(f"  只有1个pkl文件: {selected_pkl}")
    else:
        # 随机选择一个pkl文件
        selected_pkl = random.choice(pkl_files)
        print(f"  从{len(pkl_files)}个pkl文件中随机选择: {selected_pkl}")

    # 查找匹配的文件
    avi_file, jpg_files = find_matching_files(selected_pkl, all_files)

    if not avi_file or len(jpg_files) != 3:
        print(f"  跳过 - 匹配文件不完整 (avi: {avi_file is not None}, jpg: {len(jpg_files) if jpg_files else 0})")
        return

    # 创建目标文件夹
    base_name = get_base_name(selected_pkl)
    target_folder = output_path / base_name
    target_folder.mkdir(parents=True, exist_ok=True)

    # 移动文件
    try:
        # 移动pkl文件
        shutil.move(str(subfolder / selected_pkl), str(target_folder / selected_pkl))
        print(f"  移动: {selected_pkl}")

        # 移动avi文件
        shutil.move(str(subfolder / avi_file), str(target_folder / avi_file))
        print(f"  移动: {avi_file}")

        # 移动jpg文件
        for jpg_file in jpg_files:
            shutil.move(str(subfolder / jpg_file), str(target_folder / jpg_file))
            print(f"  移动: {jpg_file}")

        processed_count += 1
        print(f"  成功处理并移动到: {target_folder}")
    except Exception as e:
        print(f"  错误 - 移动文件时出现问题: {e}")
        # 如果移动失败，尝试清理已创建的目标文件夹
        if target_folder.exists():
            try:
                shutil.rmtree(target_folder)
            except:
                pass


def process_directory_MultiLine(input_dir, output_dir):
    """处理目录中的文件"""
    input_path = Path(input_dir)
    output_path = Path(output_dir)

    # 创建输出目录
    output_path.mkdir(parents=True, exist_ok=True)

    processed_count = 0

    subfolders = [subfolder for subfolder in input_path.iterdir() if subfolder.is_dir()]
    samples_del(subfolders, output_path, processed_count)

    print(f"\n处理完成！总共成功处理了 {processed_count} 组数据")




def organize_files(source_dir_name: str = 'dir'):
    """
    将指定文件夹内的 avi, pkl, jpg 文件根据命名规则进行分组。

    分组规则:
    - 文件夹名: 基于文件名的前两部分，如 'data_ip_len23'
    - 移动所有以此部分开头的文件到该文件夹。
    """
    source_dir = Path(source_dir_name)

    # 1. 检查源文件夹是否存在
    if not source_dir.is_dir():
        print(f"@Moss：文件夹 '{source_dir}' 不存在。")
        return


    # 2. 遍历源文件夹中的所有文件
    # 使用 glob('*.*') 可以大致过滤掉文件夹，只选择带扩展名的文件
    for file_path in source_dir.rglob('*.*'):
        # 确保我们只处理文件，而不是子目录
        if not file_path.is_file():
            continue

        # 3. 解析文件名以确定分组名
        parts = file_path.name.split('_loop')
        if len(parts) < 2:
            print(f"  -> 跳过不符合格式的文件: {file_path.name}")
            continue

        # 分组文件夹名由文件名的前两部分组成
        group_name = parts[0]

        # 4. 创建目标文件夹 (如果尚不存在)
        target_dir = source_dir / group_name
        target_dir.mkdir(exist_ok=True)


        # 5. 移动文件到目标文件夹
        destination_path = target_dir / file_path.name
        # 使用 shutil.move 更稳妥，Path.rename 在跨盘符时可能失败
        shutil.move(str(file_path), str(destination_path))

        print(f"  -> 已移动 '{file_path.name}' 到 '{group_name}/'")

    print("处理完成。")


def sample_files(input_directory):

    # 设置输入和输出目录
    output_directory = Path(input_directory).parent / Path(Path(input_directory).name + '_sample')

    # 检查输入目录是否存在
    if not os.path.exists(input_directory):
        print(f"错误：输入目录不存在: {input_directory}")
        return

    # 确认操作
    print(f"\n将要处理的目录: {input_directory}")
    print(f"输出目录: {output_directory}")

    process_directory_MultiLine(input_directory, output_directory)


# --- 主程序入口 ---
if __name__ == "__main__":

    # dir_0 = f"/root/share175/sport_datas/sit_up/classify_cheating/ori_vids_fusion/襄阳市谷城县城关镇第三初级中学/samples"
    # for dir_path in list(Path(dir_0).glob('*')):
    #     if dir_path.is_dir():
    #         print(dir_path)
    #         sample_files(input_directory=dir_path)
    # exit()

    # dir_1 = "/root/share175/sport_datas/sit_up/classify_cheating/ori_vids_fusion/Hunan_changshataiyushiyanxiaoxue/2024_sampled_Matched/1"
    #
    # for dir_path in list(Path(dir_1).glob('*')):
    #     if dir_path.is_dir():
    #         vid_name = '_'.join(dir_path.name.split('_')[:7])
    #         dst = dir_path.parent / vid_name
    #         Path(vid_name).mkdir(exist_ok=True)
    #         shutil.move(str(dir_path), str(dst))
    #         print(vid_name)
    #
    #
    # exit()




    # 将这里的 'dir1' 替换为你的实际文件夹名
    dir_path = \
        f"/root/share175/sport_datas/sit_up/classify_cheating/ori_vids_fusion/Hunan_changshaogaoxingqushiyanxiaoxuevideos/1_sampled"
    # organize_files(dir_path)

    sample_files(input_directory=dir_path)


    # all_dirs = [p for p in Path(dir_path).glob("*") if p.is_dir()]
    # sorted_dir = sorted(all_dirs, key=lambda p: len(p.parts),reverse=True)
    #
    # for p in sorted_dir:
    #     if not any(p.iterdir()):
    #         p.rmdir()


