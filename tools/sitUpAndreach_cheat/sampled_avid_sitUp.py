# -*-coding:utf-8-*-
"""
@Moss 2024/08/07
需求是从路径中抽取不同样本时长的样本
"""
import random
from pathlib import Path


def main(avid_pth):

    dst = avid_pth + '_Sampled'
    avid_pth = Path(avid_pth)

    Path(dst).mkdir(exist_ok=True)

    avids = list(avid_pth.rglob('*.avi'))
    length_num = set()

    for vid in avids:
        parts = str(vid).split('_len')
        if len(parts) > 1 and parts[1].find('_') != -1:
            length = parts[1].split('_')[0]
            length_num.add(length)  # 集合自动去重

    # 抽样
    for length in length_num:
        # 构建检索
        pattern = f'_len{length}_'
        avis = [f for f in avids if pattern in str(f)]
        if avis:
            a_vidlab = random.choice(avis)
            a_pkl = a_vidlab.with_suffix('.pkl')
            print(a_pkl.name)

            # 剪切
            a_vidlab.rename(Path(dst) / a_vidlab.name)
            a_pkl.rename(Path(dst) / a_pkl.name)


if __name__ == '__main__':
    dirs = '/root/share175/sport_datas/sit_and_reach/classify_cheating/ori_videos/SitUp_oriVideos/Zhengzhou_shangxueyuan/2024_10_18_sampled'
    #dirs = r'/root/share175/sport_datas/sit_and_reach/classify_cheating/train/skl_points/3_violate_rule/no_cover_head/Zhenzhou_hangtianhangkongxueyuan_sitUp'

    assert Path(dirs).is_dir(), f'@Moss: {dirs} do not exist'
    sub_dirs = [str(sub) for sub in Path(dirs).glob('*') if sub.is_dir()]
    assert len(sub_dirs) >= 2, f"@Moss: num of files is {len(sub_dirs)} , 路径需要给大文件夹, 大文件夹下要有子文件夹，子文件夹下放数据"

    for subdir in sub_dirs:
        try:
            main(avid_pth=subdir)
        except Exception as e:
            print(e)
            continue







