# 图片长宽比统计工具使用说明

## 功能概述

`statistic_WH_curs.py` 是一个用于统计分类模型数据集中图片长宽比的高效工具。

### 主要功能

1. **自动遍历数据集类别**：支持包含 0、1、2 等数字命名的类别目录
2. **批量图片分析**：使用多进程/多线程快速处理大量图片
3. **统计分析**：计算每个类别的平均值、中位数、最值、标准差等
4. **异常检测**：识别长宽比超出设定阈值的样本
5. **多格式输出**：生成详细的txt报告和JSON统计摘要

## 数据集结构要求

```
data_path/
├── 0/           # 类别0
│   ├── sample1/
│   │   ├── img1.jpg
│   │   ├── img2.jpg
│   │   ├── img3.jpg
│   │   ├── data.pkl
│   │   └── video.avi
│   └── sample2/
│       └── ...
├── 1/           # 类别1
│   └── ...
└── 2/           # 类别2
    └── ...
```

## 安装依赖

```bash
pip install opencv-python numpy
```

## 使用方法

### 基本用法

```bash
# 使用默认参数
python statistic_WH_curs.py

# 指定数据集路径
python statistic_WH_curs.py --data_path /path/to/your/dataset
```

### 高级用法

```bash
# 自定义阈值范围
python statistic_WH_curs.py --min_threshold 0.8 --max_threshold 2.5

# 自定义输出文件
python statistic_WH_curs.py --output my_outliers.txt --json_output my_stats.json

# 静默模式
python statistic_WH_curs.py --quiet

# 控制并行度
python statistic_WH_curs.py --max_workers 8
```

### 完整参数示例

```bash
python statistic_WH_curs.py \
    --data_path /root/data/classify_dataset \
    --min_threshold 0.8 \
    --max_threshold 2.2 \
    --output outliers_report.txt \
    --json_output stats_summary.json \
    --max_workers 6
```

## 参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `--data_path` | str | `/root/share175/sport_test/sit_up/classify_cheat/pose_rgb/1_fake` | 数据集根目录路径 |
| `--min_threshold` | float | 1.0 | 长宽比最小阈值 |
| `--max_threshold` | float | 2.0 | 长宽比最大阈值 |
| `--output` | str | `outlier_samples.txt` | 异常样本输出文件路径 |
| `--json_output` | str | `statistics_summary.json` | 统计摘要JSON输出文件路径 |
| `--max_workers` | int | 自动检测 | 最大并行工作进程数 |
| `--quiet` | bool | False | 静默模式，减少输出信息 |

## 输出文件

### 1. 异常样本报告 (outlier_samples.txt)

包含详细的异常样本信息：

- 样本路径
- 图片尺寸
- 长宽比数值
- 异常类型（大于/小于阈值）

### 2. 统计摘要 (statistics_summary.json)

包含每个类别的统计信息：

```json
{
  "0": {
    "category": "0",
    "total_images": 1500,
    "failed_images": 0,
    "mean_ratio": 1.7778,
    "median_ratio": 1.7778,
    "min_ratio": 1.7778,
    "max_ratio": 1.7778,
    "std_ratio": 0.0,
    "sample_count": 500
  }
}
```

## 性能特点

- **多进程文件搜索**：快速遍历大型目录结构
- **多线程图片处理**：并行读取图片尺寸信息
- **内存优化**：流式处理，适合处理大型数据集
- **异常处理**：对损坏或无法读取的图片进行容错处理

## 使用场景

1. **数据集质量检查**：发现尺寸异常的图片
2. **模型训练准备**：确保输入数据的一致性
3. **数据预处理**：识别需要调整尺寸的样本
4. **质量控制**：批量验证图片数据集的规范性

## 注意事项

1. 确保数据集目录结构符合要求
2. 脚本会自动跳过无法读取的图片文件
3. 建议在大型数据集上使用时适当调整 `max_workers` 参数
4. 阈值设置应根据具体的模型要求进行调整

## 示例输出

```
开始统计数据集: /path/to/dataset
长宽比阈值范围: [1.0, 2.0]
最大并行进程数: 自动
============================================================
找到 3 个类别目录: ['0', '1', '2']

[1/3] 处理类别 0...
处理类别 0: /path/to/dataset/0
找到 1500 张图片，开始计算长宽比...
类别 0 处理完成 - 图片: 1500, 异常: 0

[2/3] 处理类别 1...
...

============================================================
统计结果汇总
============================================================

类别 0:
  总图片数: 1500
  平均长宽比: 1.7778
  中位数长宽比: 1.7778
  最小长宽比: 1.7778
  最大长宽比: 1.7778
  标准差: 0.0000
  异常样本数: 0

总处理时间: 15.32秒
```
