# -*-coding:utf-8-*-
"""
调整数据为镜像数据
"""

from pathlib import Path
import pickle

ori = "/root/share175/sport_datas/action_recongnition/base_skeleton/train/solidball_videos/train/skl_points"
ori_pth = Path(ori)

data = [p for p in list(ori_pth.rglob('*.pkl')) if 'Kangbashi' in str(p) and 'loop' in str(p)]

print(data)
for one in data:
    # del loop
    one.unlink()



# for one in data:
#     with open(one, 'rb') as dt:
#         vid_info = pickle.load(dt)
#     a_skpts = vid_info.get('ori_skpts')
#
#     det_skpts = a_skpts[:, 6:]
#     det_skpts[:, ::3] = 1920 - det_skpts[:, ::3]
#
#     one_skpts_x, one_skpts_y = a_skpts[0, 6:][[15, 18]].mean(), a_skpts[0, 6:][[16, 19]].mean()  # 获取 首帧51个关键点的肩点中心
#     det_skpts[:, ::3] -= one_skpts_x  # 以肩点中心为圆心的切换
#     det_skpts[:, 1::3] -= one_skpts_y
#
#     vid_info['pred_skpts'] = det_skpts
#
#     new = str(one).replace('loop', 'flip')
#     with open(new, 'wb') as fs:
#         pickle.dump(vid_info, fs)  # saved pkl



