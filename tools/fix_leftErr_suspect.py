# -*-coding:utf-8-*-
"""
created by @Moss 2024/08/07
修复因逻辑错误导致的问题：
 对于画面是朝左做动作的 仰卧起做、体前屈 pkl数据，会将存储的B区域和S区域的人体存反，这里
 需要对调朝左画面的 B区域和S区域的人体姿态
"""
import pickle
from pathlib import Path

ori_pths = f"/root/share175/sport_datas/sit_and_reach/classify_cheating/ori_videos/SitReach_oriVideos/zhengzhou_shangxueyuan/classification_types"
ori_pths = Path(ori_pths)

all_pkls = list(ori_pths.rglob('*.pkl'))


for a_pkl in all_pkls:
    try:
        with open(a_pkl, 'rb') as dat:
            pkl_dict = pickle.load(dat)

        pred_skpts = pkl_dict['pred_skpts']  # 初始样本

        need_fix_mirror = pred_skpts[0][0][6:][[33, 36]].mean() > pred_skpts[0][0][6:][[45, 48]].mean()  # 根据 胯点>脚踝X的大小 朝左需要镜像

        if need_fix_mirror:
            for frame_skpts in pred_skpts:
                frame_skpts[1], frame_skpts[-1] = frame_skpts[-1], frame_skpts[1]
            pkl_dict['pred_skpts'] = pred_skpts
            with open(a_pkl, 'wb') as fs:
                pickle.dump(pkl_dict, fs)  # saved pkl
            # print('---')
        else:
            continue

    except:
        print(str(a_pkl))
        continue
