# -*-coding:utf-8-*-
"""
created by @Moss 2025.02.11
# 筛选
不出成绩的视频、部分误触发视频： 视频没有终点部分画面
起点(无人)未触发成功的视频

# 运行方式：cd到项目根目录 使用python -m
python -m tools.run50.useful_vidFilter.py       # 从项目根目录运行脚本，避免导入问题

"""
from GenVidInfo import *
from Gen_Samples import *

import os
from Config.devices_selected import select_device, get_free_gpu_ids

device = select_device(get_free_gpu_ids(num_gpus=1, selected_device=[1]), batch_size=128)            # 用1张卡进行推理
os.environ["CUDA_VISABLE_DEVICES"] = f'{device}'                                # 选择空闲的卡
print(f"@Moss: we find and use One-device {device} for free cuda infer")

# ================================1. 先读配置项，读取视频, 保存data_info.pkl =====================================================================

data_cls = read_config(f"/root/share175/sport_trains/action_recongnition/base_skeleton/dataGeter_yoloPose/Config/baseline_run50_fake.yml")  # 读取所有配置内容, 如无必要，无需修改主函数
if not Path(data_cls.get('save_dir')).exists():
    ori_save = data_cls['videos_pth']
    data_cls['save_dir'] = Path(ori_save).with_name(f"{Path(ori_save).name}_Grade").__str__()         # 不出成绩的单独存储

# 获取 符合逻辑的关键帧idx
filter_run50_gradeVids(data_cls)


if __name__ == '__main__':
    pass

