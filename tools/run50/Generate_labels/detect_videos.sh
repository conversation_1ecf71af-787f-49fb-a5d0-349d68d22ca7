#!/bin/bash
#set -x             # 开启调试模式, 会将sh中信息逐个打印
creator=Moss
VideoMod=True           #
script_pth="/root/share175/sport_trains/run_50M/classify_cheating/mmaction2-main0401/mmaction2-main/tools"
run50_pth="/root/share175/sport_trains/action_recongnition/base_skeleton/dataGeter_yoloPose/tools/run50/Generate_labels"
data_pth="/root/share175/sport_test/run_50M/classify_cheating/testsets/videos"    # 视频测试集合路径
datasets_pth="$data_pth/../datasets_videos"
video_list="$datasets_pth/all_label.txt"
ann_file_pth="$datasets_pth/all_label_ann.pkl"
CONFIG="$datasets_pth/slowonly_r50_sport-keypoint.py"



CHECKPOINT="${script_pth}/work_dirs/slowonly_r50_sport-keypoint/20250424_145348/best_acc_top1_epoch_25.pth"     # 0421


# 指定要监测的显卡 GPU ID 列表（用逗号分隔）
test_batch=8
CUDA_DEVICES="4,5,6,7"       # "0,1,2,3", 即脚本等待的显卡
#CUDA_DEVICES="0,1,2,3"       # "0,1,2,3", 即脚本等待的显卡
THRESHOLD=5       # % 设定显存占用率临界值
WaitTime=20          # 设置监测间隔时间(s)

# 先做数据处理，再等待有资源时进行推理
python $run50_pth/create_vidlabel_lst.py --VideoMod $VideoMod --data_pth $data_pth --dataset_pth $datasets_pth
python $run50_pth/custom_yoloPose_extraction.py --TestMod True --video-list $video_list --ann_file_pth $ann_file_pth --test_batch $test_batch  --Add-dataMod True

# 使用方法：
  # 设置需要监控的显卡, 间隔WaitTime秒 查看1次显存占用率,
  # 所有监控的显卡都低于占用率阈值时，执行设置好的命令(command); 否则Sleep WaitTime秒继续监测；
command_func() {
    echo $data_pth

    CHECKPOINT=$CHECKPOINT CUDA_DEVICES=$CUDA_DEVICES CONFIG=$CONFIG bash ${script_pth}/dist_test.sh
#    python $run50_pth/Select_samples.py --datasets $datasets_pth --split $Method --score_thre $score_thre

    echo $data_pth
    echo 'okk.GPU_Monitor: detect_videos.sh'
}



# 定义监测函数，接受多个参数："$CUDA_DEVICES" "$THRESHOLD" "$WaitTime" "$要执行的命令"
monitor_gpu() {
    # 获取传递给函数的 GPU ID 列表参数
    CUDA_DEVICES="$1"
    # 将 GPU ID 列表转换为以空格分隔的格式
    CUDA_DEVICES_LIST=$(echo $CUDA_DEVICES | tr ',' ' ')
    # 定义显存占用率临界值和监测间隔时间
    THRESHOLD="$2"  # %
    WaitTime="$3"

    # 获取传递给函数的要执行的命令
    command="$4"


    while true; do
        # 假设所有显卡都满足条件
        ALL_BELOW_THRESHOLD=1

        for GPU_ID in $CUDA_DEVICES_LIST; do
            # 获取指定 GPU 的显存使用量和总显存大小
            VRAM_INFO=$(nvidia-smi --query-gpu=memory.used,memory.total --format=csv -i $GPU_ID)
            # 读取显存使用量和总显存大小（单位：MiB）
            read -r VRAM_USED_STR VRAM_TOTAL_STR <<< "$(grep -v '^$' <<< "$VRAM_INFO" | tail -n 1 | awk -F ', ' '{print $1,$2}')"

            # 提取数值部分（去掉单位）
            VRAM_USED=$(echo $VRAM_USED_STR | grep -o -E '[0-9]+')
            VRAM_TOTAL=$(echo $VRAM_TOTAL_STR | grep -o -E '[0-9]+')

            # 检查变量是否为空
            if [ -z "$VRAM_USED" ] || [ -z "$VRAM_TOTAL" ]; then
                echo "GPU ${GPU_ID}: Failed to extract VRAM information, setting to 0."
                VRAM_USED=0
                VRAM_TOTAL=1  # 防止除零错误
            fi

            # 避免除零错误
            if [ "$VRAM_TOTAL" -eq 0 ]; then
                echo "GPU ${GPU_ID} Total VRAM is 0, skip calculation."
                VRAM_PERCENTAGE=0
            else
                # 计算显存占用百分比（保留 2 位小数）
                VRAM_PERCENTAGE=$(awk -v used="$VRAM_USED" -v total="$VRAM_TOTAL" 'BEGIN {printf "%.2f", (used / total) * 100}')
            fi

            # 输出当前显存使用情况（便于调试）
            echo "GPU ${GPU_ID} 显存使用情况：${VRAM_USED_STR} / ${VRAM_TOTAL_STR}（Used-Rate：${VRAM_PERCENTAGE}%）"

            # 判断显存占用率是否大于设定的阈值（使用 awk 代替 bc 进行浮点比较）
            if [ $(awk -v perc="$VRAM_PERCENTAGE" -v th="$THRESHOLD" 'BEGIN {print (perc + 0 > th + 0)}') -eq 1 ]; then
                echo "GPU ${GPU_ID} Occupancy > Thres $THRESHOLD%，Continue Wait..."
                # 只要有一张显卡不满足条件，就将标志设置为 0
                ALL_BELOW_THRESHOLD=0
                break
            fi
        done

        # 如果所有显卡都满足条件，则执行传入的命令
        if [ $ALL_BELOW_THRESHOLD -eq 1 ]; then
            echo "All Set GPU Occupancy <= $THRESHOLD%，Start Run My Task as follow..."
            # 使用 eval 执行传入的命令
#            eval "$command"
            command_func
            break
        fi

        # 设置监测间隔（秒）
        sleep ${WaitTime}
        echo "Sleep ${WaitTime} s."
    done
}

# 调用函数，传入 GPU ID 列表和要执行的命令
monitor_gpu "$CUDA_DEVICES" "$THRESHOLD" "$WaitTime" "command_func"