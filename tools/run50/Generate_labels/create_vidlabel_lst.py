# -*-coding:utf-8-*-
"""
created by @Moss
fixed by @Moss 2025.04.10 新增测试模式

50m项目：创建 vid跑道标签 载入样本pkl
1. 按pkl名生成标签，划分训练和验证
输入:
    *  数据路径: 作弊数据集路径/train/skl_points
    *  label_name : labels.txt      # 在数据路径下，txt中每行只写入标签名(即类别文件夹名)
    *  验证数据占比：0.2
    *  TestMod: 默认为False
python create_vidlabel_lst.py
输出：
    * 在数据路径skl_points同级目录 生成tadasets/all_label.txt, train_label.txt, val_label.txt
"""
import random
import argparse
from tqdm import tqdm
from pathlib import Path


def read_labels(label_pth, TestMod=False, default_label=0):
    """
    获取标签信息，与文件夹关联
    """
    if TestMod:
        return f'{default_label}_default'

    with open(label_pth, 'r') as flab:
        labels = [line.strip() for line in flab.readlines() if len(line) > 1]

    return labels


class video_data:
    def __init__(self, val_rate, suffix='pkl', TestMod=False, VideoMod=False):
        self.TestMod = TestMod
        self.VideoMod = VideoMod
        self.suffix = suffix
        self.val_rate = val_rate  # 验证集比例 默认0.2

    def convert_dataset(self, lable_name, alabel_lst, txts_path_tup, SclName='Scl'):
        all_txt_path, train_txt_path, val_txt_path = txts_path_tup
        train_txt = open(train_txt_path, "a")
        val_txt = open(val_txt_path, "a")
        all_txt = open(all_txt_path, "a")

        # 按类别 划分训练、验证
        all_num = len(alabel_lst)
        all_list = range(all_num)
        # 这里按照2级别文件夹进行随机划分
        if self.val_rate == 1.0:
            val_num = all_list
        else:
            val_num = random.sample(all_list, int(self.val_rate * all_num))  # 随机抽取数据集的rate 做验证集
        for ik, vid_pth in enumerate(tqdm(alabel_lst, desc=f'Data {SclName}')):
            all_txt.writelines(vid_pth + " " + str(lable_name) + "\n")
            if ik in val_num:
                val_txt.writelines(vid_pth + " " + str(lable_name) + "\n")
            else:
                train_txt.writelines(vid_pth + " " + str(lable_name) + "\n")  #

        train_txt.close()
        val_txt.close()
        all_txt.close()

    def videos_files_convert(self, data_files_path, label_list, dataset_txt_path):
        dataset_txt_path = Path(dataset_txt_path)
        Path(dataset_txt_path).mkdir(exist_ok=True)

        all_txt_path = dataset_txt_path / "all_label.txt"
        open(all_txt_path, 'w').close()
        if self.VideoMod:
            alabel_lst = [str(f) for f in Path(data_files_path).rglob(f'*.{self.suffix}')]  # 类别下的所有样本
            self.convert_videoSet(alabel_lst, all_txt_path)
            return

        if self.TestMod:
            alabel_lst = [str(f) for f in Path(data_files_path).rglob(f'*.{self.suffix}')]  # 类别下的所有样本
            self.convert_testset(label_list, alabel_lst, all_txt_path)
            return

        train_txt_path = dataset_txt_path / "train_label.txt"
        val_txt_path = dataset_txt_path / "val_label.txt"
        txts_path_tup = (all_txt_path, train_txt_path, val_txt_path)
        open(train_txt_path, 'w').close()
        open(val_txt_path, 'w').close()

        for i, lable_name in enumerate(label_list):
            print(lable_name)
            video_files_path = Path(data_files_path) / lable_name
            if not video_files_path.exists():
                continue
            Scls_lst = [d for d in video_files_path.iterdir() if d.is_dir()]  # 类别下的所有文件夹
            for scl_dir in Scls_lst:
                alabel_lst = [str(f) for f in scl_dir.rglob(f'*.{self.suffix}')]  # 类别下的所有样本
                self.convert_dataset(lable_name, alabel_lst, txts_path_tup, SclName=scl_dir.name)

    @staticmethod
    def convert_testset(lable_name, alabel_lst, all_txt_path, SclName='Test-Scl'):
        all_txt = open(all_txt_path, "a")

        for ik, vid_pth in enumerate(tqdm(alabel_lst, desc=f'Data {SclName}')):
            all_txt.writelines(vid_pth + " " + str(lable_name) + "\n")

        all_txt.close()

    @staticmethod
    def convert_videoSet(alabel_lst, all_txt_path, SclName='Scl-VidDir'):
        all_txt = open(all_txt_path, "a")

        for ik, vid_pth in enumerate(tqdm(alabel_lst, desc=f'Data {SclName}')):
            lable_name = Path(vid_pth).parent.name
            all_txt.writelines(vid_pth + " " + str(lable_name) + "\n")

        all_txt.close()


if __name__ == '__main__':
    parser = argparse.ArgumentParser(prog='训练-验证/测试/视频测试集 标签生成')
    parser.add_argument('--data_pth', type=str,
                        default="/root/share175/sport_datas/run_50M/classify_cheating/train/skl_points",
                        help='训练-验证集 标签来源')
    # default="/root/share175/sport_test/run_50M/classify_cheating/testsets/videos", help='测试集 标签来源')

    parser.add_argument('--dataset_pth', type=str, default=None, help='标签路径')
    parser.add_argument('--dataset_vid', type=str, default=None, help='视频标签路径')

    parser.add_argument('--val_rate', default=0.15, help='验证集占整个训练集的比例')
    parser.add_argument('--label_name', default="labels.txt", help='标签名在数据路径中，并与文件夹对应')

    parser.add_argument('--TestMod', type=str, default=False, help='测试样本模式')
    parser.add_argument('--VideoMod', type=str, default=False, help='视频测试集模式')
    parser.add_argument('--video_pth', type=str,
                        default="/root/share175/sport_test/run_50M/classify_cheating/testsets/videos",
                        help='训练-验证集 标签来源')

    parser.add_argument('--default_lable', default=0, help='测试模式，生成默认标签0')

    opt = parser.parse_args()

    opt.val_rate = float(opt.val_rate)

    if isinstance(opt.TestMod, str):
        opt.TestMod = opt.TestMod.lower() == 'true'  # True 或 true
    if isinstance(opt.VideoMod, str):
        opt.VideoMod = opt.VideoMod.lower() == 'true'  # True 或 true
        opt.TestMod = True if opt.VideoMod else False  # 视频测试集下，默认使用测试模式


    label_pth = Path(opt.data_pth) / opt.label_name
    labels_lst = read_labels(label_pth, TestMod=opt.TestMod, default_label=opt.default_lable)
    print(labels_lst)

    video_data = video_data(val_rate=opt.val_rate, TestMod=opt.TestMod, VideoMod=opt.VideoMod)

    if opt.dataset_pth is None:
        opt.dataset_pth = Path(opt.data_pth).parent / "datasets"
    if opt.dataset_vid is None:
        opt.dataset_vid = Path(opt.video_pth).parent / "datasets_videos"  # 视频测试集 标签默认地址

    if opt.VideoMod:
        opt.data_pth = opt.video_pth
        opt.dataset_pth = opt.dataset_vid

    video_data.videos_files_convert(opt.data_pth, labels_lst, opt.dataset_pth)
