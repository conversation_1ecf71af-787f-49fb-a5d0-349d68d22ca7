#!/usr/bin/env bash
#set -x             # 开启调试模式, 会将sh中信息逐个打印
creator=Moss
TestMod=True           #
script_pth="/root/share175/sport_trains/run_50M/classify_cheating/mmaction2-main0401/mmaction2-main/tools"
run50_pth="/root/share175/sport_trains/action_recongnition/base_skeleton/dataGeter_yoloPose/tools/run50/Generate_labels"

Method="Match"          # Cut、Copy、Match
default_lable=0         # 默认标签为0
score_thre=0.65         # 仅在Match下产生效果

test_batch=1
CUDA_DEVICES="4,5,6,7"        # 默认使用前4张GPU卡
#CUDA_DEVICES="0,1,2,3"        # 默认使用前4张GPU卡
#CHECKPOINT="${script_pth}/work_dirs/slowonly_r50_sport-keypoint/20250411_122154/best_acc_top1_epoch_22.pth"
#CHECKPOINT="${script_pth}/work_dirs/slowonly_r50_sport-keypoint/20250414_134405/best_acc_top1_epoch_24.pth"     # 0415
#CHECKPOINT="${script_pth}/work_dirs/slowonly_r50_sport-keypoint/20250415_133034/best_acc_top1_epoch_22.pth"     # 0416
#CHECKPOINT="${script_pth}/work_dirs/slowonly_r50_sport-keypoint/20250417_123446/best_acc_top1_epoch_20.pth"     # 0418
#CHECKPOINT="${script_pth}/work_dirs/slowonly_r50_sport-keypoint/20250421_120954/best_acc_top1_epoch_24.pth"     # 0421
CHECKPOINT="${script_pth}/work_dirs/slowonly_r50_sport-keypoint/20250424_145348/epoch_28.pth"     # 0421[已发布]


#data_pth="/root/share175/sport_datas/run_50M/classify_cheating/ori_vids/南京陶行知中学/Run50_freeTest_freeTest__Grade"    # 0412 num 20451
#data_pth="/root/share175/sport_datas/run_50M/classify_cheating/ori_vids/长沙实验中学/Run50_freeTest_freeTest__Grade"    # 0412 num 11735
#data_pth="/root/share175/sport_datas/run_50M/classify_cheating/ori_vids/西安莲湖远东实验小学/西安市莲湖区远东实验小学_Grade_Grade_Grade_PosFilted"    # 0412 16:25
#data_pth="/root/share175/sport_datas/run_50M/classify_cheating/train/skl_points/0_normal/Model_check_Scl/Xiuzhou_shiyanxiaoxue_Pred"    # 0414
#data_pth="/root/share175/sport_datas/run_50M/classify_cheating/ori_vids/长沙实验中学/0415/all_dx_PosFilted"    # 0414
#data_pth="/root/share175/sport_datas/run_50M/classify_cheating/ori_vids/鄂尔多斯市一中伊金霍洛分校/Run50_freeTest_freeTest_2024_Grade"    # 0414
data_pth="/root/share175/sport_datas/run_50M/classify_cheating/test_with_CPP"    # 0414


datasets_pth="$data_pth/../datasets"
video_list="$datasets_pth/all_label.txt"
ann_file_pth="$datasets_pth/all_label_ann.pkl"
CONFIG="$datasets_pth/slowonly_r50_sport-keypoint.py"


# 1 生成默认标签 0_default; 2 生成pkl
python $run50_pth/create_vidlabel_lst.py --TestMod ${TestMod} --data_pth $data_pth --dataset_pth $datasets_pth --default_lable $default_lable
python $run50_pth/custom_yoloPose_extraction.py --TestMod ${TestMod} --video-list $video_list --ann_file_pth $ann_file_pth --test_batch $test_batch  --Add-dataMod True

# 3. 执行测试
CHECKPOINT=$CHECKPOINT CUDA_DEVICES=$CUDA_DEVICES CONFIG=$CONFIG \
bash ${script_pth}/dist_test.sh

# 4. Match Or Copy
#python $run50_pth/Select_samples.py --datasets $datasets_pth --split $Method --score_thre $score_thre


echo $data_pth
echo 'okk.'
