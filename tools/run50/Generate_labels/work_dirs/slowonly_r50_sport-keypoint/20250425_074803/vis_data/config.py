Test_ann_file = '/root/share175/sport_test/run_50M/classify_cheating/testsets/skl_points/0_normal/../datasets_0/all_label_ann.pkl'
TrainVal_ann_file = '/root/share175/sport_datas/run_50M/classify_cheating/train/datasets/trainval_yolopose.pkl'
dataset_type = 'PoseDataset'
default_hooks = dict(
    checkpoint=dict(interval=1, save_best='auto', type='CheckpointHook'),
    logger=dict(ignore_last=False, interval=20, type='LoggerHook'),
    param_scheduler=dict(type='ParamSchedulerHook'),
    runtime_info=dict(type='RuntimeInfoHook'),
    sampler_seed=dict(type='DistSamplerSeedHook'),
    sync_buffers=dict(type='SyncBuffersHook'),
    timer=dict(type='IterTimerHook'))
default_scope = 'mmaction'
env_cfg = dict(
    cudnn_benchmark=False,
    dist_cfg=dict(backend='nccl'),
    mp_cfg=dict(mp_start_method='fork', opencv_num_threads=0))
launcher = 'pytorch'
left_kp = [
    1,
    3,
    5,
    7,
    9,
    11,
    13,
    15,
]
load_from = '/root/share175/sport_trains/run_50M/classify_cheating/mmaction2-main0401/mmaction2-main/tools/work_dirs/slowonly_r50_sport-keypoint/20250424_145348/best_acc_top1_epoch_25.pth'
log_level = 'INFO'
log_processor = dict(by_epoch=True, type='LogProcessor', window_size=20)
model = dict(
    backbone=dict(
        base_channels=32,
        conv1_stride_s=1,
        depth=50,
        dilations=(
            1,
            1,
            1,
        ),
        in_channels=17,
        inflate=(
            0,
            1,
            1,
        ),
        num_stages=3,
        out_indices=(2, ),
        pool1_stride_s=1,
        pretrained=
        '/root/share175/sport_trains/run_50M/classify_cheating/mmaction2-main0401/mmaction2-main/configs/skeleton/posec3d/pretrained_model/slowonly_r50_8xb32-u48-240e_k400-keypoint_20230731-7f498b55.pth',
        spatial_strides=(
            2,
            2,
            2,
        ),
        stage_blocks=(
            4,
            6,
            3,
        ),
        temporal_strides=(
            1,
            1,
            2,
        ),
        type='ResNet3dSlowOnly'),
    cls_head=dict(
        average_clips='prob',
        dropout_ratio=0.5,
        in_channels=512,
        num_classes=3,
        type='I3DHead'),
    type='Recognizer3D')
optim_wrapper = dict(
    clip_grad=dict(max_norm=40, norm_type=2),
    optimizer=dict(lr=0.2, momentum=0.9, type='SGD', weight_decay=0.0003))
param_scheduler = [
    dict(
        T_max=24,
        by_epoch=True,
        convert_to_iter_based=True,
        eta_min=0,
        type='CosineAnnealingLR'),
]
resume = False
right_kp = [
    2,
    4,
    6,
    8,
    10,
    12,
    14,
    16,
]
split_label = dict({
    0: [
        1,
    ],
    1: [
        0,
        2,
    ]
})
test_batch = 2
test_cfg = dict(type='TestLoop')
test_dataloader = dict(
    batch_size=2,
    dataset=dict(
        ann_file=
        '/root/share175/sport_test/run_50M/classify_cheating/testsets/skl_points/0_normal/../datasets_0/all_label_ann.pkl',
        pipeline=[
            dict(
                clip_len=48,
                num_clips=10,
                test_mode=True,
                type='UniformSampleFrames'),
            dict(type='PoseDecode'),
            dict(type='LocationCompact', use_start_pt=True),
            dict(allow_imgpad=True, hw_ratio=1.0, type='PoseCompact'),
            dict(scale=(
                -1,
                64,
            ), type='Resize'),
            dict(crop_size=64, type='CenterCrop'),
            dict(
                double=True,
                left_kp=[
                    1,
                    3,
                    5,
                    7,
                    9,
                    11,
                    13,
                    15,
                ],
                right_kp=[
                    2,
                    4,
                    6,
                    8,
                    10,
                    12,
                    14,
                    16,
                ],
                sigma=0.6,
                type='GeneratePoseTarget',
                use_score=True,
                with_kp=True,
                with_limb=False),
            dict(input_format='NCTHW_Heatmap', type='FormatShape'),
            dict(
                pred_txt=
                '/root/share175/sport_test/run_50M/classify_cheating/testsets/skl_points/0_normal/../datasets_0',
                split_label=dict({
                    0: [
                        1,
                    ],
                    1: [
                        0,
                        2,
                    ]
                }),
                type='PackActionInputs'),
        ],
        split=None,
        test_mode=True,
        type='PoseDataset'),
    num_workers=1,
    persistent_workers=True,
    sampler=dict(shuffle=False, type='DefaultSampler'))
test_evaluator = [
    dict(type='ClassifyReport'),
]
test_pipeline = [
    dict(
        clip_len=48, num_clips=10, test_mode=True, type='UniformSampleFrames'),
    dict(type='PoseDecode'),
    dict(type='LocationCompact', use_start_pt=True),
    dict(allow_imgpad=True, hw_ratio=1.0, type='PoseCompact'),
    dict(scale=(
        -1,
        64,
    ), type='Resize'),
    dict(crop_size=64, type='CenterCrop'),
    dict(
        double=True,
        left_kp=[
            1,
            3,
            5,
            7,
            9,
            11,
            13,
            15,
        ],
        right_kp=[
            2,
            4,
            6,
            8,
            10,
            12,
            14,
            16,
        ],
        sigma=0.6,
        type='GeneratePoseTarget',
        use_score=True,
        with_kp=True,
        with_limb=False),
    dict(input_format='NCTHW_Heatmap', type='FormatShape'),
    dict(
        pred_txt=
        '/root/share175/sport_test/run_50M/classify_cheating/testsets/skl_points/0_normal/../datasets_0',
        split_label=dict({
            0: [
                1,
            ],
            1: [
                0,
                2,
            ]
        }),
        type='PackActionInputs'),
]
train_cfg = dict(
    max_epochs=30, type='EpochBasedTrainLoop', val_begin=1, val_interval=1)
train_dataloader = dict(
    batch_size=32,
    dataset=dict(
        dataset=dict(
            ann_file=
            '/root/share175/sport_datas/run_50M/classify_cheating/train/datasets/trainval_yolopose.pkl',
            pipeline=[
                dict(clip_len=48, type='UniformSampleFrames'),
                dict(type='PoseDecode'),
                dict(type='LocationCompact', use_start_pt=True),
                dict(allow_imgpad=True, hw_ratio=1.0, type='PoseCompact'),
                dict(scale=(
                    -1,
                    64,
                ), type='Resize'),
                dict(area_range=(
                    0.56,
                    1.0,
                ), type='RandomResizedCrop'),
                dict(keep_ratio=False, scale=(
                    56,
                    56,
                ), type='Resize'),
                dict(
                    flip_ratio=0.5,
                    left_kp=[
                        1,
                        3,
                        5,
                        7,
                        9,
                        11,
                        13,
                        15,
                    ],
                    right_kp=[
                        2,
                        4,
                        6,
                        8,
                        10,
                        12,
                        14,
                        16,
                    ],
                    type='Flip'),
                dict(
                    sigma=0.6,
                    type='GeneratePoseTarget',
                    use_score=True,
                    with_kp=True,
                    with_limb=False),
                dict(input_format='NCTHW_Heatmap', type='FormatShape'),
                dict(pred_txt=None, split_label=None, type='PackActionInputs'),
            ],
            split='train',
            type='PoseDataset'),
        times=10,
        type='RepeatDataset'),
    num_workers=1,
    persistent_workers=True,
    sampler=dict(shuffle=True, type='DefaultSampler'))
train_pipeline = [
    dict(clip_len=48, type='UniformSampleFrames'),
    dict(type='PoseDecode'),
    dict(type='LocationCompact', use_start_pt=True),
    dict(allow_imgpad=True, hw_ratio=1.0, type='PoseCompact'),
    dict(scale=(
        -1,
        64,
    ), type='Resize'),
    dict(area_range=(
        0.56,
        1.0,
    ), type='RandomResizedCrop'),
    dict(keep_ratio=False, scale=(
        56,
        56,
    ), type='Resize'),
    dict(
        flip_ratio=0.5,
        left_kp=[
            1,
            3,
            5,
            7,
            9,
            11,
            13,
            15,
        ],
        right_kp=[
            2,
            4,
            6,
            8,
            10,
            12,
            14,
            16,
        ],
        type='Flip'),
    dict(
        sigma=0.6,
        type='GeneratePoseTarget',
        use_score=True,
        with_kp=True,
        with_limb=False),
    dict(input_format='NCTHW_Heatmap', type='FormatShape'),
    dict(pred_txt=None, split_label=None, type='PackActionInputs'),
]
val_cfg = dict(type='ValLoop')
val_dataloader = dict(
    batch_size=32,
    dataset=dict(
        ann_file=
        '/root/share175/sport_datas/run_50M/classify_cheating/train/datasets/trainval_yolopose.pkl',
        pipeline=[
            dict(
                clip_len=48,
                num_clips=1,
                test_mode=True,
                type='UniformSampleFrames'),
            dict(type='PoseDecode'),
            dict(type='LocationCompact', use_start_pt=True),
            dict(allow_imgpad=True, hw_ratio=1.0, type='PoseCompact'),
            dict(scale=(
                -1,
                64,
            ), type='Resize'),
            dict(crop_size=64, type='CenterCrop'),
            dict(
                sigma=0.6,
                type='GeneratePoseTarget',
                use_score=True,
                with_kp=True,
                with_limb=False),
            dict(input_format='NCTHW_Heatmap', type='FormatShape'),
            dict(pred_txt=None, split_label=None, type='PackActionInputs'),
        ],
        split='val',
        test_mode=True,
        type='PoseDataset'),
    num_workers=1,
    persistent_workers=True,
    sampler=dict(shuffle=False, type='DefaultSampler'))
val_evaluator = [
    dict(type='AccMetric'),
]
val_pipeline = [
    dict(clip_len=48, num_clips=1, test_mode=True, type='UniformSampleFrames'),
    dict(type='PoseDecode'),
    dict(type='LocationCompact', use_start_pt=True),
    dict(allow_imgpad=True, hw_ratio=1.0, type='PoseCompact'),
    dict(scale=(
        -1,
        64,
    ), type='Resize'),
    dict(crop_size=64, type='CenterCrop'),
    dict(
        sigma=0.6,
        type='GeneratePoseTarget',
        use_score=True,
        with_kp=True,
        with_limb=False),
    dict(input_format='NCTHW_Heatmap', type='FormatShape'),
    dict(pred_txt=None, split_label=None, type='PackActionInputs'),
]
vis_backends = [
    dict(type='LocalVisBackend'),
]
visualizer = dict(
    type='ActionVisualizer', vis_backends=[
        dict(type='LocalVisBackend'),
    ])
work_dir = './work_dirs/slowonly_r50_sport-keypoint'
