# -*-coding:utf-8-*-
"""
由于 all_label_ann.pkl 和 slowonly_r50_sport-keypoint.py 的存在
可以将datasets/model_Match 中已剪切的数据，根据前版模型的推理结果,全部移动回原目录

"""
from pathlib import Path

pred_info = "/root/share175/sport_datas/run_50M/classify_cheating/ori_vids/武昌区滨江实验第一小学_Grade_Grade_Grade/datasets/pred_gt_labels.txt"

model_Match = Path(pred_info).parent / 'model_Match'


exist_samples = list(model_Match.rglob('*.*'))

with open(pred_info, 'r', encoding='utf-8') as pf:
    lines = pf.readlines()

file_pths = [line.split(',')[0] for line in lines]

for sample in exist_samples:
    name = sample.name

    ori_pth = next((pth for pth in file_pths if name in pth), None)
    if ori_pth is None:
        continue

    label = sample.with_suffix('.jpg')
    label_pth = Path(ori_pth).with_suffix('.jpg')
    label.rename(label_pth)
    sample.rename(ori_pth)

    print(name)
    del ori_pth
