# -*-coding:utf-8-*-
"""
created by Moss 2025/03/06 17:01
fixed by Moss 2025/04/01 16:54      # 新增起点附近丢失轨迹

注意事项：
关键字： split['train'] 'train'、'val' 在训练代码的配置文件中对应 split='xsub_train'
关键字： filename 对应 训练代码mmaction.datasets.pose_dataset.load_data_list()  标识符 'filename' 和 'frame_dir'

输出:
    * 在数据路径skl_points同级目录 生成datasets/all_label_ann.pkl, trainval_yolopose.pkl

说明, pkl中包含如下信息：
    * dict_keys(['keypoint',      # (1, T, 17, 2)
           'keypoint_score',      # (1, T, 17)
           'frames_skpt',       # 有效轨迹帧id [T]
           'start_pt',          # 起点标定中心 (x, y)
           'img_shape', 'original_shape',
           'total_frames',      # 有效总帧数
           'vid_frames',         # 视频部分的原始帧数(包含跟踪丢失和出画面的帧)
           'frame_dir', 'filename', 'label'])

"""
import pickle
import re
from pathlib import Path
from tqdm import tqdm
import argparse

import numpy as np


def mmengine_dump(obj, file_path, **kwargs):
    """
    参考mmengine.dump(anno, out) 将列表信息保存成pkl
    obj： 要序列化的对象
    file_path: 输出文件夹路径
    **kwargs: 传递给pickle.dump的额外参数
    """
    kwargs.setdefault('protocol', 2)        # 设置兼容老版本python
    with open(file_path, 'wb') as file:
        pickle.dump(obj, file, **kwargs)

    return


def mrlines(fname, sp='\n') -> list:
    """获取数据集的正确路径和标签"""
    f = open(fname).read().split(sp)
    while f != [] and f[-1] == '':
        f = f[:-1]

    lines = [x.split() for x in f]  # 将标签独立出来
    return lines


def read_file_pkl(pkl_name:str) -> dict:
    """读取pkl信息"""
    if not Path(pkl_name).exists():
        return {}

    with open(pkl_name, 'rb') as fi:
        data = pickle.load(fi)

    return data


def pose_getResult(anno_in, base_info):
    """
    将读取到的样本 -> 需要的数据字典
        num_person = 1      # 默认单人
    """
    if not len(anno_in):
        return {}
    anno = dict()

    frame_skpts = []         # 记录有效帧

    keypoint_lst = anno_in.get('pred_skpts', [])
    start_kpt_lst = anno_in.get('Start_run', {})

    # 未跟踪到 但在起点附近检测到的目标
    start_kpts, start_scores = [], []
    if len(start_kpt_lst):
        for _frame, a_skpt in start_kpt_lst.items():
            if a_skpt is None:
                continue
            a_skpt = a_skpt[6:].reshape(17,3)
            start_kpts.append(a_skpt[:,:2].unsqueeze(0))
            start_scores.append(a_skpt[:,2].unsqueeze(0))
            frame_skpts.append(_frame)

    if not len(frame_skpts):
        name = anno_in.get('sample_name', '')
        end_frame = int(re.search(r'_([\d]+)\.pkl', name).group(1))
        track_frame = end_frame - len(keypoint_lst) + 1
    else:
        track_frame = frame_skpts[0]

    # 跟踪到的目标
    skpts, scores, use_fid = [], [], []
    for k, skpt_ts in enumerate(keypoint_lst):
        if skpt_ts is None or len(skpt_ts) < 2:
            continue
        this_k = k + track_frame
        skpts.append(skpt_ts[1][..., :2])
        scores.append(skpt_ts[1][..., 2])
        use_fid.append(this_k)

    # 合并骨骼数据
    skpts = start_kpts + skpts
    scores = start_scores + scores
    # 合并获取的帧id
    frames_skpt = frame_skpts + use_fid

    anno['keypoint'] = np.stack(skpts, axis=1)           # (N, T, 17, 2)
    anno['keypoint_score'] = np.stack(scores, axis=1)           # (N, T, 17)
    anno['frames_skpt'] = frames_skpt           # [T]

    # 起跑线中心
    anno['start_pt'] = anno_in.get('start_pt', (None, None))
    # 其他信息汇总
    anno['img_shape'] = anno_in.get('img_shape')
    anno['original_shape'] = anno_in.get('oriVid_shape')
    anno['total_frames'] = len(skpts)
    anno['vid_frames'] = anno_in.get('total_frames')        # 视频部分的原始帧数(包含跟踪丢失和出画面的帧)

    anno.update(base_info)

    del anno_in
    return anno


def write_results(annos):
    results = []
    for anno_info in tqdm(annos):
        pkl_name = anno_info.get('filename', 'None')            # 很重要，测试时可以根据该元信息 获取到样本路径
        anno_dict = read_file_pkl(pkl_name)
        anno = pose_getResult(anno_dict, anno_info)

        results.append(anno)

    return results


def write_results_add_Mode(annos, line_pths):
    results = read_file_pkl(pkl_name=args.output)
    exit_pkls =[info.get('filename', 'None') for info in results]       # 读取已存在pkl中的数据
    exit_pkls += [str(args.output), str(args.out_pose_pkl)]

    # 过滤掉 已删除、或被移动的数据
    results = [item for item in results if item.get('filename', 'None') in line_pths]

    results_add = []
    for anno_info in tqdm(annos):
        pkl_name = anno_info.get('filename', 'None')  # 很重要，测试时可以根据该元信息 获取到样本路径
        if pkl_name in exit_pkls or args.output.name in pkl_name:
            continue
        anno_dict = read_file_pkl(pkl_name)
        anno = pose_getResult(anno_dict, anno_info)

        results_add.append(anno)
        print(f"add-dataMod: {Path(pkl_name).name}")

    results += results_add

    return results



def dump_all_label_ann(args) ->list:
    # 获取样本路径
    lines = mrlines(args.video_list)

    # * We set 'frame_dir' as the base name (w/o. suffix) of each video
    if len(lines[0]) == 2:
        annos = [dict(frame_dir=x[0].split('.')[0], filename=x[0], label=int(x[1].split('_')[0])) for x in lines]
    else:
        # 没有标签/标签错误，仅有pkl的情况，可能用于样本推理
        annos = [dict(frame_dir=x[0].split('.')[0], filename=x[0]) for x in lines]

    if args.Add_dataMod:
        line_pths = [line[0] for line in lines]
        results = write_results_add_Mode(annos, line_pths)
    else:
        results = write_results(annos)


    mmengine_dump(results, args.output)



    return results


def dump_trainVal_ann(args, annotations):
    train_lines = mrlines(args.tain_txt_path)
    val_lines = mrlines(args.val_txt_path)

    split = dict()
    split['train'] = [x[0] for x in train_lines]
    split['val'] = [x[0] for x in val_lines]

    mmengine_dump(dict(split=split, annotations=annotations), args.out_pose_pkl)

    return


def modeified_one(lines, target_var, new_value):
    modified = False
    for i, line in enumerate(lines):
        # 使用正则表达式匹配变量赋值行
        if re.match(rf'^{target_var}\s*=\s*.*$', line):
            # 替换变量值
            if isinstance(new_value, str):
                lines[i] = f"{target_var} = '{new_value}'\n"
            elif isinstance(new_value, int):
                pattern = r'{}s*=\s*\d+'.format(re.escape(target_var))
                replacement = r'{} = {}'.format(target_var, new_value)
                lines[i] = re.sub(pattern, replacement, lines[i])

            modified = True
            break

    if not modified:
        print(f"Moss: Do not Found {target_var}，can not be fixed。")

    return lines


def modify_config_file(file_path, target_var, new_value, new_file_path):
    """
    修改 slowonly_r50_sport-keypoint.py 文件中的变量值

    Args:
        file_path (str): config.py 文件的路径
        target_var (list): 要修改的变量名 [Test_ann_file, test_batch]
        new_value (list): 新的变量值 [val1,val2]
    """
    try:
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as file:
            lines = file.readlines()

        # 修改 列表中的变量值
        for target_one, new_one in zip(target_var, new_value):
            lines = modeified_one(lines, target_one, new_one)

        # 修改后的内容写到 新文件中
        with open(new_file_path, 'w', encoding='utf-8') as file:
            file.writelines(lines)

        print(f"successs fix the value of {target_var} to '{new_value}'。")

    except FileNotFoundError:
        print(f"错误: 文件 {file_path} 不存在。")
    except Exception as e:
        print(f"错误: 修改文件时发生异常 - {e}")



def parse_args():
    parser = argparse.ArgumentParser(
        description='Generate 2D pose annotations for a custom video dataset')

    parser.add_argument('--video-list', type=str, help='the list of source videos',
                        default="/root/share175/sport_datas/run_50M/classify_cheating/train/datasets/all_label.txt")

    parser.add_argument('--Add-dataMod', default=True, help='添加数据的模式，向已存在的大pkl中添加数据，避免重复生成数据，默认 False')

    parser.add_argument('--tain_txt', type=str, help='默认在video_list同级', default='train_label.txt')
    parser.add_argument('--val_txt', type=str, help='默认在video_list同级', default='val_label.txt')

    parser.add_argument('--output', type=str, help='output pickle name, 默认存储在video_list同级', default='None')
    parser.add_argument('--out_pose_pkl', type=str, help='output pickle name, 默认存储在video_list同级', default='None')

    parser.add_argument('--VideoMod', type=str, default=False, help='视频测试集模式')
    parser.add_argument('--video_labs', type=str, help='the list of source videos',
                        default="/root/share175/sport_test/run_50M/classify_cheating/testsets/datasets_videos/all_label.txt")

    parser.add_argument('--TestMod', default=False, help='测试模式，生成pkl')
    parser.add_argument('--test_batch', default=4, help='测试时使用的Batch Size')
    parser.add_argument('--ann_file_pth', default='all_label_ann.pkl', help='测试时使用的大pkl')

    args = parser.parse_args()
    if isinstance(args.Add_dataMod, str):
        args.Add_dataMod = args.Add_dataMod.lower() == 'true'
    if isinstance(args.TestMod, str):
        args.TestMod = args.TestMod.lower() == 'true'  # True 或 true
    if isinstance(args.VideoMod, str):
        args.VideoMod = args.VideoMod.lower() == 'true'  # True 或 true
        args.TestMod = True if args.VideoMod else False  # 视频测试集下，默认使用测试模式
        args.video_list = args.video_labs

    if args.TestMod:
        args.slowonly_r50_sport_keypoint = \
            '/root/share175/sport_trains/run_50M/classify_cheating/mmaction2-main0401/mmaction2-main/configs/skeleton/posec3d/slowonly_r50_sport-keypoint.py'
        args.new_file_path = Path(args.video_list).parent / Path(args.slowonly_r50_sport_keypoint).name
        args.test_batch = int(args.test_batch)

    if not Path(args.output).is_file():
        args.output = Path(args.video_list).parent / 'all_label_ann.pkl'

    if not Path(args.out_pose_pkl).is_file():
        args.out_pose_pkl = Path(args.video_list).parent / 'trainval_yolopose.pkl'

    if args.VideoMod:
        args.ann_file_pth = Path(args.video_labs).parent / 'all_label_ann.pkl'      # 视频测试集 pkl


    args.tain_txt_path = Path(args.video_list).parent / args.tain_txt
    args.val_txt_path = Path(args.video_list).parent / args.val_txt

    return args


if __name__ == '__main__':
    args = parse_args()

    if args.TestMod:
        modify_config_file(file_path=args.slowonly_r50_sport_keypoint, target_var=['Test_ann_file', 'test_batch'],
                           new_value=[args.ann_file_pth, args.test_batch], new_file_path=args.new_file_path)
        print(f"@Moss: we fixed {args.slowonly_r50_sport_keypoint}:\n Test_ann_file become {args.output}.")

    # Step：生成大Pkl
    annotations = dump_all_label_ann(args)
    print('Generate all_label_ann.pkl', end=';')

    if not args.TestMod and (not args.VideoMod):
        # Step: 生成训练、验证的pkl
        dump_trainVal_ann(args, annotations)
        print('Generate trainval_yolopose.pkl success!')
