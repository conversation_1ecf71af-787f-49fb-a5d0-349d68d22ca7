#!/usr/bin/env bash
#set -x             # 开启调试模式, 会将sh中信息逐个打印
creator=Moss
Start_Time=$(date +%s)
TestMod=False           #
val_rate=1.0
script_pth="/root/share175/sport_trains/run_50M/classify_cheating/mmaction2-main0401/mmaction2-main/tools"
data_pth="/root/share175/sport_test/run_50M/classify_cheating/testsets/skl_points"
datasets_pth="/root/share175/sport_test/run_50M/classify_cheating/testsets/datasets"
video_list="$datasets_pth/all_label.txt"

# 视频测试集路径
VideoMod=True
video_pth="/root/share175/sport_test/run_50M/classify_cheating/testsets/videos"
datasets_vid="/root/share175/sport_test/run_50M/classify_cheating/testsets/datasets_videos"
video_labs="$datasets_vid/all_label.txt"


test_batch=8
#CUDA_DEVICES="4,5,6,7"        # 默认使用前4张GPU卡
CUDA_DEVICES="0,1,2,3"        # 默认使用前4张GPU卡
#CHECKPOINT="work_dirs/slowonly_r50_sport-keypoint/20250414_134405/best_acc_top1_epoch_24.pth"
#CHECKPOINT="work_dirs/slowonly_r50_sport-keypoint/20250415_133034/best_acc_top1_epoch_22.pth"     # 0415
#CHECKPOINT="work_dirs/slowonly_r50_sport-keypoint/20250416_121558/best_acc_top1_epoch_24.pth"     # 0417
#CHECKPOINT="work_dirs/slowonly_r50_sport-keypoint/20250417_123446/best_acc_top1_epoch_20.pth"     # 0418
#CHECKPOINT="work_dirs/slowonly_r50_sport-keypoint/20250421_120954/best_acc_top1_epoch_24.pth"     # 0421
#CHECKPOINT="work_dirs/slowonly_r50_sport-keypoint/20250422_115755/best_acc_top1_epoch_28.pth"     # 0422
#CHECKPOINT="work_dirs/slowonly_r50_sport-keypoint/20250423_120156/best_acc_top1_epoch_23.pth"     # 0423
#CHECKPOINT="work_dirs/slowonly_r50_sport-keypoint/20250424_145348/best_acc_top1_epoch_25.pth"     # 0424
CHECKPOINT="work_dirs/slowonly_r50_sport-keypoint/20250424_145348/epoch_28.pth"     # 0424
CHECKPOINT=$script_pth/$CHECKPOINT
if [ "$VideoMod" = True ]; then
  CONFIG="$datasets_vid/slowonly_r50_sport-keypoint.py"
else
  CONFIG="$script_pth/../configs/skeleton/posec3d/slowonly_r50_sport-keypoint.py"
fi


# 1 生成默认标签 0_default; 2 生成pkl
run50_pth="/root/share175/sport_trains/action_recongnition/base_skeleton/dataGeter_yoloPose/tools/run50/Generate_labels"
python $run50_pth/create_vidlabel_lst.py --TestMod ${TestMod} --data_pth $data_pth --video_pth ${video_pth}  --VideoMod ${VideoMod}
python $run50_pth/custom_yoloPose_extraction.py --TestMod ${TestMod} --video-list $video_list --Add-dataMod True --video_labs ${video_labs}  --VideoMod ${VideoMod}

End_Time_1=$(date +%s)
time1=$(((End_Time_1 - Start_Time) / 60))


# 3. 执行测试
CONFIG=$CONFIG CHECKPOINT=$CHECKPOINT CUDA_DEVICES=$CUDA_DEVICES \
bash "${script_pth}/dist_test.sh"


End_Time_2=$(date +%s)
time2=$(((End_Time_2 - End_Time_1) / 60))


echo "ProcessData Use Time $time1 min."
echo "Model test Use Time $time2 min."


# 4. Copy or Cut
#Method="Match"
#python Select_samples.py --datasets $datasets_pth --split $Method
