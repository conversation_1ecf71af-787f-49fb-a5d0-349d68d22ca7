# -*-coding:utf-8-*-
"""
统计视频测试集
   生成的 pred_gt_labels.txt 中，统计
            1. 视频准确率：视频m个，每个视频全正确区分，则无误判

"""
import argparse
import re
from pathlib import Path


def read_txtInfo(txt_pth):
    with open(txt_pth, 'r', encoding='utf-8') as fl:
        info = fl.readlines()

    info = [l.strip() for l in info if l != "\n"]


    return info


def parse_Vidlabels(data, split_label):
    vid_dict = {}
    for info in data:
        vid_name = Path(info).parents[1].name
        name = Path(info.split(',')[0]).stem
        Pos = int(re.findall(r'_P(\d+)_', name)[0])
        pred = int(Path(info.split(',')[1]).stem)
        gt = int(Path(info.split(',')[2]).stem)

        # 标签映射
        pred = next((k for k, v in split_label.items() if pred in v), pred)
        gt = next((k for k, v in split_label.items() if gt in v), gt)

        val = vid_dict.get(vid_name, None)
        if val is None:
            vid_dict[vid_name] = {Pos: [pred, gt]}
        else:
            vid_dict[vid_name].update({Pos: [pred,gt]})


    return vid_dict



if __name__ == '__main__':
    parser = argparse.ArgumentParser(prog='视频 标签统计')
    parser.add_argument('--datasets_pth', type=str,
                        default="/root/share175/sport_test/run_50M/classify_cheating/testsets/datasets_videos", help='训练-验证集 标签来源')
    parser.add_argument('--label_name', default="pred_gt_labels.txt", help='标签名在数据路径中，并与文件夹对应')

    opt = parser.parse_args()

    split_label = {0: [1], 1: [0, 2]}  # key-0:fake, key-1:normal

    opt.label_pth = Path(opt.datasets_pth) / opt.label_name

    # 读取标签，获取视频信息、真实标签、预测标签
    data = read_txtInfo(opt.label_pth)
    vid_dict = parse_Vidlabels(data, split_label)

    len_vids = len(vid_dict)
    num_allGt = 0
    for name, val_dict in vid_dict.items():
        all_Gt = next((False for val in val_dict.values() if not val[0] == val[1]), True)
        num_allGt += 1 if all_Gt else 0


    print(f"all Right Video Acc: {num_allGt / len_vids}")




