# -*-coding:utf-8-*-
"""
模型反推结果分析 Match
使用 测试后生成的txt 获取gt与pred不一致的异常样本
输入: pred_gt_labels.txt, [Match|Copy|Cut]
输出：
"""
import copy
import pathlib

from tqdm import tqdm
import argparse
from pathlib import Path


def read_txtInfo(txt_pth):
    with open(txt_pth, 'r', encoding='utf-8') as fl:
        info = fl.readlines()

    info = [l.strip() for l in info if l != "\n"]


    return info


def change_files(target_pth:Path, file_pth:Path, suffixes=None, method=None):
    if suffixes is None:
        suffixes = ['.jpg', '.pkl']
    for suffix in suffixes:
        file_pth = file_pth.with_suffix(suffix)
        try:  # 该方法在拷贝大文件时不是很友好
            if method in ['Cut', 'cut']:
                file_pth.replace(target_pth / file_pth.name)
            elif method in ['Copy', 'copy']:
                (target_pth / file_pth.name).write_bytes(file_pth.read_bytes())
        except Exception as e:
            print(f"异常数据:{e}, {file_pth}")


def change_samples(_Select, dst_pth, err_data, right_data, label_map):
    dst = Path(dst_pth) / Path(f"model_check_{_Select}")

    # err-data Copy
    for gt_lab, pkl_pres in tqdm(err_data.items()):
        if not len(pkl_pres):
            continue
        dst_lab = dst / 'err_datas' / f'{gt_lab}_{label_map[gt_lab]}'
        for pkl_pth, pred_lab, _score in pkl_pres:
            dst_gt_pred = dst_lab / f'{pred_lab}_pred'
            dst_gt_pred.mkdir(exist_ok=True, parents=True)
            change_files(target_pth=dst_gt_pred, file_pth=Path(pkl_pth), suffixes=['.pkl', '.jpg'], method=_Select)

    # right-data
    for gt_lab, pkl_pths in tqdm(right_data.items()):
        if not len(pkl_pths):
            continue
        dst_lab = dst / 'pre_is_gt' / f'{gt_lab}_{label_map[gt_lab]}'
        dst_lab.mkdir(exist_ok=True, parents=True)
        for pkl_pth, _pred_lab, _score in pkl_pths:
            change_files(target_pth=dst_lab, file_pth=Path(pkl_pth), suffixes=['.pkl', '.jpg'], method=_Select)


def match_samples(style_data, dst, label_map, score_thre):
    for gt_lab, pkl_pres in tqdm(style_data.items()):
        if not len(pkl_pres):
            continue
        for pkl_pth, pred_lab, score in pkl_pres:
            if score <= score_thre:
                pred_pth = dst / f'{pred_lab}_{label_map[pred_lab]}_pred' / 'weak_score'
            else:
                pred_pth = dst / f'{pred_lab}_{label_map[pred_lab]}_pred'
            pred_pth.mkdir(exist_ok=True, parents=True)
            change_files(target_pth=pred_pth, file_pth=Path(pkl_pth), suffixes=['.pkl', '.jpg'], method='Cut')



def file_select_move(_Select, all_datas, dst_pth, labels_name, score_thre):
    label_map = {lab.split('_')[0]: lab.strip().split('_')[1] for lab in labels_name}
    print('lab_map:', label_map)
    right_data, err_data = all_datas

    if _Select in ['Copy', 'copy', 'Cut', 'cut']:
        change_samples(_Select, dst_pth, err_data, right_data, label_map)

    elif _Select == 'Match':
        dst = Path(dst_pth) / Path(f"model_{_Select}")

        # 仅在Match时判断阈值，并将weak score sample 单独存储
        for style_data in [err_data, right_data]:
            match_samples(style_data, dst, label_map, score_thre)

    return



def main(opt, split):
    data = read_txtInfo(txt_pth=opt.label_pth)
    labels = read_txtInfo(txt_pth=opt.labels_pth)

    right_data = {str(lab):[] for lab in range(len(labels))}
    err_data = copy.deepcopy(right_data)
    for a_sample in tqdm(data):
        sample_info = a_sample.rsplit(', ')

        a_pkl, a_pred, a_label = sample_info[0], sample_info[1], sample_info[2]
        a_score = float(sample_info[-1].strip())          # gt_label
        # print(a_pkl, a_pred, a_label)

        if a_label == a_pred:
            right_data[a_label].append([a_pkl, a_pred, a_score])
        else:
            # 以下 处理模型判错的数据
            err_data[a_label].append([a_pkl, a_pred, a_score])

    file_select_move(_Select=split, all_datas=[right_data, err_data],
                     dst_pth=opt.datasets, labels_name=labels,
                     score_thre=opt.score_thre)


    return



if __name__ == '__main__':
    parser = argparse.ArgumentParser(prog='训练-验证标签生成')
    parser.add_argument('--datasets', type=str,
                        # default="/root/share175/sport_datas/run_50M/classify_cheating/train/skl_points/datasets", help='样本路径')
                        default="/root/share175/sport_test/run_50M/classify_cheating/testsets/skl_points/datasets_0", help='样本路径')
    parser.add_argument('--label_name', default="pred_gt_labels.txt", help='标签名在数据路径中，并与文件夹对应')

    parser.add_argument('--labels_pth', default="/root/share175/sport_datas/run_50M/classify_cheating/train/skl_points/labels.txt", help='默认用训练数据集的标签路径，只读')

    # TODO: 测试模式 看清楚！Match 要慎重 不可逆！！
    parser.add_argument('--split', default='Copy', help='测试模式：testset，detect,Match,predict')
    parser.add_argument('--score_thre', default=0.65, help='若得分<=阈值,保存特殊标签')

    opt = parser.parse_args()

    opt.label_pth = Path(opt.datasets) / opt.label_name
    opt.score_thre = float(opt.score_thre)

    main(opt, opt.split)

