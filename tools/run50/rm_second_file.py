# -*-coding:utf-8-*-
"""
删除!!! 数据中的second样本
要求路径结构为：
path_jpg / 文件夹 / mp4文件和样本
"""
from pathlib import Path


path_jpg = f"/root/share175/sport_datas/run_50M/classify_cheating/ori_vids/武昌区滨江实验第一小学_Grade_Grade_Grade/videos"


dir_lst = list(dir for dir in Path(path_jpg).glob('*') if dir.is_dir())

for dir in dir_lst:
    mp4_file = list(dir.rglob('*.mp4'))
    if len(mp4_file) <=1:
        continue
    sec_file = list(f for f in dir.rglob('*.pkl') if 'second' in str(f))
    sec_file_jpg = list(f for f in dir.rglob('*.jpg') if 'second' in str(f))
    for pkl, jpg in zip(sec_file, sec_file_jpg):
        pkl.unlink()
        jpg.unlink()
        print(pkl.name)


