# -*-coding:utf-8-*-
"""
分析现网C++生成的
* 逻辑上符合要求->出成绩的 XML文件
* 每个xml代表1个逻辑有效的过线轨迹
* XML解析：
    第1个值是过线的跑道(从0开始)， 第2,3个值是过线跑道所在起点线跑道的中心坐标(x0, y0)，
    剩下的值 = (x,y,c)*17 * time
"""
from pathlib import Path


# 0
def move_mp4file(file: Path, dst: Path):
    try:
        file.rename(dst)
    except:
        print(f"@Moss:No Mp4 file: {file}")
        return


# 1. 根据xml名称匹配对应视频
def Xml_MatchVid(xmlpth: Path = None, videopth: Path = None):
    xmls = list(xmlpth.rglob('*.xml'))
    vids = list(videopth.rglob('*.mp4'))
    for a_xml in xmls:
        name = a_xml.stem.split('PoseC3dCheat_')[1].split('_')[:5]
        name = '_'.join(name)
        for a_vid in vids:
            if name in a_vid.stem:
                dst = a_xml.parent / a_vid.name
                file = a_vid
                move_mp4file(file, dst)
    return


if __name__ == '__main__':
    xmls = '/root/persons/ai_group/Moss/Question/合肥45中数据/debet'
    vids = Path(xmls).parent / 'videos'

    Xml_MatchVid(xmlpth=Path(xmls), videopth=vids)
