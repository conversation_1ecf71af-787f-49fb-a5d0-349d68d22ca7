# -*-coding:utf-8-*-
"""
样本采集后使用：给生成好的样本(pkl、jpg)文件分个组
"""


from pathlib import Path



dir_path = \
f"/root/share175/sport_test/run_50M/classify_cheating/testsets/1_94_中国科大附中高新中学"
# f"/root/share175/sport_test/run_50M/classify_cheating/testsets/videos/50中/2025_50z_05"

assert Path(dir_path).is_dir(), f"@Moss need a dir path"


name_part = set()
for s in list(Path(dir_path).rglob('*.jpg')):
    if s.name.startswith('endCam'):
        continue

    name = s.stem.split('_')
    file_name_part = '_'.join(name[:6])
    sample_dir = Path(dir_path) / file_name_part

    if file_name_part not in name_part:
        sample_dir.mkdir(exist_ok=True)
        name_part.add(file_name_part)

    s.rename(sample_dir / s.name)

    s_pkl = s.with_suffix('.pkl')
    if s_pkl.exists():
        s_pkl.rename(sample_dir / s.with_suffix('.pkl').name)

    print(s.name)


# 将mp4视频也对应放到 样本文件夹中
for f_mp4 in list(Path(dir_path).rglob('*.mp4')):
    file_name_part = '_'.join(f_mp4.stem.split('_')[:6])
    if file_name_part in name_part:
        try:
            f_mp4.rename(f_mp4.parent / file_name_part / f_mp4.name)
        except:
            continue
    print(f_mp4.name)




