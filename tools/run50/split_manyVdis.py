# -*-coding:utf-8-*-
"""
将路径中的 视频 划分到多个文件夹中，
                            便于多个终端同时执行

"""
import math
from pathlib import Path


def split_manyVids(vids_pth:str, split_num:int):
    """
    从vids_pth路径下将所有的mp4视频，进行分组
    vids_pth: 原始视频路径
    split_num: 每？个mp4视频分到1个子文件夹下

    return
    """
    vids_pth = Path(vids_pth)
    split_num = int(split_num)
    vids_lst = list(vids_pth.rglob('*.mp4'))
    total_files = len(vids_lst)

    print(f"all MP4 file num is {total_files}")
    #
    if total_files < split_num:
        print(f"total_files < set split_num:{split_num}, do not group")
        return
    # 计算需要创建的文件夹数量
    n_folders = math.ceil(total_files / split_num)
    folder_pths = [vids_pth / f"{i+1:03d}" for i in range(n_folders)]    # 格式化 001, 002...
    for f in folder_pths:
        f.mkdir(exist_ok=True)

    for i, file in enumerate(vids_lst):
        target_folder = folder_pths[i // split_num]
        target_pth = target_folder / file.name
        file.rename(target_pth)
        print(f"Moved {file.name}.")





if __name__ == '__main__':

    ori_vid_path = f"/root/share175/sport_datas/run_50M/classify_cheating/ori_vids/郑州航空学院_Grade_Grade_Grade"
    split_manyVids(vids_pth=ori_vid_path, split_num=500)


