# -*-coding:utf-8-*-
"""
修订 标签的定义
新增9_bias 偏移修正类别，特征是在A道起跑，在B道过线
"""
import re
import pickle
from pathlib import Path


def read_file_pkl(pkl_name:str) -> dict:
    """读取pkl信息"""
    if not Path(pkl_name).exists():
        return {}
    with open(pkl_name, 'rb') as fi:
        data = pickle.load(fi)
    return data



def fix_normal_labels():
    path_label = "/root/share175/sport_test/run_50M/classify_cheating/testsets/skl_points/0_normal"  # 大类路径 Normal

    all_scls = list(scl for scl in Path(path_label).glob("*") if scl.is_dir())

    for scl in all_scls:
        scl_samples = list(sample for sample in scl.rglob('*.pkl'))

        for sample in scl_samples:
            data = read_file_pkl(str(sample))
            # print(data)
            start_pt = data.get('start_pt')
            P12sP123e = data.get('P12sP123e')
            P1e = P12sP123e[2]

            name = data.get('sample_name', '')
            EndPos = int(re.search(r'_P([\d]+)_', name).group(1))  # 终点过线道

            box = next((x for x in data['pred_skpts'] if x is not None), None)[0][:4]
            c_box = (box[0] + box[2]) / 2
            # (P1e[EndPos][0] + P1e[EndPos-1][0]) / 2
            if min(P1e[EndPos][0], P1e[EndPos - 1][0]) <= c_box <= max(P1e[EndPos][0], P1e[EndPos - 1][0]):
                continue
            else:
                dst = sample.parents[1] / (sample.parent.name + '_check')
                dst.mkdir(exist_ok=True, parents=True)
                label = sample.with_suffix('.jpg')
                sample.rename(dst / sample.name)
                label.rename(dst / label.name)
                print(sample.name)



def fix_anomaly_labels():
    path_label = "/root/share175/sport_test/run_50M/classify_cheating/testsets/skl_points/2_anomaly"  # 大类路径

    all_scls = list(scl for scl in Path(path_label).glob("*") if scl.is_dir())

    for scl in all_scls:
        scl_samples = list(sample for sample in scl.rglob('*.pkl'))

        for sample in scl_samples:
            data = read_file_pkl(str(sample))

            Start_run = data.get('Start_run')
            if not len(Start_run):
                continue
            vals = next((x for x in Start_run.values() if x is not None), None)

            if vals is not None:
                dst = sample.parents[1] / (sample.parent.name + '_check')
                dst.mkdir(exist_ok=True, parents=True)
                label = sample.with_suffix('.jpg')
                sample.rename(dst / sample.name)
                label.rename(dst / label.name)
                print(sample.name)





if __name__ == '__main__':
    # fix_normal_labels()
    fix_anomaly_labels()
