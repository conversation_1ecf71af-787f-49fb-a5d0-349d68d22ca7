# -*-coding:utf-8-*-
import pickle
import shutil
from pathlib import Path

import bisect


def Move_SampleWith_StrDir(dst_file, a_pkl, DirName):
    (dst_file / f'PerNum_{DirName}').mkdir(exist_ok=True)
    shutil.move(str(a_pkl), str(dst_file / f'PerNum_{DirName}' / a_pkl.name))
    a_label = a_pkl.with_suffix('.jpg')
    if a_label.exists():
        shutil.move(str(a_label), str(dst_file / f'PerNum_{DirName}' / a_label.name))


def read_pkls(folder_path, thres_dx=40, thres_dy=110, pers_thres=[15, 20, 25, 35, 50], rate=0.66):
    folder_path = Path(folder_path)
    name = folder_path.name
    log_file = folder_path.parent / f'{name}_Position_dxy_log.txt'
    dst_file = folder_path.parent / f'{name}_PosFilted'
    # dst_file.mkdir(exist_ok=True)
    (dst_file / 'dx').mkdir(exist_ok=True, parents=True)
    (dst_file / 'dy').mkdir(exist_ok=True)

    process_sub_folder(folder_path, log_file, dst_file, thres_dx, thres_dy, pers_thres, rate)

    return


def process_sub_folder(sub_folder, log_file, dst_file, thres_dx=40, thres_dy=110, pers_thres=[15, 20, 25, 35, 50], rate=0.66):
    all_pkls = list(sub_folder.rglob('*.pkl'))

    for a_pkl in all_pkls:
        with open(a_pkl, 'rb') as fi:
            data = pickle.load(fi)

        if data.get('start_pt',None) is None:
            print(f'add start_pt in {a_pkl.name}')
            P1s, P2s, P1e, P2e, P3e = data['P12sP123e']
            pos = int(data['sample_name'].rsplit('_P')[-1].split('_')[0])
            start_pt = (P1e[pos][0] + P1e[pos + 1][0]) * 0.5, (P1e[pos][1] + P1e[pos + 1][1]) * 0.5
            data['start_pt'] = start_pt
            with open(a_pkl, 'wb') as fw:
                pickle.dump(data, fw)
        else:
            print(f'{a_pkl.name}')


        sample_name, dx, dy = position_logic_dxy(data, log_file, thres_dx, thres_dy)

        thres_num = personNum_statistic(data, pers_thres)


        BigPerson = Area_statistic(data, rate)



        if sample_name:
            if any([dx, dy]) and BigPerson:
                Move_SampleWith_StrDir(dst_file, a_pkl, DirName=BigPerson)
                continue

            if any([dx, dy]) and thres_num:
                Move_SampleWith_StrDir(dst_file, a_pkl, DirName=thres_num)
                continue

            if dx:
                shutil.move(str(a_pkl), str(dst_file / 'dx' / a_pkl.name))
                a_label = a_pkl.with_suffix('.jpg')
                if a_label.exists():
                    shutil.move(str(a_label), str(dst_file / 'dx' / a_label.name))
            elif dy:
                shutil.move(str(a_pkl), str(dst_file / 'dy' / a_pkl.name))
                a_label = a_pkl.with_suffix('.jpg')
                if a_label.exists():
                    shutil.move(str(a_label), str(dst_file / 'dy' /a_label.name))



    return


def position_logic_dxy(data, log_file, thres_dx=40, thres_dy=110):
    keypoint_lst = data.get('pred_skpts')
    start_pt = data.get('start_pt')         # 过线道的起点中心

    first_skpts = next((x for x in keypoint_lst if x is not None), None)[1]         # 跟踪首帧
    foot_c = tuple(((first_skpts[:, 15] + first_skpts[:, 16]) * 0.5)[0][:2].numpy())
    # 计算y方向上的距离差
    delt_y = foot_c[1] - start_pt[1]
    # 计算x方向上的距离差
    delt_x = abs(foot_c[0] - start_pt[0])

    sample_name = data.get('sample_name', '')

    print(sample_name, int(delt_x), int(delt_y), '.')
    dy = delt_y >= thres_dx
    dx = delt_x > thres_dy
    if dx or dy:
        with open(log_file, 'a+', encoding='utf-8') as flog:
            flog.write(f'{sample_name}:{delt_x},{delt_y}\n')
        return sample_name, dx, dx

    return False, dx, dy


def find_interval(num, thres_lst):
    """
    2分法 查找数字所在区间
    """
    index = bisect.bisect_right(thres_lst, num)
    if index == 0:
        # return thres_lst[0]
        return False        # 小于最小阈值, 不做区分

    elif index >= len(thres_lst):
        return thres_lst[-1]
    else:
        return thres_lst[index-1]


def personNum_statistic(data, pers_thres):
    dets_dict = data.get('all_dets')
    start = data.get('EndCam_starFrame', 0)
    len = data.get('total_frames', 0)
    f_id = start + len - 1

    pers = dets_dict.get(f_id, [])
    num = pers.shape[0]

    # 判断人数介于哪个阈值区间
    thre_num = find_interval(num, pers_thres)
    print(num, 'belong2', thre_num)

    return thre_num


def Area_statistic(data, rate=0.65):
    """
    统计人体框的高度，与 标定起点-终点直接的高度差
    * Person-Heigh >= 2/3 * 标定Heigh[End-Start]
    * 过线帧人体框的一半box_yc >= 终点标定线
    """
    preds = data.get('pred_skpts')
    markPt = data.get('P12sP123e')
    num_run = len(markPt[-1]) - 1
    mark_start, mark_end = markPt[2][num_run//2], markPt[-1][num_run//2]
    mark_dy = mark_end[1] - mark_start[1]
    last_pred = next((item for item in reversed(preds) if item is not None), None)
    if last_pred is not None:
        delt_y = (last_pred[0][3] - last_pred[0][1])
    else:
        delt_y = 1080 // 2

    box_yc = (last_pred[0][1] + last_pred[0][3]) * 0.5
    overLine_More = box_yc >= mark_end[1]
    BigPerson_H = mark_dy * rate <= delt_y

    if overLine_More or BigPerson_H:
        print('Rel rate:', delt_y / mark_dy)
        return 'BigPerson'


    return False



if __name__ == "__main__":
    # 设置文件夹A的路径, 用 阈值筛选 负样本,
    # * 将负样本中，人数超过阈值 的数据单独存放
    # * 将负样本中，过线人体框高度 / 起点终点标定高度 > rate 或 过线帧人体框的一半高度在终点线以外 的样本认为是 PerNum_BigPerson
    folder_path = "   "  # 替换为你的文件夹A的实际路径


    read_pkls(folder_path, thres_dx=40, thres_dy=110, pers_thres=[15, 20, 25, 35, 50], rate=0.66)

    print(folder_path, 'okk.')

