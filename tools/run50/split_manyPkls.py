# -*-coding:utf-8-*-
"""
将筛选后，生成的很多负样本进行分组
    1个样本包含1对文件，1个pkl和1个jpg
"""
import math
from pathlib import Path


def split_manyPkls(vids_pth:str, split_num:int):
    """
    从vids_pth路径下将所有的mp4视频，进行分组
    vids_pth: 原始视频路径
    split_num: 每？个mp4视频分到1个子文件夹下

    return
    """
    vids_pth = Path(vids_pth)
    split_num = int(split_num)
    vids_lst = list(vids_pth.rglob('*.pkl'))
    total_files = len(vids_lst)


    #
    if total_files < split_num:
        print(f"total_files < set split_num:{split_num}, do not group")
        return
    # 计算需要创建的文件夹数量
    n_folders = math.ceil(total_files / split_num)
    print(f"all pkl file num is {total_files}, need created dir num is:{n_folders}")
    folder_pths = [vids_pth / f"{i+1:03d}" for i in range(n_folders)]    # 格式化 001, 002...
    for f in folder_pths:
        f.mkdir(exist_ok=True)

    for i, file in enumerate(vids_lst):
        target_folder = folder_pths[i // split_num]
        jpg_file = file.with_suffix('.jpg')
        target_pth = target_folder / file.name
        label_pth = target_folder / jpg_file.name
        file.rename(target_pth)
        jpg_file.rename(label_pth)
        print(f"Moved {file.name}.")





if __name__ == '__main__':

    samples_path = f"/root/share175/sport_datas/run_50M/classify_cheating/ori_vids/西安莲湖远东实验小学/西安市莲湖区远东实验小学_Grade_Grade_Grade_PosFilted/dx"
    split_manyPkls(vids_pth=samples_path, split_num=1000)