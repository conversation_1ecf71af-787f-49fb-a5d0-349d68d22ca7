# -*-coding:utf-8-*-
import pickle
from pathlib import Path


def mmengine_dump(obj, file_path, **kwargs):
    """
    参考mmengine.dump(anno, out) 将列表信息保存成pkl
    obj： 要序列化的对象
    file_path: 输出文件夹路径
    **kwargs: 传递给pickle.dump的额外参数
    """
    kwargs.setdefault('protocol', 2)        # 设置兼容老版本python
    with open(file_path, 'wb') as file:
        pickle.dump(obj, file, **kwargs)

    return


def read_file_pkl(pkl_name:str) -> dict:
    """读取pkl信息"""
    if not Path(pkl_name).exists():
        return {}
    with open(pkl_name, 'rb') as fi:
        data = pickle.load(fi)
    return data


path = f'/root/share175/sport_datas/run_50M/classify_cheating/ori_vids/合肥市五十中学天鹅湖教育集团潜山路校区_Grade_Grade_Grade/test2'


pkl_pth = [pkl for pkl in Path(path).rglob('*.pkl')]

for pkl_name in pkl_pth:
    data = read_file_pkl(pkl_name)
    print(data)