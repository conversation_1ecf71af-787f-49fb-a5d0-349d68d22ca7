# -*-coding:utf-8-*-
"""
引体向上 将pkl拷贝至txt路径
"""
import shutil
from pathlib import Path



new_pkl = f"/root/share175/sport_datas/pull_up/classify_cheating/ori_videos/Jiangsu_nanjingjinlingzhongxuehexifenxiao_2/PKL"


old_txt = f"/root/share175/sport_datas/pull_up/classify_cheating/train/videos"


all_txt = [txt for txt in list(Path(old_txt).rglob('*.txt'))]
all_pkl = [pkl for pkl in list(Path(new_pkl).rglob('*.pkl'))]

for txt_pth in all_txt:
    for pkl_pth in all_pkl:
        if txt_pth.stem in str(pkl_pth):
            src = str(pkl_pth)
            dst = txt_pth.parent.__str__()
            shutil.copy(src, dst)

