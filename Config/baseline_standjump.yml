# basic config information  for all proj action classify based on original video
# 立定跳远触发
creator: Moss
task: classify
proj_name: stand_jump

videos_pth: /root/share175/sport_datas/action_recongnition/base_skeleton/train/standjump_videos/ori_vids/Changsha_liuyangdaowu/2024_0527              # 原始视频路径
save_dir:  /root/share175/sport_datas/action_recongnition/base_skeleton/train/standjump_videos/ori_vids/sample_Changsha_liuyangdaowu/2024_0527          # 存储pkl 样本路径
mark_file:  Config/mark_standjump/Changsha_liuyangdaowu_A.json                                                                                       # 标定文件
model_info:
  yolo_pose: /root/share175/sport_models/stand_jump/detect_person_pose/detect_person_pose_standjump_20240524_V6_768_448.onnx
  infer_size: [448, 768]




# 立定跳远触发项目
stand_jump:
  num_bodys: 1             # 单人姿态
  total_frames: 50        # 采集样本持续的帧数
  interval_frames: 25     # 最小采集的间隔帧 间隔1秒
  sample_rate: 2          # 采样间隔帧
  len_sample: 25            # 样本长度 = total_frames / sample_rate


