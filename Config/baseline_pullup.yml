# basic config information  for all proj action classify based on original video
# 引体向上作弊
creator: Moss
task: detect
proj_name: pull_up

#videos_pth: pullup_data/video        # 原始视频路径
#videos_pth: /root/share175/sport_datas/pull_up/classify_cheating/train/videos/0_normal/Zhejiang_hangzhouliangzhudi1zhongxue/C_2024_0920_5_backgroundSideHasPerson  # 原始视频路径
#videos_pth: /root/share175/sport_test/pull_up/classify_cheating/videos/6_side_two_hugging/Neimenggu_eerduosishidi1zhongxueyijinhuoluoqixiaoqu/C_2024_1013_6_backgroundSideHasPerson # 原始视频路径
#videos_pth: /root/share175/sport_datas/pull_up/classify_cheating/ori_videos/Dongyuan_qinghuagongxuexiao_2 # 原始视频路径 1
#videos_pth: /root/share175/sport_datas/pull_up_移除/classify_cheating/ori_videos/test6 # 原始视频路径 2
#videos_pth: Z:\sport_datas\pull_up\classify_cheating\ori_videos\Zhejiang_jiaotongzhiyejishuxueyuan_1 # 原始视频路径
videos_pth: /root/persons/ai_group/Andy/sport_datas_all/PullAndUp/cheak_videos/45zhong/pullUp/PullAndUp_faceTest_/2025_7_10     # 原始视频路径
#videos_pth: /root/persons/ai_group/Ronson/AA_TYdata/shenzhen_lianhua_nan       # 原始视频路径
#videos_pth: /root/share175/sport_datas/pull_up/classify_cheating/ori_videos/badStandard/Neimenggu_eerduosibashi1zhong  # 不符合规范 原始视频路径
#videos_pth: /root/persons/ai_group/Xing/数据二组/引体向上/引体向上 作弊分类/测试验证/2024_0827/collection # 原始视频路径
#videos_pth: /home/<USER>/share175/sport_datas/pull_up/classify_cheating/train/videos/0_normal/Jiangsu_nanjingjinglinzhongxuehexifenxiao/A_2024_0329_1 # 原始视频路径

#如果为空，输出当前视频同一级目录
save_dir: default    # 存储pkl 样本路径 pullup_data/videos_clip_out
#save_dir: videos_clip_out        # 存储pkl 样本路径 pullup_data/videos_clip_out


#videos_clip_out :
videos_clip_out : videos_clip_out

# 原始图像大小
vid_ori_width : 1920
# 裁剪后，长宽相等的宽大小
vid_crop_width : 1080
#裁剪的高度缩放比例，底部过短，就是1.0,45中底部高度大，缩放为0.9
vid_crop_height_precent : 1.0

model_info:
  yolo_detect: /root/share175/sport_models/pull_up/detect_hand_head/PullUp_hand_head_20250324_V31_768_768.onnx
  infer_size: [768, 768]


# 引体向上作弊项目
pullup:
  num_bodys: 1             # 单人姿态
  total_frames: 50        # 采集样本持续的帧数
  interval_frames: 25     # 最小采集的间隔帧 间隔1秒
  sample_rate: 2          # 采样间隔帧
  len_sample: 25            # 样本长度 = total_frames / sample_rate

pullup_detcls:
  hand: 0 #正手
  backhand: 1 #反手
  head: 2 #头


