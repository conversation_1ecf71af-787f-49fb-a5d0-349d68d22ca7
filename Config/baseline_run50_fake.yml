# basic config information  for all proj action classify based on original video
# 50m 防作弊|违规 数据采集配置
creator: Moss       # 用于场景理解
task: classify
proj_name: fake_run50
devices: [0,1,2,3,4,5,6,7]


#videos_pth: /root/share175/sport_datas/run_50M/classify_cheating/ori_vids/鄂尔多斯市一中伊金霍洛分校/Run50_freeTest_freeTest_2024      # 原始视频路径
videos_pth: /root/share175/sport_datas/sit_up/classify_cheating/ori_vids_fusion/襄阳市谷城县城关镇第三初级中学/tmp        # 原始视频路径
#videos_pth: /root/share175/sport_datas/run_50M/classify_cheating/ori_vids/合肥市五十中学天鹅湖教育集团潜山路校区_Grade_Grade_Grade/test2        # 原始视频路径

Used_vid_txt: default     # 会在 videos_pth / 视频路径名 文件中，记录处理的视频信息

mark_file: Config/mark_run50/Jiangsu_nanjing12zhongxue_204_6.json False
mark_file2: None
mark_end: Config/mark_run50/Jiangsu_nanjing12zhongxue_207_6.json False

save_dir:  default        # 存储pkl 样本路径 默认修改到videos_pth路径下# 标定文件

model_info:
  trt_infer: False           # True: 使用tensorrt推理[模型路径为onnx时自动转换成engine];
  yolo_pose: /root/share175/sport_models/run_50M/detect_person_pose/Run50_100_detect_person_pose_20250314_V17_768_448.onnx
  infer_size: [448, 768]




