# basic config information  for all proj action classify based on original video
# 立定跳远 作弊|违规 数据采集配置
creator: Moss       # 用于场景理解
task: classify
proj_name: fake_stand_jump

#videos_pth: /root/persons/ai_group/Andy/现网反馈问题数据/stand_jump/168玫瑰园东区          # 原始视频路径
#videos_pth: Y:\ai_group\Pillar\现网问题反馈\standJump\作弊问题\1_169_合肥50中天鹅湖教育集团潜山路校区
videos_pth: /root/persons/ai_group/Andy/sport_datas_all/Stand_jump/cheak_videos/zhuhaisanzaozhen/203_1080p
#videos_pth: /root/persons/ai_group/Pillar/现网问题反馈/standJump/作弊问题/1_169_合肥50中天鹅湖教育集团潜山路校区
#videos_pth: /root/persons/ai_group/Lumi/视频测试集/立定跳远/待测试

mark_file:  Config/mark_standjump/zhuhai_sanzaozhenxiaoxue_203.json
#mark_file:  D:\autocollectdatamanager-master-AutoCollectDataManagerVer2\dataGeter_yoloPose\Config\mark_standjump\zhuhaisanzaizhen_106.json
#mark_file:  Config/mark_standjump/0/5/rtspAadminAyskj12345B192.168.2.101_1.json

save_dir:  default        # 存储pkl 样本路径 默认修改到videos_pth路径下# 标定文件
model_info:
#  yolo_pose: /root/share175/sport_models/stand_jump/detect_person_pose/detect_person_pose_standjump_20241109_V8_768_448.onnx
#  yolo_pose: /root/share175/sport_models/stand_jump/detect_person_pose/detect_person_pose_standjump_20250408_V9_768_448.onnx
#  yolo_pose: Z:\sport_models\stand_jump\detect_person_pose\detect_person_pose_standjump_20250519_V11_768_448.onnx
  yolo_pose: /root/share175/sport_models/stand_jump/detect_person_pose/detect_person_pose_standjump_20250519_V11_768_448.onnx
  infer_size: [448, 768]
#  det_foot: /root/share175/sport_models/stand_jump/detect_foot/stand_jumpt_detect_foot_20240807_v34_736_416.onnx
#  det_foot: /root/share175/sport_models/stand_jump/detect_foot/stand_jumpt_detect_foot_20241123_v35_736_416.onnx
#  det_foot: Z:\sport_models\stand_jump\detect_foot\stand_jumpt_detect_foot_20250521_v37_736_416.onnx
  det_foot: /root/share175/sport_models/stand_jump/detect_foot/stand_jumpt_detect_foot_20250521_v37_736_416.onnx
  foot_size: [416, 736]
#  classify_hand_hold: '/root/share175/sport_trains/stand_jump/classification_hand_hold_jump_efficientnetV2/20241226_run/weights/best.onnx'
#  classify_hand_hold: '/root/share175/sport_models/stand_jump/classification_hand_hold_jump_efficientnetV2/Standjump_hand_hold_jump_20241203_V2_384_384.onnx'
#  classify_hand_hold: '/root/share175/sport_models/stand_jump/classification_hand_hold_jump_efficientnetV2/Standjump_hand_hold_jump_20241228_V6_384_384.onnx'
  classify_hand_hold: '/root/share175/sport_models/stand_jump/classification_hand_hold_jump_efficientnetV2/Standjump_hand_hold_jump_20250519_V7_384_384.onnx'
  hand_hold_size: [320, 384]







# 立定跳远触发项目
fake_stand_jump:
  num_bodys: 1             # 单人姿态
  act_frames: 60            # 离开准备区域的动作帧  B sampleds
  len_sample: 75            # 样本长度 = A(准备帧) + B(运动帧)


