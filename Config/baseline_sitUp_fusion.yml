# basic config information  for all proj action classify based on original video
# 仰卧起坐 作弊|违规 数据采集配置
creator: Moss
task: classify
proj_name: fusion_sit_up


devices: [0,1,2,3,4,5,6,7] #  [0,1,2,3, 4,5,6,7  ]
batch_Mod: True
save_oriImg: False            # 是否保存原始视频.mp4 False
#videos_pth: /root/share175/sport_datas/sit_up/classify_cheating/ori_vids_fusion/Zhenzhou_hangtianhangkongxueyuan/SitAndUp_faceAutoTest        # 原始视频路径
videos_pth: /root/persons/ai_group/Pillar/现网问题反馈/sitUp/作弊误判/1_hefei_45zhong_点偏移作弊误检/videos       # 原始视频路径
#videos_pth: /root/persons/ai_group/Moss/Question/测试_长沙taiyu
#videos_pth: /root/persons/ai_group/Moss/Question/logic_fix/okk/2024_10_18_11_13_15_192.168.2.128_1_3_D.mp4

box_scale_thres: 1.5          # 需要严格满足: 人体框 W > 1.5 * H
save_dir:  default        # 存储pkl 样本路径 默认修改到videos_pth路径下# 标定文件
model_info:
  trt_infer: True           # True: 使用tensorrt推理[模型路径为onnx时自动转换成engine];
  yolo_pose: /root/share175/sport_models/sit_and_reach/detect_person_pose/detect_person_pose_situp_sitandreach_20250508_V41_768_448.onnx
  infer_size: [448, 768]

fusion_sit_up:
  num_bodys: 2             # 最大疑似 作弊嫌疑人 数量
  len_sample: 35            # 最大样本帧数
  min_len_sample: 25        # 最短样本长度


# 预设类别索引
pred_labels:
  0: normal
  1: backside_push
  2: hand_pull
  3: violate_rule
  4: single_person
  5: lookers_nearby

