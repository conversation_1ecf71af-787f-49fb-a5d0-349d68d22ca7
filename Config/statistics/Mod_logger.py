# -*-coding:utf-8-*-
# Copyright (c) 2020 PaddlePaddle Authors. All Rights Reserved.

import logging
import random
import time
import sys
from pathlib import Path



__all__ = ['init_logger']      # 限制使用 from logger import * 仅能导入__all__中指定的对象


# TODO become a class Logger for use @


logger_initialized = {}


def _color_format():
    """
    Use color log have a Question:
        Must open log file by Notepad++ or VSCode TO avoid see [32m color Coding
    """
    import colorlog
    log_colors_config = {'DEBUG': 'white', 'INFO': 'green',
                         'WARNING': 'yellow', 'ERROR': 'red', 'CRITICAL': 'bold_red'}

    formatter = colorlog.ColoredFormatter(fmt='%(log_color)s [%(asctime)s] %(name)s %(levelname)s: %(message)s',
                                          datefmt='%Y-%m-%d %H:%M:%S',
                                          log_colors=log_colors_config)
    return formatter



def init_logger(name='pylts', log_dir=None, log_level=logging.INFO, file_mode='a', use_colorlog=False):
    """
    Initialize logger and set its verbosity level to INFO.
    Args:
        name(str): Logger name. should append in logger_initialized
        file_mode(str): The file mode used in opening log file. Defaults to 'w'.
        log_level: The logger level. Only the process of rank 0 is affected
        log_dir (str): a file name or a directory to save log. If None, will not save log file.
        use_colorlog: if use colorlog for show colorful log Info and write in logs.

    Returns:
        logging.Logger: a logger
    """
    if log_dir is None:
        log_dir = str(Path(__file__).parents[3] / Path('logs'))

    _logger = logging.getLogger(name)
    # e.g., _logger "one" is initialized, then _logger "one.sub" will skip the initialization since it is a child of "one".
    for logger_name in logger_initialized:
        if name.startswith(logger_name):
            return _logger

    if use_colorlog:
        formatter = _color_format()
    else:
        formatter = logging.Formatter(fmt='[%(asctime)s] %(name)s %(levelname)s: %(message)s',
                                  datefmt="%Y/%m/%d %H:%M:%S")

    # handle duplicate logs to the console
    # Starting in 1.8.0, PyTorch DDP attaches a StreamHandler <stderr> (NOTSET)  to the root _logger.
    for handler in _logger.root.handlers:
        if type(handler) is logging.StreamHandler:  # for DDP
            handler.setLevel(logging.ERROR)

    stream_handler = logging.StreamHandler(stream=sys.stdout)
    handlers = [stream_handler]

    # only rank 0 will add a FileHandler
    rank = 0
    if rank == 0 and log_dir is not None:  # Thus, we provide an interface to change the file mode to the default behaviour.
        Path(log_dir).mkdir(exist_ok=True)  # assert the dir of logfile is exist
        timestamp = time.strftime('%Y%m%d_%H%M%S', time.localtime())
        log_file = Path(log_dir) / Path(f'{timestamp}{random.randint(1,9)}.log')
        handlers.append(logging.FileHandler(log_file, file_mode, encoding='utf-8'))  # file_mode 'w' or 'a'

    # for all the logger here
    for handler in handlers:
        handler.setFormatter(formatter)  # formatter (add color)
        handler.setLevel(log_level)
        _logger.addHandler(handler)  # 将logger添加到handle中

    # The _logger level.
    if rank == 0:
        _logger.setLevel(log_level)  # only the process of rank 0 is affected,
    else:
        _logger.setLevel(logging.ERROR)  # and other processes will set the level to "Error"

    logger_initialized[name] = True

    return _logger



def print_log(msg, logger=None, level=logging.INFO):
    """
    TODO: the follows from mmcv.utils.logging.py
    Print a log message.

    Args:
        msg (str): The message to be logged.
        logger (logging.Logger | str | None): The logger to be used.
            Some special loggers are:
            - "silent": no message will be printed.
            - other str: the logger obtained with `get_root_logger(logger)`.
            - None: The `print()` method will be used to print log messages.
        level (int): Logging level. Only available when `logger` is a Logger
            object or "root".
    """
    if logger is None:
        print(msg)
    elif isinstance(logger, logging.Logger):
        logger.log(level, msg)
    elif logger == 'silent':
        pass
    elif isinstance(logger, str):
        _logger = init_logger(logger)
        _logger.log(level, msg)
    else:
        raise TypeError(
            'logger should be either a logging.Logger object, str, '
            f'"silent" or None, but got {type(logger)}')




if __name__ == '__main__':
    # init _logger before other steps
    work_dir = '../../../logs'

    logger = init_logger(log_dir=work_dir, log_level=logging.INFO)      # logging.INFO, WARNING...

    logger.info('train')        # critical, info, warning, error

    logger = init_logger(name='new', log_dir=work_dir+'/shape', log_level=logging.INFO)  # logging.INFO, WARNING...
    logger.warning('test')



