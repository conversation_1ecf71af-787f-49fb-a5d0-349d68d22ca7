# basic config information  for all proj action classify based on original video
creator: Moss
task: classify
proj_name: soild_ball

videos_pth: /root/share175/sport_datas/soild_sphere/classify_trigger/ori_vids/Shanxi_fuxianzhangjiayijiounianzhixuexiao/2024_7_0118            # 原始视频路径
save_dir:  /root/share175/sport_datas/soild_sphere/classify_trigger/ori_vids/sample_Shanxi_fuxianzhangjiayijiounianzhixuexiao/2024_7_0118         # 存储pkl 样本路径
mark_file:  Config/mark_files/Shanxi_fuxianzhangjiayijiounianzhixuexiao.json                                                                                       # 标定文件
model_info:
  yolo_pose: /root/share175/sport_models/stand_jump/detect_person_pose/detect_person_pose_standjump_20241109_V8_768_448.onnx
  infer_size: [448, 768]


# 实心球触发项目
soild_ball:
  num_bodys: 1             # 单人姿态
  total_frames: 75        # 采集样本持续的帧数
  interval_frames: 25     # 最小采集的间隔帧
  sample_rate: 3          # 采样间隔帧
  len_sample: 25            # 样本长度 = total_frames / sample_rate


