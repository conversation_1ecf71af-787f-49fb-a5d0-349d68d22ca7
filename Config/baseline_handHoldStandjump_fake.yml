# basic config information  for all proj action classify based on original video
# 立定跳远 作弊|违规 数据采集配置
creator: Moss       # 用于场景理解
task: classify
proj_name: fake_stand_jump

#videos_pth: /root/persons/ai_group/Andy/现网反馈问题数据/stand_jump/168玫瑰园东区          # 原始视频路径
#videos_pth: /root/share175/sport_datas/stand_jump/classify_cheating/action_ori_vids/Jiangsu_xuzhouxingyuanzhongxue/206_4
videos_pth: /root/persons/ai_group/Pillar/现网问题反馈/standJump/cheating/2_90_重庆人和街小学/107_1080p
#videos_pth: /root/persons/ai_group/Lumi/视频测试集/立定跳远/待测试

#mark_file:  Config/mark_standjump/beijing_qinghuafuxiao_100.json
#mark_file:  Config/mark_standjump_hand_hold/wenzhou_12zhong.json
mark_file:  Config/mark_standjump/2/2_90_192.168.2.107_1.json

save_dir:  default        # 存储pkl 样本路径 默认修改到videos_pth路径下# 标定文件
model_info:
#  yolo_pose: /root/share175/sport_models/stand_jump/detect_person_pose/detect_person_pose_standjump_20250408_V9_768_448.onnx
  yolo_pose: /root/share175/sport_models/stand_jump/detect_person_pose/detect_person_pose_standjump_20250418_V10_768_448.onnx
#  yolo_pose: /root/share175/sport_models/stand_jump/detect_person_pose/detect_person_pose_standjump_20241109_V8_768_448.onnx
  infer_size: [448, 768]
#  det_foot: /root/share175/sport_models/stand_jump/detect_foot/stand_jumpt_detect_foot_20241123_v35_736_416.onnx
  det_foot: /root/share175/sport_models/stand_jump/detect_foot/stand_jumpt_detect_foot_20250517_v36_736_416.onnx
  foot_size: [416, 736]
#  classify_hand_hold: '/root/share175/sport_trains/stand_jump/classification_hand_hold_jump_efficientnetV2/20241226_run/weights/best.onnx'
  classify_hand_hold: '/root/share175/sport_models/stand_jump/classification_hand_hold_jump_efficientnetV2/Standjump_hand_hold_jump_20250519_V7_384_384.onnx'
#  classify_hand_hold: '/root/share175/sport_models/stand_jump/classification_hand_hold_jump_efficientnetV2/Standjump_hand_hold_jump_20241228_V6_384_384.onnx'
#  classify_hand_hold: '/root/share175/sport_trains/stand_jump/classification_hand_hold_jump_efficientnetV2/20250517_run/weights/best.onnx'
  hand_hold_size: [320, 384]







# 立定跳远触发项目
fake_stand_jump:
  num_bodys: 1             # 单人姿态
  act_frames: 60            # 离开准备区域的动作帧  B sampleds
  len_sample: 75            # 样本长度 = A(准备帧) + B(运动帧)


