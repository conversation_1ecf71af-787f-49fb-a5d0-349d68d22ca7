# basic config information  for all proj action classify based on original video
# 坐位体前屈 作弊|违规 数据采集配置
creator: Moss       # 用于场景理解
task: classify
proj_name: fake_sit_reach

devices: [6]

videos_pth: /root/share175/sport_datas/sit_and_reach/classify_cheating/ori_videos/咸阳市高新交大梦桃学校/videos  # sitreach_2024_0829
#videos_pth: /root/share175/sport_datas/sit_and_reach/classify_cheating/ori_videos/SitReach_oriVideos_Multi/Hunan_ningxiangnanyailanyueguxuexiao/temp_1  # sitreach_2024_0829
#videos_pth: /root/persons/dev_group/Stone/单人体前屈/郑州商学院  # sitreach_2024_0829

save_dir:  default        # 存储pkl 样本路径 默认修改到videos_pth路径下# 标定文件
model_info:             # 人体姿态检测模型
  yolo_pose: /root/share175/sport_models/sit_and_reach/detect_person_pose/detect_person_pose_situp_sitandreach_20250508_V41_768_448.onnx
  infer_size: [448, 768]

board_model:          # 板检测模型
  yolo_detect: /root/share175/sport_models/sit_and_reach/detect_board/detect_board_20250513_V16_1280_768.onnx        # detect_board_20241014_V15_1280_768.onnx
#  yolo_detect: Z:\sport_models\sit_and_reach\detect_board\detect_board_20240719_V14_640_384.onnx        # detect_board_20241014_V15_1280_768.onnx
  infer_size: [768, 1280]       # 图像预处理会处理成[736, 1280]


# 体前屈
fake_sit_reach:
  num_bodys: 2             # 多人姿态 TBS
  len_min: 25            # 最短样本长度

# TODO:夜跑模式专用 预训练的onnx 分类模型
pretrained_model:
  TCN_classify: /root/share175/sport_trains/action_recongnition/base_skeleton/GCN_yoloPose/runs/train_situpAndreach_cheat/exp37/best_model.onnx

# 预设类别索引
pred_labels:
  0: normal
  1: backside_push
  2: hand_pull
  3: violate_rule
  4: single_person
  5: lookers_nearby



