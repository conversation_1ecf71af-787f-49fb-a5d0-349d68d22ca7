# basic config information  for all proj action classify based on original video
# 仰卧起坐 作弊|违规 数据采集配置
creator: Moss       # 用于场景理解
task: classify
proj_name: fake_sit_up


devices: [5] #  [0,1,2,3, 4,5,6,7  ]
batch_Mod: True
save_oriImg: False            # 是否保存原始视频.mp4 False
videos_pth: /root/share175/sport_datas/sit_up/classify_cheating/ori_vids_fusion/Henan_zhenzhouhangtanhangkongxueyuan/videos        # 原始视频路径
#videos_pth: /root/persons/ai_group/Moss/Question/测试_长沙taiyu
#videos_pth: /root/persons/ai_group/Moss/Question/logic_fix/okk/2024_10_18_11_13_15_192.168.2.128_1_3_D.mp4

box_scale_thres: 1.5          # 需要严格满足: 人体框 W > 1.5 * H
save_dir:  default        # 存储pkl 样本路径 默认修改到videos_pth路径下# 标定文件
model_info:
  yolo_pose: /root/share175/sport_models/sit_and_reach/detect_person_pose/detect_person_pose_situp_sitandreach_20241129_V39_768_448.onnx
  infer_size: [448, 768]


# 仰卧起坐防作弊 配置项
fake_sit_up:
  num_bodys: 2             # 最大疑似 作弊嫌疑人 数量
  len_sample: 35            # 最大样本帧数
  min_len_sample: 25        # 最短样本长度


# TODO：夜跑模式专用：预训练的onnx 分类模型
pretrained_model:
  TCN_classify: /root/share175/sport_trains/action_recongnition/base_skeleton/GCN_yoloPose/runs/train_situpAndreach_cheat/exp103/best_model.onnx

# 预设类别索引
pred_labels:
  0: normal
  1: backside_push
  2: hand_pull
  3: violate_rule
  4: single_person
  5: lookers_nearby

