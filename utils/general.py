# -*-coding:utf-8-*-
import glob
import os
from pathlib import Path
import cv2
import numpy as np
import torch
import time
import re
import torchvision
import random
import matplotlib
import json
import pickle

from tensorRT.non_max_suppression_np import scale_boxes_np, scale_coords_np
# from utils.utils import letterbox, preprocess_img


# -----------------------------基础服务--------------------------------------
class LetterBox:
    """Resize image and padding for detection, instance segmentation, pose."""

    def __init__(self, new_shape=(640, 640), auto=False, scaleFill=False, scaleup=True, center=True, stride=32):
    # def __init__(self, new_shape=(640, 640), auto=False, scaleFill=False, scaleup=True, center=True, stride=16):
        """Initialize LetterBox object with specific parameters."""
        self.new_shape = new_shape
        self.auto = auto
        self.scaleFill = scaleFill
        self.scaleup = scaleup
        self.stride = stride
        self.center = center  # Put the image in the middle or top-left

    def __call__(self, labels=None, image=None):
        """Return updated labels and image with added border."""
        if labels is None:
            labels = {}
        img = labels.get("img") if image is None else image
        shape = img.shape[:2]  # current shape [height, width]
        new_shape = labels.pop("rect_shape", self.new_shape)
        if isinstance(new_shape, int):
            new_shape = (new_shape, new_shape)

        # Scale ratio (new / old)
        r = min(new_shape[0] / shape[0], new_shape[1] / shape[1])
        if not self.scaleup:  # only scale down, do not scale up (for better val mAP)
            r = min(r, 1.0)

        # Compute padding
        ratio = r, r  # width, height ratios
        new_unpad = int(round(shape[1] * r)), int(round(shape[0] * r))
        dw, dh = new_shape[1] - new_unpad[0], new_shape[0] - new_unpad[1]  # wh padding
        if self.auto:  # minimum rectangle
            dw, dh = np.mod(dw, self.stride), np.mod(dh, self.stride)  # wh padding
        elif self.scaleFill:  # stretch
            dw, dh = 0.0, 0.0
            new_unpad = (new_shape[1], new_shape[0])
            ratio = new_shape[1] / shape[1], new_shape[0] / shape[0]  # width, height ratios

        if self.center:
            dw /= 2  # divide padding into 2 sides
            dh /= 2

        if shape[::-1] != new_unpad:  # resize
            img = cv2.resize(img, new_unpad, interpolation=cv2.INTER_LINEAR)
        top, bottom = int(round(dh - 0.1)) if self.center else 0, int(round(dh + 0.1))
        left, right = int(round(dw - 0.1)) if self.center else 0, int(round(dw + 0.1))
        img = cv2.copyMakeBorder(
            img, top, bottom, left, right, cv2.BORDER_CONSTANT, value=(114, 114, 114)
        )  # add border
        if labels.get("ratio_pad"):
            labels["ratio_pad"] = (labels["ratio_pad"], (left, top))  # for evaluation

        if len(labels):
            labels = self._update_labels(labels, ratio, dw, dh)
            labels["img"] = img
            labels["resized_shape"] = new_shape
            return labels
        else:
            return img

    def _update_labels(self, labels, ratio, padw, padh):
        """Update labels."""
        labels["instances"].convert_bbox(format="xyxy")
        labels["instances"].denormalize(*labels["img"].shape[:2][::-1])
        labels["instances"].scale(*ratio)
        labels["instances"].add_padding(padw, padh)
        return labels


def preprocess(im, imgsz=[448,768]):
    """
    Prepares input image before inference.

    Args:
        im (torch.Tensor | List(np.ndarray)): BCHW for tensor, [(HWC) x B] for list.
    """
    not_tensor = not isinstance(im, torch.Tensor)
    if not_tensor:
        same_shapes = len({x.shape for x in im}) == 1
        letterbox = LetterBox(imgsz, auto=same_shapes, stride=32)
        pre_transform = [letterbox(image=x) for x in [im]]

        im = np.stack(pre_transform)
        im = im[..., ::-1].transpose((0, 3, 1, 2))  # BGR to RGB, BHWC to BCHW, (n, 3, h, w)
        im = np.ascontiguousarray(im)  # contiguous
        # im = torch.from_numpy(im)

        # im = im.astype(np.float32)
        im = im / np.float32(255.0)  # 0 - 255 to 0.0 - 1.0
    return im


class Colors:
    # Ultralytics color palette https://ultralytics.com/
    def __init__(self):
        self.palette = [self.hex2rgb(c) for c in matplotlib.colors.TABLEAU_COLORS.values()]
        self.n = len(self.palette)

    def __call__(self, i, bgr=False):
        c = self.palette[int(i) % self.n]
        return (c[2], c[1], c[0]) if bgr else c

    @staticmethod
    def hex2rgb(h):  # rgb order (PIL)
        return tuple(int(h[1 + i:1 + i + 2], 16) for i in (0, 2, 4))


colors = Colors()  # create instance for 'from utils.plots import colors'


def ORB_match_frames(frame1, frame2, threshold, orb_bf=None):
    """
    使用openCV的ORB检测器和BFMatch进行特征提取和匹配，
    对于相似场景可以不重新初始化
    """
    # if orb_bf is None:
    #     orb, bf = cv2.ORB_create(), cv2.BFMatcher(cv2.NORM_HAMMING, crossCheck=True)  # 初始化ORB检测器
    # 提取关键点和描述符，分析是否存在终点画面
    # kp1, des1 = orb.detectAndCompute(frame1, None)
    # kp2, des2 = orb.detectAndCompute(frame2, None)
    # matches = bf.match(des1, des2).__len__

    # 直方图
    hist1 = cv2.calcHist([frame1], [0], None, [256], [0, 256])
    hist2 = cv2.calcHist([frame2], [0], None, [256], [0, 256])
    hist1 = cv2.normalize(hist1, hist1).flatten()
    hist2 = cv2.normalize(hist2, hist2).flatten()
    matches = cv2.compareHist(hist1, hist2, cv2.HISTCMP_CORREL)
    print(matches)

    if matches > threshold:
        return True  # 来自同一个摄像头

    return False


# --------------------------------加载图片、视频数据--------------------------------------------
class LoadImages_pullup:  # only for videos
    video_frames: int

    def __init__(self, files_list, img_size=640, vid_width=1920):
        self.files = files_list
        self.img_size = img_size
        self.vid_width = vid_width

        self.nf = len(self.files)  # number of files
        self.video_flag = [True] * self.nf
        self.mode, self.frame = 'video', 0

        self.cap= self.new_video(self.files[0]) if any(self.files) else None


    def __iter__(self):
        self.count = 0
        return self

    def __next__(self):
        if self.count == self.nf:
            raise StopIteration
        path = self.files[self.count]

        if self.video_flag[self.count]:
            # Read video
            ret_val, img0 = self.cap.read()
            if not ret_val:         # 当前视频已结束，开始下一个video
                self.count += 1
                self.cap.release()
                if self.count == self.nf:  # last video
                    raise StopIteration
                else:
                    path = self.files[self.count]
                    self.new_video(path)
                    ret_val, img0 = self.cap.read()
                    self.frame = 0

            self.frame += 1         # 从第1帧开始
            # print(f'video {self.count + 1}/{self.nf} ({self.frame}/{self.video_frames}) {Path(path).name}: ', end=' ')
        else:
            raise Exception(f"@Moss Err info: ")

        # Padded resize
        input_img, _, _ = self.letterbox(img0, new_shape=self.img_size)
        img = self.preprocess_img(input_img)

        return path, img, img0, self.count, self.frame

    def new_video(self, path):
        self.cap = cv2.VideoCapture(path)
        self.video_frames = int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))
        shape = (int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT)), int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH)))     # (H, W)

        # assert shape[1] == self.vid_width, f"@Moss: 标定img—width:{self.vid_width} != video-shape: {shape[1]}; vid_pth:{path}"    # 标定图像尺寸匹配视频尺寸
        return self.cap

    def __len__(self):
        return self.nf  # number of files

    def letterbox(self, im, new_shape=(640, 640), color=(114, 114, 114), auto=True, scaleFill=False, scaleup=True, stride=32):
        # Resize and pad image while meeting stride-multiple constraints
        shape = im.shape[:2]  # current shape [height, width]
        if isinstance(new_shape, int):
            new_shape = (new_shape, new_shape)

        # Scale ratio (new / old)
        r = min(new_shape[0] / shape[0], new_shape[1] / shape[1])
        if not scaleup:  # only scale down, do not scale up (for better val mAP)
            r = min(r, 1.0)

        # Compute padding
        ratio = r, r  # width, height ratios
        new_unpad = int(round(shape[1] * r)), int(round(shape[0] * r))
        dw, dh = new_shape[1] - new_unpad[0], new_shape[0] - new_unpad[1]  # wh padding
        if auto:  # minimum rectangle
            dw, dh = np.mod(dw, stride), np.mod(dh, stride)  # wh padding
        elif scaleFill:  # stretch
            dw, dh = 0.0, 0.0
            new_unpad = (new_shape[1], new_shape[0])
            ratio = new_shape[1] / shape[1], new_shape[0] / shape[0]  # width, height ratios

        dw /= 2  # divide padding into 2 sides
        dh /= 2

        if shape[::-1] != new_unpad:  # resize
            im = cv2.resize(im, new_unpad, interpolation=cv2.INTER_LINEAR)
        top, bottom = int(round(dh - 0.1)), int(round(dh + 0.1))
        left, right = int(round(dw - 0.1)), int(round(dw + 0.1))
        im = cv2.copyMakeBorder(im, top, bottom, left, right, cv2.BORDER_CONSTANT, value=color)  # add border
        return im, ratio, (dw, dh)


    def preprocess_img(self, frame):
        img = frame[:, :, ::-1]
        img = img / 255.00
        img = np.asarray(img, dtype=np.float32)
        img = np.expand_dims(img, 0)
        img = img.transpose(0, 3, 1, 2)
        return img


class LoadImages:  # only for videos
    video_frames: int
    jump_video: bool

    def __init__(self, files_list, img_size=640, vid_width=1920, mirror='True'):
        self.files = files_list
        self.img_size = img_size
        self.vid_width = vid_width
        self.mirror = mirror            # mirror = 'True' 表示从左边跳，不需要镜像

        self.jump_video = False         # 默认不会跳过任何一个视频

        self.nf = len(self.files)  # number of files
        self.video_flag = [True] * self.nf
        self.mode, self.frame = 'video', 0

        self.cap= self.new_video(self.files[0]) if any(self.files) else None


    def __iter__(self):
        self.count = 0
        return self

    def __next__(self):
        if self.count == self.nf:
            raise StopIteration
        path = self.files[self.count]

        if self.video_flag[self.count]:
            # Read video
            ret_val, img0 = self.cap.read()
            if self.mirror is None:
                img0 = np.flip(img0, axis=1) if img0 is not None else img0


            if (not ret_val) or self.jump_video:         # 当前视频已结束，开始下一个video
                self.count += 1
                self.cap.release()
                if self.count == self.nf:  # last video
                    raise StopIteration
                else:
                    path = self.files[self.count]
                    self.new_video(path)
                    self.jump_video = False
                    ret_val, img0 = self.cap.read()
                    if self.mirror is None:
                        img0 = np.flip(img0, axis=1) if img0 is not None else img0

                    self.frame = 0

            self.frame += 1         # 从第1帧开始
            # print(f'video {self.count + 1}/{self.nf} ({self.frame}/{self.video_frames}) {Path(path).name}: ', end=' ')
        else:
            raise Exception(f"@Moss Err info: ")

        # preprocess
        if hasattr(img0, 'shape'):      # 缩放
            oriVid_shape = img0.shape
            if oriVid_shape == (1440, 2560, 3) or oriVid_shape == (2160, 3840, 3):
               img0 = cv2.resize(img0, (1920, 1080), interpolation=cv2.INTER_LINEAR)
            elif oriVid_shape == (1080, 1920, 3):
               img0 = img0
            else:
               img0, img = None, None

        if hasattr(img0, 'shape'):      # 预处理
            img = preprocess(img0, imgsz=self.img_size)
        else:
            img, oriVid_shape = None, None

        return path, img, img0, self.count, self.frame, oriVid_shape

    def new_video(self, path):
        self.cap = cv2.VideoCapture(path)
        self.video_frames = int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))
        shape = (int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT)), int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH)))     # (H, W)
        # assert shape[1] == self.vid_width, f"@Moss: 标定img—width:{self.vid_width} != video-shape: {shape[1]}; vid_pth:{path}"    # 标定图像尺寸匹配视频尺寸
        return self.cap

    def __len__(self):
        return self.nf  # number of files


class LoadFrames:  # only for videos in Run50
    video_frames: int
    jump_video: bool

    def __init__(self, files_list, img_size=640, vid_width=1920, minVid_len=16*25):
        self.files = files_list
        self.img_size = img_size
        self.vid_width = vid_width
        self.minVid_len = minVid_len

        self.jump_video = False         # 默认 不跳过任何一个视频

        self.nf = len(self.files)  # number of files
        self.video_flag = [True] * self.nf
        self.mode, self.frame = 'video', 0

    def __iter__(self):
        self.count = 0
        return self

    def __next__(self):
        if self.count == self.nf:
            raise StopIteration
        path = self.files[self.count]

        if self.video_flag[self.count]:
            self.cap = self.new_video(path)
            self.video_frames = int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))
            # print(self.video_frames, self.video_frames/25)

            if self.video_frames <= self.minVid_len:
                ret_val, img0, img, imgZ = False, None, None, None
                self.jump_video = True          # 包无效视频-short
            elif 21*25 < self.video_frames:
                self.jump_video = False         # 超-长视频 包有效
                ret_val, img0 = self.cap.read()
                img = self.process_frame(img0)
                img0, imgZ = None, None
            else:
                self.jump_video = False         # 判断2帧 ORB相似度
                ret_val, img0 = self.cap.read()
                self.cap.set(cv2.CAP_PROP_POS_FRAMES, self.video_frames - 4*25)     # 4秒内跑完50m的作弊过滤
                ret_val_1, imgZ = self.cap.read()
                simility = ORB_match_frames(img0, imgZ, threshold=0.81, orb_bf=None)
                if simility:
                    img0, img, imgZ = simility, None, simility       # True: 高相似度表明无终点画面
                else:
                    img = self.process_frame(img0)


            # 当前视频自动结束，开始下一个video
            self.count += 1
            self.cap.release()

            # print(f'video {self.count + 1}/{self.nf} ({self.frame}/{self.video_frames}) {Path(path).name}: ', end=' ')
        else:
            raise Exception(f"@Moss Err info: ")


        return path, img0, img, imgZ, self.count

    def process_frame(self, img0):
        # pre-process
        if hasattr(img0, 'shape'):      # 缩放
            oriVid_shape = img0.shape
            if oriVid_shape == (1440, 2560, 3) or oriVid_shape == (2160, 3840, 3):
               img0 = cv2.resize(img0, (1920, 1080), interpolation=cv2.INTER_LINEAR)
            elif oriVid_shape == (1080, 1920, 3):
               img0 = img0
            else:
               img0, img = None, None

        if hasattr(img0, 'shape'):      # 预处理
            img = preprocess(img0, imgsz=self.img_size)
        else:
            img, oriVid_shape = None, None

        return img


    def new_video(self, path):
        self.cap = cv2.VideoCapture(path)
        shape = (int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT)), int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH)))     # (H, W)
        # assert shape[1] == self.vid_width, f"@Moss: 标定img—width:{self.vid_width} != video-shape: {shape[1]}; vid_pth:{path}"    # 标定图像尺寸匹配视频尺寸
        return self.cap

    def __len__(self):
        return self.nf  # number of files


# --------------------------------------------------------------------------------------


def plot_sitUp_status(status: list, save_path='list_plot.png'):
    """
    仰卧起坐防作弊 绘制点轨迹波形图
    :param save_path:
    :param status:
    :return:
    """
    import matplotlib.pyplot as plt
    x_axis, y_axis = list(range(len(status))), status

    plt.figure(figsize=(25, 8))  # 图像尺寸
    plt.plot(x_axis, y_axis, marker='.')
    # 辅助线
    plt.axhline(y=95, color='g', linestyle='-')
    plt.axhline(y=115, color='r', linestyle='--')
    plt.xticks(x_axis[::25], rotation=45)  # 间隔25帧显示1次
    plt.grid(True)
    plt.savefig(save_path)

    return



def split_sample(sub_lst, sample_len=64, delay=32):
    samples = []
    for i in range(0, len(sub_lst), delay):
        sample = sub_lst[i:i+sample_len]
        if len(sample) == sample_len:
            samples.append(sample)
    return samples



def split_detframes(lst, thresh=5) ->[list]:
    result, res_id, sub_lst = [], [], [lst[0]]            # 在ori_vid的索引划分； 对应到lst的idx
    for i in range(1, len(lst)):
        if lst[i] - lst[i-1] > thresh:
            samples = split_sample(sub_lst, sample_len=64, delay=32)
            result += samples
            sub_lst = [lst[i]]
        else:
            sub_lst.append(lst[i])
    end = split_sample(sub_lst, sample_len=64, delay=32)
    result += end

    # 按lst的值取result的索引
    res_id = [[lst.index(val) for val in sublist] for sublist in result]

    return result, res_id




def onnx_postprocess(session, img):
    img = np.asarray(img, dtype=np.float32)
    img = (img - 0.0) * 0.00392156862745098
    img = np.expand_dims(img, 0)

    # only for yolo-pose v5 onnx
    pred = torch.tensor(session.run([session.get_outputs()[0].name], {session.get_inputs()[0].name: img})[0])
    shape = pred.shape                                          # onnx关键点顺序已改变为 xx..x yy..y cc..c
    pred_51 = pred[..., 6:].view(shape[0], shape[1], 3, 17)     # 51 reshape-> {x}*17,{y}*17,{c}*17
    pred_51 = torch.transpose(pred_51, -2, -1).reshape(shape[0], shape[1], -1)  # -> {xyc}*17
    pred = torch.concat((pred[..., :6], pred_51), -1)

    # Apply NMS
    pred = non_max_suppression(pred, conf_thres=0.4, iou_thres=0.45, classes=0, kpt_label=17)

    return pred, img


def non_max_suppression(prediction, conf_thres=0.25, iou_thres=0.45, classes=None, agnostic=False, multi_label=False,
                        labels=(), kpt_label=False, nc=None, nkpt=17):
    """Runs Non-Maximum Suppression (NMS) on inference results

    Returns:
         list of detections, on (n,6) tensor per image [xyxy, conf, cls]
    """
    if nc is None:
        nc = prediction.shape[2] - 5  if not kpt_label else prediction.shape[2] - (5+3*nkpt)    # number of classes: 1 is person  @Moss fixed 56
    xc = prediction[..., 4] > conf_thres  # xyxy pre lab (xy c)*17

    # Settings
    min_wh, max_wh = 2, 4096  # (pixels) minimum and maximum box width and height
    max_det = 300  # maximum number of detections per image
    max_nms = 30000  # maximum number of boxes into torchvision.ops.nms()
    time_limit = 10.0  # seconds to quit after
    redundant = True  # require redundant detections
    multi_label &= nc > 1  # multiple labels per box (adds 0.5ms/img)
    merge = False  # use merge-NMS

    t = time.time()
    output = [torch.zeros((0, 5+nc), device=prediction.device)] * prediction.shape[0]
    for xi, x in enumerate(prediction):  # image index, image inference
        # Apply constraints
        # x[((x[..., 2:4] < min_wh) | (x[..., 2:4] > max_wh)).any(1), 4] = 0  # width-height
        x = x[xc[xi]]  # confidence

        # Cat apriori labels if autolabelling
        if labels and len(labels[xi]):
            l = labels[xi]
            v = torch.zeros((len(l), nc + 5), device=x.device)
            v[:, :4] = l[:, 1:5]  # box
            v[:, 4] = 1.0  # conf
            v[range(len(l)), l[:, 0].long() + 5] = 1.0  # cls
            x = torch.cat((x, v), 0)

        # If none remain process next image
        if not x.shape[0]:
            continue

        # Compute conf
        x[:, 5:5+nc] *= x[:, 4:5]  # conf = obj_conf * cls_conf

        # Box (center x, center y, width, height) to (x1, y1, x2, y2)
        box = xywh2xyxy(x[:, :4])

        # Detections matrix nx6 (xyxy, conf, cls)
        if multi_label:
            if not kpt_label:
                i, j = (x[:, 5:] > conf_thres).nonzero(as_tuple=False).T
                x = torch.cat((box[i], x[i, j + 5, None], j[:, None].float()), 1)
            else:   # TODO @Moss add ??
                kpts = x[:, 5+nc:]
                i, j = (x[:, 5:5+nc] > conf_thres).nonzero(as_tuple=False).T
                x = torch.cat((box[i], x[i, j + 5, None], j[:, None].float(), kpts[i]), 1)
        else:  # best class only
            if not kpt_label:
                conf, j = x[:, 5:].max(1, keepdim=True)
                x = torch.cat((box, conf, j.float()), 1)[conf.view(-1) > conf_thres]
            else:
                kpts = x[:, 6:]
                conf, j = x[:, 5:6].max(1, keepdim=True)
                x = torch.cat((box, conf, j.float(), kpts), 1)[conf.view(-1) > conf_thres]


        # Filter by class
        if classes is not None:
            x = x[(x[:, 5:6] == torch.tensor(classes, device=x.device)).any(1)]

        # Apply finite constraint
        # if not torch.isfinite(x).all():
        #     x = x[torch.isfinite(x).all(1)]

        # Check shape
        n = x.shape[0]  # number of boxes
        if not n:  # no boxes
            continue
        elif n > max_nms:  # excess boxes
            x = x[x[:, 4].argsort(descending=True)[:max_nms]]  # sort by confidence

        # Batched NMS
        c = x[:, 5:6] * (0 if agnostic else max_wh)  # classes
        boxes, scores = x[:, :4] + c, x[:, 4]  # boxes (offset by class), scores
        i = torchvision.ops.nms(boxes, scores, iou_thres)  # NMS
        if i.shape[0] > max_det:  # limit detections
            i = i[:max_det]
        if merge and (1 < n < 3E3):  # Merge NMS (boxes merged using weighted mean)
            # update boxes as boxes(i,4) = weights(i,n) * boxes(n,4)
            iou = box_iou(boxes[i], boxes) > iou_thres  # iou matrix
            weights = iou * scores[None]  # box weights
            x[i, :4] = torch.mm(weights, x[:, :4]).float() / weights.sum(1, keepdim=True)  # merged boxes
            if redundant:
                i = i[iou.sum(1) > 1]  # require redundancy

        output[xi] = x[i]
        if (time.time() - t) > time_limit:
            print(f'WARNING: NMS time limit {time_limit}s exceeded')
            break  # time limit exceeded

    return output


def scale_coords(img1_shape, coords, img0_shape, ratio_pad=None, kpt_label=False, step=2):
    """
    TODO: 区分 tensor 和 numpy 的计算方式
    """
    # 以下是tensor的处理方式
    if ratio_pad is None:  # calculate from img0_shape
        gain = min(img1_shape[0] / img0_shape[0], img1_shape[1] / img0_shape[1])  # gain  = old / new
        pad = (img1_shape[1] - img0_shape[1] * gain) / 2, (img1_shape[0] - img0_shape[0] * gain) / 2  # wh padding
    else:
        gain = ratio_pad[0]
        pad = ratio_pad[1]
    if isinstance(gain, (list, tuple)):
        gain = gain[0]
    if not kpt_label:       # use person bbox xyxy
        coords[:, [0, 2]] -= pad[0]  # x padding
        coords[:, [0, 2]] /= gain
        coords[:, [1, 3]] -= pad[1]  # y padding
        coords[:, [1, 3]] /= gain
        # coords[:, 0:4] = coords[:, 0:4].round()
        clip_coords(coords[0:4], img0_shape)        # check if x,y beyond img0's xy

    else:                   # use keypoint labels
        coords[:, 0::step] -= pad[0]  # x padding
        coords[:, 0::step] /= gain
        coords[:, 1::step] -= pad[1]  # y padding
        coords[:, 1::step] /= gain
        clip_coords(coords, img0_shape, step=step)
        # coords = coords.round()
    return coords



def plot_skeleton_kpts(im, kpts, steps, orig_shape=None, kpt_num=17):
    # Plot the skeleton and keypointsfor coco datatset
    palette = np.array([[255, 128, 0], [255, 153, 51], [255, 178, 102],
                        [230, 230, 0], [255, 153, 255], [153, 204, 255],
                        [255, 102, 255], [255, 51, 255], [102, 178, 255],
                        [51, 153, 255], [255, 153, 153], [255, 102, 102],
                        [255, 51, 51], [153, 255, 153], [102, 255, 102],
                        [51, 255, 51], [0, 255, 0], [0, 0, 255], [255, 0, 0],
                        [255, 255, 255]])       # RGB 调色板

    skeleton = [[16, 14], [14, 12], [17, 15], [15, 13], [12, 13], [6, 12],
                [7, 13], [6, 7], [6, 8], [7, 9], [8, 10], [9, 11], [2, 3],
                [1, 2], [1, 3], [2, 4], [3, 5], [4, 6], [5, 7]]

    pose_limb_color = palette[[9, 9, 9, 9, 7, 7, 7, 0, 0, 0, 0, 0, 16, 16, 16, 16, 16, 16, 16]]     # row len is 17+2
    pose_kpt_color = palette[[16, 16, 16, 16, 16, 0, 0, 0, 0, 0, 0, 9, 9, 9, 9, 9, 9]][:kpt_num]              # row len is 17. @Moss split by [:kpt_num]
    radius = 5
    num_kpts = len(kpts) // steps

    for kid in range(num_kpts):
        r, g, b = pose_kpt_color[kid]
        x_coord, y_coord = kpts[steps * kid], kpts[steps * kid + 1]
        if not (x_coord % 640 == 0 or y_coord % 640 == 0):
            if steps == 3:
                conf = kpts[steps * kid + 2]
                if conf < 0.5:
                    continue
            cv2.circle(im, (int(x_coord), int(y_coord)), radius, (int(r), int(g), int(b)), -1)      # plot num_kpts point

    for sk_id, sk in enumerate(skeleton):
        r, g, b = pose_limb_color[sk_id]
        pos1 = (int(kpts[(sk[0]-1)*steps]), int(kpts[(sk[0]-1)*steps+1]))
        pos2 = (int(kpts[(sk[1]-1)*steps]), int(kpts[(sk[1]-1)*steps+1]))
        if steps == 3:
            conf1 = kpts[(sk[0]-1)*steps+2]
            conf2 = kpts[(sk[1]-1)*steps+2]
            if conf1<0.5 or conf2<0.5:
                continue
        if pos1[0]%640 == 0 or pos1[1]%640==0 or pos1[0]<0 or pos1[1]<0:
            continue
        if pos2[0] % 640 == 0 or pos2[1] % 640 == 0 or pos2[0]<0 or pos2[1]<0:
            continue
        cv2.line(im, pos1, pos2, (int(r), int(g), int(b)), thickness=2)


def plot_one_box(x, im, color=None, label=None, line_thickness=3, kpt_label=False, kpt_num=17, kpts=None, steps=2, orig_shape=None):
    # Plots one bounding box on image 'im' using OpenCV
    assert im.data.contiguous, 'Image not contiguous. Apply np.ascontiguousarray(im) to plot_on_box() input image.'
    tl = line_thickness or round(0.002 * (im.shape[0] + im.shape[1]) / 2) + 1  # line/font thickness
    color = color or [random.randint(0, 255) for _ in range(3)]
    c1, c2 = (int(x[0]), int(x[1])), (int(x[2]), int(x[3]))
    cv2.rectangle(im, c1, c2, (255,0,0), thickness=tl*1//3, lineType=cv2.LINE_AA)
    if label:
        if len(label.split(' ')) > 1:
            label = label.split(' ')[-1]
            tf = max(tl - 1, 1)  # font thickness
            t_size = cv2.getTextSize(label, 0, fontScale=tl / 6, thickness=tf)[0]
            c2 = c1[0] + t_size[0], c1[1] - t_size[1] - 3
            cv2.rectangle(im, c1, c2, color, -1, cv2.LINE_AA)  # filled
            cv2.putText(im, label, (c1[0], c1[1] - 2), 0, tl / 6, [225, 255, 255], thickness=tf//2, lineType=cv2.LINE_AA)
    if kpt_label:
        plot_skeleton_kpts(im, kpts, steps, orig_shape=orig_shape, kpt_num=kpt_num)



def save_video(vid_cap, im0, save_path):
    vid_writer = None
    if isinstance(vid_writer, cv2.VideoWriter):
        vid_writer.release()  # release previous video writer
    if vid_cap:  # video
        fps = vid_cap.get(cv2.CAP_PROP_FPS)
        w = int(vid_cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        h = int(vid_cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    save_path = str(Path(save_path).with_suffix('.avi'))  # force *.mp4 suffix on results videos
    vid_writer = cv2.VideoWriter(save_path, cv2.VideoWriter_fourcc(*'XVID'), fps, (w, h))
    vid_writer.write(im0)



def clip_coords(boxes, img_shape, step=2):
    # Clip bounding xyxy bounding boxes to image shape (height, width)
    if isinstance(boxes, torch.Tensor):
        boxes[:, 0::step].clamp_(0, img_shape[1])  # x1，x2
        boxes[:, 1::step].clamp_(0, img_shape[0])  # y1，y2
    elif isinstance(boxes, np.ndarray):
        boxes[:, 0::step] = np.clip(boxes[:, 0::step], 0, img_shape[1])
        boxes[:, 1::step] = np.clip(boxes[:, 1::step], 0, img_shape[0])
    else:
        raise TypeError(f"@Moss: boxes is {boxes.__class__}")



def xywh2xyxy(x):
    # Convert nx4 boxes from [x, y, w, h] to [x1, y1, x2, y2] where xy1=top-left, xy2=bottom-right
    y = x.clone() if isinstance(x, torch.Tensor) else np.copy(x)
    y[:, 0] = x[:, 0] - x[:, 2] / 2  # top left x
    y[:, 1] = x[:, 1] - x[:, 3] / 2  # top left y
    y[:, 2] = x[:, 0] + x[:, 2] / 2  # bottom right x
    y[:, 3] = x[:, 1] + x[:, 3] / 2  # bottom right y
    return y


def box_iou(box1, box2):
    # https://github.com/pytorch/vision/blob/master/torchvision/ops/boxes.py
    """
    Return intersection-over-union (Jaccard index) of boxes.
    Both sets of boxes are expected to be in (x1, y1, x2, y2) format.
    Arguments:
        box1 (Tensor[N, 4])
        box2 (Tensor[M, 4])
    Returns:
        iou (Tensor[N, M]): the NxM matrix containing the pairwise
            IoU values for every element in boxes1 and boxes2
    """

    def box_area(box):
        # box = 4xn
        return (box[2] - box[0]) * (box[3] - box[1])

    area1 = box_area(box1.T)
    area2 = box_area(box2.T)

    # inter(N,M) = (rb(N,M,2) - lt(N,M,2)).clamp(0).prod(2)
    inter = (torch.min(box1[:, None, 2:], box2[:, 2:]) - torch.max(box1[:, None, :2], box2[:, :2])).clamp(0).prod(2)
    return inter / (area1[:, None] + area2 - inter)  # iou = inter / (area1 + area2 - inter)


def increment_path(path, exist_ok=False, sep='', mkdir=False):
    # Increment file or directory path, i.e. runs/exp --> runs/exp{sep}2, runs/exp{sep}3, ... etc.
    path = Path(path)  # os-agnostic
    if path.exists() and not exist_ok:
        suffix = path.suffix
        path = path.with_suffix('')
        dirs = glob.glob(f"{path}{sep}*")  # similar paths
        matches = [re.search(rf"%s{sep}(\d+)" % path.stem, d) for d in dirs]
        i = [int(m.groups()[0]) for m in matches if m]  # indices
        n = max(i) + 1 if i else 2  # increment number
        path = Path(f"{path}{sep}{n}{suffix}")  # update path
    dir = path if path.suffix == '' else path.parent  # directory
    if not dir.exists() and mkdir:
        dir.mkdir(parents=True, exist_ok=True)  # make directory
    return path


def time_synchronized():
    # pytorch-accurate time
    if torch.cuda.is_available():
        torch.cuda.synchronize()
    return time.time()


# ------- 以下是引体向上 数据服务
def CVImageCrop(img, new_width, new_height,):
    #image_ori = Image.fromarray(img[:, :, [2, 1, 0]])
    h, w,_ = img.shape
    x1 = int((w - new_width) // 2)
    y1 = int((h - new_height) // 2)
    x2 = int((w + new_width) // 2)
    y2 = int((h + new_height) // 2)
    print("left:top,right:bottom",x1,y1,x2,y2)
    croped_image = img[y1:y2,x1:x2]
    #cv2.imwrite("pullup_data/video/test.jpg",croped_image)
    return croped_image


def saveImageToMp4(path,out_dir,frame_id,frame_count,frames,width,height):
    # 生成vids & 存储
    #lab_videoName = Path(path).with_suffix('.mp4').__str__()
    path_name = Path(path).stem.strip() + f'_{frame_id}_{frame_count}.mp4'
    save_path = (Path(path).parent /out_dir/ path_name).__str__()
    if not Path(Path(path).parent /out_dir).exists():
        os.mkdir(Path(Path(path).parent /out_dir))
    vid_writer = cv2.VideoWriter(save_path, cv2.VideoWriter_fourcc(*'mp4v'), 25,
                                 (width, height))  # 创建保存视频的对象
    for idf in frames:
        vid_writer.write(idf)  # saving avi
    vid_writer.release()


def saveHnadInfoToTxt(path,out_dir,frame_id,frame_count,left_hand,right_hand):
    path_name = Path(path).stem + f'_{frame_id}_{frame_count}.txt'
    save_path = (Path(path).parent / out_dir / path_name).__str__()
    with open(save_path,"w") as f:
        txt = ''.join(str((left_hand[:4].tolist()+right_hand[:4].tolist())))
        f.write(txt)


def saveHnadInfoToPkl(path,out_dir,up_frameIds):
    frame_id = list(up_frameIds.keys())[0]
    frame_count = len(up_frameIds)
    path_name = Path(path).stem.strip() + f'_{frame_id}_{frame_count}.pkl'
    save_path = (Path(path).parent / out_dir / path_name).__str__()
    with open(save_path,"wb") as f:
        pickle.dump(up_frameIds,f)

