# -*-coding:utf-8-*-
"""
@Moss の Logic for 2024-Server
"""
import math
from math import sqrt

import pickle
import torch
from collections import deque
import warnings
import numpy as np

from typing import List, Tuple

warnings.filterwarnings('ignore', category=UserWarning)


# ----------------------------------- 基础服务 ------------------------------------------------------------------
def get_near_suspect(area_list: list, bodys: int) -> list:
    sorted_dist = sorted(area_list, key=lambda item: item[0])
    top_body_idx = [index for _, index in sorted_dist[: bodys]]

    return top_body_idx


def compute_area_person(det_index: int, det: torch.tensor, target_point: tuple) -> torch.tensor:
    # 计算B区或S区 距离T目标的最小距离
    xl_wrist, yl_wrist = det[det_index, 6:][27], det[det_index, 6:][28]
    xr_wrist, yr_wrist = det[det_index, 6:][30], det[det_index, 6:][31]
    dist_left = sqrt((target_point[0] - xl_wrist) ** 2 + (target_point[1] - yl_wrist) ** 2)
    dist_right = sqrt((target_point[0] - xr_wrist) ** 2 + (target_point[1] - yr_wrist) ** 2)
    dist_min = dist_left if dist_right > dist_left else dist_right

    return dist_min


def IOU_limit(xycl: list, Pts: list):
    # 比较 ∩最大的bbox, 计算IOU交集面积
    b1_x1, b1_y1, b1_x2, b1_y2 = xycl[0], xycl[1], xycl[2], xycl[3]
    b2_x1, b2_y1, b2_x2, b2_y2 = Pts[0], Pts[1], Pts[2], Pts[3]

    delt_x = min(b1_x2, b2_x2) - max(b1_x1, b2_x1)
    delt_y = min(b1_y2, b2_y2) - max(b1_y1, b2_y1)
    if delt_x >= 0 and delt_y >=0:
        inter = (min(b1_x2, b2_x2) - max(b1_x1, b2_x1)) * (min(b1_y2, b2_y2) - max(b1_y1, b2_y1))
    else:
        inter = -1       # -1: 木有交集

    return inter


def compare_xl_near(xyxy_lists: List[Tuple[list, torch.tensor, torch.tensor, float]], pt_xl: int) -> tuple:
    if len(xyxy_lists) == 1:
        return xyxy_lists[0]
    else:
        # 取出距离Pt1_x最近的人体
        return min(xyxy_lists, key=lambda x: abs(x[0][0] - pt_xl))


def compare_maxIOU(xyxy_lists: List[Tuple[list, torch.tensor, torch.tensor, float]]) -> tuple:
    if len(xyxy_lists) == 1:
        return xyxy_lists[0]
    else:
        # 取出交集面积x[-1] 最大的 人体
        return max(xyxy_lists, key=lambda x: abs(x[-1]))


def sample_list(lst: List, num_samples: int) -> list:
    """
    均匀抽样 for 立定跳远, 仰卧起坐
    :param lst: 需要抽样的序列
    :param num_samples: 需要抽取的元素个数,默认为15或75
    :return:
    """
    if len(lst) < num_samples:  # 若序列不够长，直接返回原序列
        return lst

    indices = np.linspace(0, len(lst) - 1, num_samples, dtype=int)  # 生成等间距索引
    return [lst[i] for i in indices]  # 使用索引取样


def compute_angle(shoulder: Tuple, waist: Tuple, knee: Tuple):
    # 仰卧起坐 计算肩-腰-膝 的夹角
    x_S, y_S = shoulder
    x_W, y_W = waist
    x_K, y_K = knee

    # 计算向量 SW、KW
    SW = (x_W - x_S, y_W - y_S)
    KW = (x_W - x_K, y_W - y_K)
    dot_product = SW[0] * KW[0] + SW[1] * KW[1]  # 计算点积

    # 计算向量的模
    norm_SW = sqrt(SW[0] ** 2 + SW[1] ** 2)
    norm_KW = sqrt(KW[0] ** 2 + KW[1] ** 2)

    # 计算夹角的余弦值
    cos_theta = dot_product / (norm_SW * norm_KW)
    angle_theta = math.acos(cos_theta) if -1 < cos_theta < 1 else math.acos(-1)  # 计算夹角
    angle_SWK = math.degrees(angle_theta)  # 将弧度转换为度

    return angle_SWK


def compute_line_func(up_down_ptList):
    # 计算线段A，B的方程的斜率和轴距
    x1, y1, x2, y2 = up_down_ptList[0], up_down_ptList[1], up_down_ptList[2], up_down_ptList[3]
    # 计算斜率
    km = (y2 - y1) / (x2 - x1) + 1e-6
    b = y1 - km * x1

    return km, b


# ---------------------------------解析配置标定文件 --------------------------------------------------------------

def read_markInfo(mark_info: dict):
    """
    获取单人实心球 标定信息，并返回 左上,右下的坐标组合
    :param mark_info:
    :return:
    """
    Pt1_x, Pt1_y = mark_info['ReadyPt1_x'], mark_info['ReadyPt1_y']
    Pt2_x, Pt2_y = mark_info['ReadyPt2_x'], mark_info['ReadyPt2_y']
    pt_xl, pt_yl = min(Pt1_x, Pt2_x), min(Pt1_y, Pt2_y)  # top_left
    pt_xr, pt_yr = max(Pt1_x, Pt2_x), max(Pt1_y, Pt2_y)  # bottom_right

    return pt_xl, pt_yl, pt_xr, pt_yr


def read_markInfo_standjump(mark_info: dict):
    """
    获取单人立定跳远 标定信息，并返回 左上,右下的坐标组合
    # 新增读取
    :param mark_info: 认为统一朝左为基础方向， if mark_info.get('mirror')， 则朝向改变
    :return: 左上点，右下点
    """
    MarkPts = mark_info.get('MarkPts')
    if MarkPts:
        up_down_ptList = predel_marks_func(mark_info)  # 标定信息扩展 【上终点x,y, 下终点x,y, 上平均y, 下平均y, 上起点x, 下起点x】
        Pt1_x, Pt1_y = up_down_ptList[6]
        Pt2_x, Pt2_y = up_down_ptList[7]
    else:
        Pt1_x, Pt1_y = mark_info['ReadyPt1_x'], mark_info['ReadyPt1_y']
        Pt2_x, Pt2_y = mark_info['ReadyPt2_x'], mark_info['ReadyPt2_y']
        up_down_ptList = None

    Pt1_x += 20 if mark_info.get('mirror') else -20  # 偏移20pix
    Pt2_x -= 200 if mark_info.get('mirror') else -200  # 偏移200pix
    Pt1_y -= int(1080 / 30)
    pt_xl, pt_yl = min(Pt1_x, Pt2_x), min(Pt1_y, Pt2_y)  # top_left
    pt_xr, pt_yr = max(Pt1_x, Pt2_x), max(Pt1_y, Pt2_y)  # bottom_right

    return pt_xl, pt_yl, pt_xr, pt_yr, up_down_ptList


# ---------------------------------以下是 定位目标主体 的逻辑--------------------------------------------------------------

def solidball_logic(mark_info: dict, det=None):
    """
    # 单人实心球判别人体是否在区域内的逻辑
    :param det: 每1张图上检测到的人体
    :param mark_info: 包含所有标定信息
    :return:
    """
    # 获取标定信息
    pt_xl, pt_yl, pt_xr, pt_yr = read_markInfo(mark_info)  # 左上点，右下点
    # threshold for logic-1
    area_min = (abs(pt_xr - pt_xl) * abs(pt_yr - pt_yl)) / 16

    xyxy_lists = []  # 备选人体
    for det_index, (*xyxy, conf, cls) in enumerate(det[:, :6]):
        inter = IOU_limit(xycl=[x.item() for x in xyxy], Pts=[pt_xl, pt_yl, pt_xr, pt_yr])  # 第1个逻辑: 交集
        bottom_in = pt_yl < xyxy[3] < pt_yr  # 第2个逻辑: 脚在框内

        if not ((inter > area_min) and bottom_in.item()):
            continue  # 当前人体不在框内
        # 确认人体在框内，加入备选
        xyxy_lists.append((xyxy, conf, cls, inter))

    # 判断该帧 框内是否有人
    if len(xyxy_lists) == 0:
        return False  # 无人

    # 第3.0 逻辑：备选人体中距离框左侧最近
    # xyxy, conf, cls, inter = compare_xl_near(xyxy_lists, pt_xl)
    # 第3.1 逻辑，备选人体中交集面积inter 最大的
    xyxy, conf, cls, inter = compare_maxIOU(xyxy_lists)
    xyxy_exp = torch.tensor(xyxy).expand(det[:, :4].size(0), 4)  # 将xyxy扩展到det相同的行数
    _index = torch.where(torch.all(torch.eq(det[:, :4], xyxy_exp), dim=1))[0].item()  # 取出 与选定的xyxy一致的det[index]， 下标索引
    return _index  # 当前帧内,在框内且符合逻辑de, 最优人体idx


def standjump_logic(mark_info: dict, det=None):
    """
    # 单人立定跳远 触发逻辑： 判别人体是否在区域内的逻辑
    :param det: 每1张图上检测到的人体
    :param mark_info: 判定长宽比的阈值
    :return:
    """
    # 获取标定信息 - 最大xr，最小xl
    pt_xl, pt_yl, pt_xr, pt_yr, up_down_ptList = read_markInfo_standjump(mark_info)  # 左上点，右下点
    area_min = (abs(pt_xr - pt_xl) * abs(pt_yr - pt_yl))  # 标定框的面积
    # threshold for logic-1: 人体框下边缘中点 是否在区域内
    xyxy_lists = []  # 备选人体
    for det_index, xyxy in enumerate(det):
        xc, yc = xyxy[0] + (xyxy[2] - xyxy[0]) * 0.5, xyxy[3]
        if pt_xl <= xc <= pt_xr and pt_yl <= yc <= pt_yr:
            inter = IOU_limit(xycl=[x.item() for x in xyxy], Pts=[pt_xl, pt_yl, pt_xr, pt_yr])  # 第1个逻辑: 交集面积
            xyxy_lists.append((xyxy, inter))  # 确认人体框下边缘中心点(xc, yc)在标定区域内，加入备选

    # 判断该帧 框内是否有人
    if len(xyxy_lists) == 0:
        return False  # 无人

    # 第2.0 逻辑，备选人体中交集面积inter 最大的
    xyxy, conf, cls, inter = compare_maxIOU(xyxy_lists)
    xyxy_exp = torch.tensor(xyxy).expand(det[:, :4].size(0), 4)  # 将xyxy扩展到det相同的行数
    _index = torch.where(torch.all(torch.eq(det[:, :4], xyxy_exp), dim=1))[0].item()  # 取出 与选定的xyxy一致的det[index]， 下标索引

    return _index  # 当前帧内,在框内且符合逻辑de, 最优人体idx


def standjump_target_logic(mark_info: dict, det_track=None):
    """
    # 单人立定跳远-作弊识别-逻辑： 判别哪个人体框 为目标人体
    :param det_track:
    :param mark_info: 包含所有标定信息
    :return:  目标人体框 下标索引
    """
    # 获取标定信息 - 最大xr，最小xl
    pt_xl, pt_yl, pt_xr, pt_yr, up_down_ptList = read_markInfo_standjump(mark_info)  # 左上点，右下点
    area_min = (abs(pt_xr - pt_xl) * abs(pt_yr - pt_yl))  # 标定框的面积
    # threshold for logic-1: 人体框下边缘中点 是否在区域内
    xyxy_lists = []  # 备选人体
    # det_track_skpt = det_track[:, :4]
    for det_index, xyxy in enumerate(det_track):
        xc, yc = xyxy[0] + (xyxy[2] - xyxy[0]) * 0.5, xyxy[3]
        if pt_xl <= xc <= pt_xr and pt_yl <= yc <= pt_yr:
            inter = IOU_limit(xycl=[x.item() for x in xyxy], Pts=[pt_xl, pt_yl, pt_xr, pt_yr])  # 第1个逻辑: 交集面积
            xyxy_lists.append((xyxy, inter))  # 确认人体框下边缘中心点(xc, yc)在标定区域内，加入备选

    # 判断该帧 框内是否有人
    if len(xyxy_lists) == 0:
        return False  # 无人

    # 第2.0 逻辑，备选人体中交集面积inter 最大的
    xyxy, inter = compare_maxIOU(xyxy_lists)
    xyxy_exp = torch.tensor(xyxy).expand(det_track.shape[0], 4)  # 将xyxy扩展到det相同的行数
    _index = torch.where(torch.all(torch.eq(torch.tensor(det_track), xyxy_exp), dim=1))[
        0].item()  # 取出 与选定的xyxy一致的det[index]， 下标索引

    # track_id = det_track[_index, -1]         # 最后1列为 默认索引, 获取跟踪id
    # row_idx = np.where(det_track[:, -1].astype(int) == track_id)[0]  # 确定跟踪id所在行
    # return int(track_id), int(row_idx)  # 当前帧内,在框内且符合逻辑de, 最优人体idx

    return _index


def predel_marks_func(mark_info):
    # 找到垫子最远的M，N点，MN线段的方程, mark_info['mirror'] = True表示从左向右跳
    # 返回 垫子最远的上、下点，以及上下点的线段方程
    data_Pts = mark_info['MarkPts']
    sum_x, sum_y = 0, 0
    points = []
    for MarkPt in data_Pts:
        key_x = [k for k in MarkPt if k.endswith('_x')][0]
        key_y = [k for k in MarkPt if k.endswith('_y')][0]
        x, y = int(MarkPt[key_x]), int(MarkPt[key_y])
        sum_x += x;
        sum_y += y
        points.append((x, y))  # 存储点

    avg_y = sum_y / len(data_Pts)  # 计算y平均
    # 根据y平均分组
    group_up = [p for p in points if p[1] < avg_y]
    group_down = [p for p in points if p[1] > avg_y]
    # 计算y平均
    avg_y_up = sum(p[1] for p in group_up) / len(group_up)
    avg_y_down = sum(p[1] for p in group_down) / len(group_down)

    # 在组里找max和min的x值
    max_, min_ = float('inf'), float('-inf')

    if mark_info.get('mirror') == 'True':  # 从左向右跳
        max_x_up = max(group_up, key=lambda p: p[0] if group_up else (min_, min_))
        max_x_down = max(group_down, key=lambda p: p[0] if group_down else (min_, min_))  # z终点

        up_startLine = min(group_up, key=lambda p: p[0] if group_up else (min_, min_))
        down_startLine = min(group_down, key=lambda p: p[0] if group_down else (min_, min_))  # 起跳线

        return [max_x_up[0], max_x_up[1], max_x_down[0], max_x_down[1], avg_y_up, avg_y_down, up_startLine,
                down_startLine]
    else:
        min_x_up = min(group_up, key=lambda p: p[0] if group_up else (max_, max_))
        min_x_down = min(group_down, key=lambda p: p[0] if group_down else (max_, max_))

        up_startLine = max(group_up, key=lambda p: p[0] if group_up else (max_, max_))
        down_startLine = max(group_down, key=lambda p: p[0] if group_down else (max_, max_))
        # 返回 【上终点x,y, 下终点x,y, 上平均y, 下平均y, 上起点xy, 下起点xy】
        return [min_x_up[0], min_x_up[1], min_x_down[0], min_x_down[1], avg_y_up, avg_y_down, up_startLine,
                down_startLine]


def standjump_InAreafoot_logic(mark_info: dict, preds: torch.tensor, up_down_ptList: list):
    """
    # 单人立定跳远-脚检测判别-逻辑： 判别离摄像头最近的脚，是否在标定区域内  @Moss 20241125 14:28

    :param mark_info: 包含所有标定信息
    :param up_down_ptList: 跳远垫子的最远点[x, y, x, y, y的上平均，y的下平均, 上起点xy, 下起点xy]

    :param preds: 检测到的脚
    :return:
    """
    # 获取 垫子最远 Up，down点的方程de 斜率和轴距
    up_pt, down_pt, avg_y_up, avg_y_down, up_startLine_x, down_startLine_x = \
        (up_down_ptList[0], up_down_ptList[1]), (up_down_ptList[2], up_down_ptList[3]), \
        up_down_ptList[4], up_down_ptList[5], \
        up_down_ptList[6][0], up_down_ptList[7][0]  # 解包
    km, b = compute_line_func(up_down_ptList)
    Jump_LtoR = mark_info.get('mirror') == 'True'  # 是否 从左边跳
    # 判断脚是否在垫子内，可能有多只foot
    foots = []
    for foot in preds:
        ft_topL = (foot[0], foot[1])  # 左上 脚
        foot_inXd = max(up_startLine_x, down_startLine_x) < foot[0] < min(up_pt[0], down_pt[0]) if Jump_LtoR \
            else max(up_pt[0], down_pt[0]) < foot[2] < min(up_startLine_x, down_startLine_x)
        foot_inYd = avg_y_up < foot[3] and ft_topL[1] < avg_y_down

        # logic-1: 脚是否在y垫子区间内 && 在x方向不超过边界
        if not foot_inYd:
            continue  # 脚不在垫子区间内
        # logic-1.2:
        foot_Ptc = (foot[0], foot[3] - abs(foot[3] - foot[1]) / 3)  # def脚中心 在下边缘向上1/3倍脚处
        x_func = (foot_Ptc[1] - b) / km  # 带入边缘线段
        foot_inXFunc = foot_Ptc[0] < x_func if Jump_LtoR else foot_Ptc[0] > x_func  # 在线段函数内
        # logic-2.0:
        if foot_inXd and foot_inXFunc:  # 第1分支
            foots.append(foot)
            continue
        # if foot_inXd or foot_inXFunc:  # 第1分支
        #     foots.append(foot)
        # elif up_pt[1] < foot_Ptc[1] < down_pt[1] and foot_inXFunc:      # 不满足x方向上foot_inXd，第2分支[存疑？？]
        #     foots.append(foot)
        #     continue
        else:
            continue

    # 找出距离camera最近的foot
    near_foot = max(foots, key=lambda ft: ft[3]) if len(foots) else False
    if near_foot is not False:
        if Jump_LtoR:
            return near_foot if near_foot[0] > max(up_startLine_x, down_startLine_x) else False
        elif near_foot[0] < min(up_startLine_x, down_startLine_x):
            return near_foot
        else:
            return False
    else:
        return False

    # return near_foot
# def standjump_InAreafoot_logic(mark_info: dict, preds: torch.tensor, up_down_ptList: list):
#     up_pt = (up_down_ptList[0], up_down_ptList[1])
#     down_pt = (up_down_ptList[2], up_down_ptList[3])
#     avg_y_up = up_down_ptList[4]
#     avg_y_down = up_down_ptList[5]
#     up_startLine_x = up_down_ptList[6][0]
#     down_startLine_x = up_down_ptList[7][0]
#
#     # y = kx + b
#     km, b = compute_line_func(up_down_ptList)
#     Jump_LtoR = mark_info.get('mirror') == 'True'
#
#     foots = []
#     for foot in preds:
#         # 计算脚中心上方一点点的参考点
#         foot_Ptc = (foot[0], foot[3] - abs(foot[3] - foot[1]) / 3)
#         x_func = (foot_Ptc[1] - b) / km
#
#         # x方向是否在垫子区间
#         foot_inXd = max(up_startLine_x, down_startLine_x) < foot[0] < min(up_pt[0], down_pt[0]) if Jump_LtoR \
#             else max(up_pt[0], down_pt[0]) < foot[2] < min(up_startLine_x, down_startLine_x)
#
#         # y方向是否在垫子上下边界之间
#         foot_inYd = avg_y_up < foot[3] and foot[1] < avg_y_down
#
#         # 脚中心是否落在跳跃方向的合理区域（线段函数判定）
#         foot_inXFunc = foot_Ptc[0] < x_func if Jump_LtoR else foot_Ptc[0] > x_func
#
#         # 核心容忍逻辑：满足以下任意两个即可保留（适配“一脚内一脚外”情况）
#         conditions = [foot_inXd, foot_inXFunc, foot_inYd]
#         if sum(conditions) >= 1:
#             foots.append(foot)
#
#     # 从保留的脚中选出离相机最近的
#     near_foot = max(foots, key=lambda ft: ft[3]) if foots else False
#
#     if near_foot is not False:
#         if Jump_LtoR:
#             return near_foot if near_foot[0] > max(up_startLine_x, down_startLine_x) else False
#         else:
#             return near_foot if near_foot[0] < min(up_startLine_x, down_startLine_x) else False
#     else:
#         return False


def standjump_first_fall_logic(foot_a, foot_b):
    # 判断是否落地帧, 判断的是脚的左下角 位移 < 10pix
    x_footDL = abs(foot_a[0] - foot_b[0]) < 10
    y_footDL = abs(foot_a[3] - foot_b[3]) < 10
    if x_footDL and y_footDL:
        return True

    return False


def situp_target_logic_1(mark_info: float, det=None):
    """
    # 单人仰卧起坐 防作弊： 判别人体是否在区域内的逻辑
    :param det: 第1帧图上检测到的人体
    :param mark_info: 判定长宽比的阈值, 默认1.5
    :return: False 或目标索引
    """
    xyxy_lists, Max_area = [], None  # 备选人体
    for det_index, (*xyxy, conf, cls) in enumerate(det[:, :6]):
        width, height = abs(xyxy[2] - xyxy[0]), abs(xyxy[3] - xyxy[1])
        # threshold for logic-1: 人体躺平，宽高比>1.5, 在里面筛选面积最大的人体
        lay_down = width > mark_info * height
        if not lay_down:
            continue
        # threshold for logic-2: 框的面积最大
        if len(xyxy_lists):
            if width * height > Max_area:
                Max_area = width * height
                xyxy_lists = [(xyxy, conf, cls, det_index)]
        else:
            Max_area = width * height
            xyxy_lists.append((xyxy, conf, cls, det_index))

    # 判断该帧 框内是否有人
    if len(xyxy_lists) == 0:
        return False  # 无人

    xyxy_first = xyxy_lists[0][0]
    return xyxy_first  # 首个躺平的人体帧


def situp_target_logic_2(xyxy_first: any, det=None, area_max=1e4, thres_waist_foot=200):
    """
    # 单人仰卧起坐 防作弊： 判别人体是否在首帧xyxy框的区域内的逻辑
    :param thres_waist_foot:  腰-脚线的阈值 默认200pix
    :param area_max: 最大交集面积 1W pix
    :param det: 第1帧图上检测到的人体
    :param xyxy_first: 首帧人体框 / False(无人)
    :return: False 或目标索引
    """
    if not xyxy_first:
        return False

    width, height = abs(xyxy_first[2] - xyxy_first[0]), abs(xyxy_first[3] - xyxy_first[1])
    area_first = (width * height) / 3

    xyxy_lists, Max_area = [], None  # 备选人体
    for det_index, (*xyxy, conf, cls) in enumerate(det[:, :6]):
        inter = IOU_limit(xycl=[x.item() for x in xyxy], Pts=list(xyxy_first))  # 第1个逻辑: 交集
        delta_x = abs(det[det_index, 6:][[33, 36]].mean() - det[det_index, 6:][[45, 48]].mean())  # 第2个逻辑：X腰-脚线 是否伸展开
        if inter > max(area_max, area_first) and delta_x > thres_waist_foot:
            return det_index  # 该帧躺平的人体帧 的索引

    # 判断该帧 框内人是否符合条件
    if len(xyxy_lists) == 0:
        return False  # 无人/不符合条件


def sitreach_target_logic(mark_info: float, det=None, way=None):
    """
    # 单人坐位体前屈 防作弊：
    判别人体是否在区域内的逻辑 -> 确定主体det索引
    :param det: 每1张图上检测到的人体
    :param mark_info: 判定长宽比的阈值
    :param way: 朝向R 或 L
    :return: False 或目标索引
    """
    xyxy_lists, Max_waist = [], None  # 备选人体
    for det_index, (*xyxy, conf, cls) in enumerate(det):
        # 第1步先确定跨点朝向问题：
        one_skpts = det[det_index, 6:]
        xc_waist, yc_waist = one_skpts[[33, 36]].mean(), one_skpts[[34, 37]].mean()
        if way == 'L':  # 朝左边伸手
            x_waist, x_knee, x_foot = one_skpts[33], one_skpts[39], one_skpts[45]
            y_knee = one_skpts[40]
            y_foot = one_skpts[46]
        elif way == 'R':
            x_waist, x_knee, x_foot = one_skpts[36], one_skpts[42], one_skpts[48]  # 面朝右边伸手
            y_knee = one_skpts[43]
            y_foot = one_skpts[49]
        else:
            raise ValueError(f'get way is {way}')
        waist_knee, knee_foot = abs(x_waist - x_knee), abs(x_knee - x_foot)

        # 第2步 过滤 腿未伸直
        knee_lay_down = waist_knee * 7 > mark_info  # 大腿伸直
        foot_lay_down = knee_foot * 7 > mark_info  # 小腿伸直
        near_edge = y_knee * 2 > 1080  # 靠近camera
        knee_foot_lay = abs(y_knee - y_foot) < 35

        # print(Max_waist, knee_lay_down, foot_lay_down, end='\n')
        if not (knee_lay_down and foot_lay_down and near_edge and knee_foot_lay):
            # print(f'腿未伸直：knee_lay_down{knee_lay_down},foot_lay_down{foot_lay_down}, ')
            continue

        # 第3步：胯点y值最大 即 最靠近画面底部的为目标人体
        if len(xyxy_lists):
            if yc_waist > Max_waist:
                xyxy_lists.append((xyxy, conf, cls, det_index))
                Max_waist = yc_waist
                _index = det_index
        else:
            xyxy_lists.append((xyxy, conf, cls, det_index))
            Max_waist = yc_waist
            _index = det_index

    # 判断该帧 框内是否有人
    if len(xyxy_lists) == 0:
        return False  # 无人
    elif len(xyxy_lists) >= 1:
        return _index

    # _index = xyxy_lists[0][3]
    # print(f"Person_id:",_index)
    # return _index  # 当前帧内,在框内且符合逻辑de, 最优人体idx


def Multi_sitreach_target_logic(mark_info, det=None):
    """
    # 多人-坐位体前屈 防作弊：
    判别人体是否在区域内的逻辑 -> 确定主体det索引
    :param det: 每1张图上检测到的人体
    :param mark_info: list or dict

    :return: False 或目标索引
    """
    per_id_dict = {k:False for k in range(6)}
    index_map = {'L': [33, 39, 45, 34, 40, 46], 'R': [36, 42, 48, 37, 43, 49]}       # 17*3 骨骼索引
    area_max = -1
    pos_dict = mark_info[0]
    max_id, board_W, way_id = pos_dict['pos'], pos_dict['board_width'], pos_dict['way']
    for det_index, (*xyxy, conf, cls) in enumerate(det):
        # 第1步先确定跨点朝向问题：
        one_skpts = det[det_index, 6:]
        person = det[det_index, :4]
        # 人体与外扩板 交集面积最大原则
        area_id = IOU_limit(pos_dict['per_box'], person)
        if area_id > area_max:
            area_max = area_id
        else:
            # print(f"该人体不符合任何一个板扩展区域")
            continue

        # 第2步 过滤 腿未伸直
        x_waist, x_knee, x_foot, y_waist, y_knee, y_foot = [one_skpts[k] for k in index_map[way_id]]
        angle_SWK = compute_angle((x_waist, y_waist), (x_knee, y_knee), (x_foot, y_foot))
        # angle_SWK = 180-angle_SWK

        waist_knee, knee_foot = abs(x_waist - x_knee), abs(x_knee - x_foot)
        knee_lay_down = waist_knee * 7 > board_W  # 大腿伸直True
        foot_lay_down = knee_foot * 7 > board_W  # 小腿伸直True
        knee_foot_lay = abs(y_knee - y_foot) < 35       # 躺下

        if not (angle_SWK > 150 and knee_lay_down and foot_lay_down  and knee_foot_lay):
            print(f'腿未伸直：angle_SWK{angle_SWK}, knee_foot_lay{knee_foot_lay}, ')
            continue

        # 第3步：存储当前符合条件的最佳
        if area_max > 0:
            per_id_dict[max_id-1] = det[det_index]      # 存储当前最佳人体-> 指定位置

    return per_id_dict


# ----------------------------- 以下是 定位作弊嫌疑人 的逻辑-------------------------------------------------------------
def suspect_target_logic(num_bodys: int, index: int, det=None) -> Tuple[Tuple[int, list, list], str]:
    """
    定位 仰卧起坐,坐位体前屈 目标人体 周围可能的作弊嫌疑人
    这里的逻辑是取 TBS：
    T(Target),目标人体； B(Backside)，目标后面距离肩点最近手的人体； S(Side), 目标上侧边距离最近的人体
    :param index: 目标人体索引
    :param num_bodys: 最大作弊嫌疑人(不包括)
    :param det: 当前帧所有检测到的目标
    :return:
    """
    # 获取 目标人体T的 肩点中心作为圆心B， 最靠近上侧的手腕作为圆心S
    xc_shoulder, yc_shoulder = det[index, 6:][[15, 18]].mean(), det[index, 6:][[16, 19]].mean()
    if det[index, 6:][[33, 36]].mean() < det[index, 6:][[45, 48]].mean():  # 根据胯点和脚踝X的大小 确定朝向
        way = 'R'  # 朝右坐
        xf_wrist, yf_wrist = det[index, 6:][27], det[index, 6:][28]  # 取左手腕
        X_min = xc_shoulder if xf_wrist > xc_shoulder else xf_wrist  # 划分区域的交界点: 肩点/手腕点
    else:
        way = 'L'  # 朝左坐
        xf_wrist, yf_wrist = det[index, 6:][30], det[index, 6:][31]  # 右手腕
        X_min = xc_shoulder if xf_wrist < xc_shoulder else xf_wrist  # 划分区域的交界点: 肩点/手腕点

    # Y_max = yc_shoulder if yc_shoulder > yf_wrist else yf_wrist

    S_area, B_area = [], []
    for det_index, (*xyxy, conf, cls) in enumerate(det[:, :6]):
        if det_index == index:  # 这是目标人体
            continue
        # 判别嫌疑人属于哪个区域，用嫌疑人的胯点X作为划分标准
        xc_waist = det[det_index, 6:][[33, 36]].mean()
        if (xc_waist > X_min and way == 'R') or (xc_waist <= X_min and way == 'L'):
            dist_min = compute_area_person(det_index, det, target_point=(xf_wrist, yf_wrist))  # 计算区域内人体的手腕-T手腕距离
            S_area.append((dist_min, det_index))  # 嫌疑人在S区
        elif (xc_waist <= X_min and way == 'R') or (xc_waist > X_min and way == 'L'):
            dist_min = compute_area_person(det_index, det, target_point=(xc_shoulder, yc_shoulder))  # 计算区域内人体的手-T肩距离
            B_area.append((dist_min, det_index))  # 嫌疑人在B区

    # 对dist_min 的进行排序，并取前num个
    S_body_idx = get_near_suspect(S_area, bodys=1)
    B_body_idx = get_near_suspect(B_area, bodys=1)
    top_body_idx = (index, B_body_idx, S_body_idx)  # 将目标人体索引也作为嫌疑人放在第1个位置,构成 T-B-S

    return top_body_idx, way


# ------------------------------以下是 判别/记录状态帧 的逻辑 -----------------------------------------------------------------
def sitReach_status_judge_logic(way: str, det_index: torch.tensor, pred_board: torch.tensor) -> int:
    """
    坐位体前屈 判别目标人体的运动状态, 记录刻度值
    :way:
    :return:
    """
    xl_wrist, yl_wrist = det_index[27], det_index[28]
    xr_wrist, yr_wrist = det_index[30], det_index[31]
    board_width_extend = (pred_board[3] - pred_board[1]) / 5
    left_board = min(pred_board[0], pred_board[2]) - board_width_extend
    right_board = max(pred_board[0], pred_board[2]) + board_width_extend

    hand_in_board = left_board < xl_wrist < right_board and \
                    left_board < xr_wrist < right_board
    if not hand_in_board:
        print(f"hand_inBoard-{hand_in_board}", end=' ')
    # min(pred_board[1], pred_board[3]) < yl_wrist < max(pred_board[1], pred_board[3]) and \
    # min(pred_board[1], pred_board[3]) < yr_wrist < max(pred_board[1], pred_board[3])

    # 记录在板子上的刻度
    if hand_in_board and way == 'R':
        scale_val = min(xl_wrist, xr_wrist) - left_board
    elif hand_in_board and way == 'L':
        scale_val = right_board - max(xl_wrist, xr_wrist)
    else:
        scale_val = 0

    return int(scale_val)


def sitUp_status_judge_logic(way: str, det_index: torch.tensor) -> int:
    """
    仰卧起坐 判别目标人体的运动状态, 记录躺平和促膝角度[肩-腰-膝]
    :way: 确定主体朝向
    :det_index: 主体姿态点
    :return: 角度
    """
    xl_shoulder, yl_shoulder, xl_waist, yl_waist, xl_knee, yl_knee = det_index[15], det_index[16], det_index[33], \
                                                                     det_index[34], det_index[39], det_index[40]
    xr_shoulder, yr_shoulder, xr_waist, yr_waist, xr_knee, yr_knee = det_index[18], det_index[19], det_index[36], \
                                                                     det_index[37], det_index[42], det_index[43]

    xc_shoulder, yc_shoulder = (xl_shoulder + xr_shoulder) * 0.5, (yl_shoulder + yr_shoulder) * 0.5
    xc_waist, yc_waist = (xl_waist + xr_waist) * 0.5, (yl_waist + yr_waist) * 0.5
    xc_knee, yc_knee = (xl_knee + xr_knee) * 0.5, (yl_knee + yr_knee) * 0.5
    angle_SWK = compute_angle(shoulder=(xc_shoulder, yc_shoulder), waist=(xc_waist, yc_waist), knee=(xc_knee, yc_knee))

    return int(angle_SWK)


# -----------------------------------------以下是 划分样本区间 的逻辑----------------------------------------------------
def find_seqParts(status_list, total_frames, interval_frames):
    """
    单人实心球 触发项目
    # 双端队列实现，右加-左删-右加....(增删的是下标索引), 时间复杂度O(len(status_list))
    :param status_list: 状态列表,包含元素 -1:表示画面无人， 0: 表示标定内无人或不符合逻辑 ，1: 表示框内有最佳人体
    :param total_frames: 采集样本持续的帧数
    :param interval_frames: 最小采集的间隔帧
    :return: list
    """
    seq_parts, current_seq = [], deque()  # 初始化 存储序列，双端序列片段
    if len(status_list) < total_frames:
        return []
    for idx, statue in enumerate(status_list):
        if statue == 1:
            current_seq.append(idx)  # 当遇到1时，会把当前的索引添加到current_sequence的右边。(右加)
            if len(current_seq) > total_frames:
                current_seq.popleft()  # 如果current_sequence的长度超过了total_frames，就从左边删除1个元素(左删)。
            if len(current_seq) == total_frames:
                seq_parts.append(list(current_seq))  # 存储
                [current_seq.popleft() for _ in range(interval_frames) if current_seq]  # 继续左删，使其达到下一个起点
        else:
            current_seq.clear()  # 如果遇到的不是1，就清空current_sequence
    return seq_parts


def find_fakeParts(status_list, len_sample, act_frames):
    """
    # 立定跳远作弊  有效样本分析
    # 新增落地帧 定义 状态帧中出现落地帧时，截取首次出现落地帧作为样本结束帧      @Moss 2024127 10:44
    :param status_list: 状态列表,包含元素 -1:表示画面无人， 0: 在准备区域内 ，1: 起跳/作弊/离开, 2: 垫子内的落地帧  -2：跳出画面/跟踪丢失
    :param len_sample: 需要获取的样本长度(送入模型的样本长度)，默认75帧
    :param act_frames: 起跳/作弊/离开 需要的帧数， 默认60帧
    :return: list
    """
    zero, one = 0, 0  # 0, 1的个数总和
    zero_idx, one_idx = [], []

    # 状态帧中出现2\3\4状态时， 截断样本
    index_val = sorted([status_list.index(val) for val in [2, 3, 4] if val in status_list])

    # index_2 = status_list.index(2) if 2 in status_list else None
    if len(index_val):
        status_list = status_list[: index_val[0] + 1]
        status_list[-1] = 1  # 将首次落地状态index_val[0] 元素，改回1

    # 将status_list状态列表中 起跳后的值默认全改为1(即使又退回准备区)
    first_one = False
    for i, status in enumerate(status_list):
        if status == 1:
            first_one = True
            one_idx.append(i)
            one += 1
        elif first_one and status == 0:
            one_idx.append(i)  # 这里是第2次回到准备区域，记录为运动状态(1)
            one += 1
        elif status == 0:
            zero_idx.append(i)
            zero += 1

    # judge
    len_sample_zero = len_sample - act_frames  # 默认15帧
    if one <= 3:  # 跟踪目标没有离开标定区域
        indexes = sample_list(zero_idx, 75)  # 从1前面所有的0中 抽样
    elif one + zero <= len_sample or zero == 0:
        indexes = zero_idx + one_idx  # 总长度不够，全部取出来
    else:
        first_one_id = one_idx[0]  # 第1个1的id
        near_zero_idx = min(enumerate(zero_idx), key=lambda x: abs(first_one_id - x[1]))[0]  # 最接近首个1的 0的id
        if one <= act_frames:
            zero_end_id = near_zero_idx - (act_frames - one)  # 需要截取
            first_zero_id = zero_idx[: zero_end_id]  # 真正需要的0的序列(未采样)
            sample_zeros = sample_list(first_zero_id, len_sample_zero)  # 从1前面所有的0中 抽样
            fill_zero_id = [x for x in zero_idx if x not in first_zero_id][: act_frames - len(one_idx)]

            indexes = sample_zeros + fill_zero_id + one_idx  # 加上所有的1
        else:  # 运动状态的时间 超过60帧
            first_zero_id = zero_idx[: near_zero_idx]  # 真正需要的0的序列(未采样)
            sample_zeros = sample_list(first_zero_id, len_sample_zero)  # 从1前面所有的0中 抽样

            # if len(sample_zeros)
            # sample_ones = one_idx[: act_frames]  # 顺序取60个1
            sample_ones = sample_list(one_idx, act_frames)  # 抽样取60个1

            indexes = sample_zeros + sample_ones

    indexes.sort()
    return indexes


def find_sitReach_fakeParts(status_list, len_min=25):
    """
    # 坐位体前屈作弊  有效样本分析
    :param status_list: 状态列表存储的是手腕点在板子上的刻度,包含元素 0～scale，刻度最大表示推到最远的位置
    :param len_min: 最短样本长度
    :return: list
    """
    # 从status_list状态列表中 获取起始点{ start:首个>0的值的下标， end:最大值的下标 }
    start_idx = next((s_idx for s_idx, scale in enumerate(status_list) if scale > 0), 0)
    end_idx, _ = max(enumerate(status_list), key=lambda x: (x[1], x[0]))  # 有多个最大值时，返回最后1个最大值
    if max(status_list) <= 2:  # 兼容状态列表全0的情况
        indexes = list(range(start_idx, end_idx + 1))
        return indexes
    # judge
    elif end_idx - start_idx + 1 < len_min:
        start_part = status_list[start_idx:]
        # 过了最远刻度后，获取的第1个0刻度的帧数 或 到结束帧
        add_frames = next(
            (s_idx for s_idx, scale in enumerate(start_part) if scale == 0 or s_idx == len(start_part) - 1), 0)
        if add_frames > len_min:  # 向后补到15帧
            indexes = list(range(start_idx, start_idx + len_min))
        else:  # 向后补不足，再向前补
            indexes = list(range(start_idx - len_min + add_frames, start_idx + add_frames))
    else:
        indexes = list(range(start_idx, end_idx + 1))

    return indexes


def find_sitUp_fakeParts(status_list, peak_valley=[115, 95], len_sample=None):
    """
    # 仰卧起坐 防作弊  有效样本分析——峰-谷片段
    （1）降噪：高斯滤波器平滑波形
    （2）使用机器学习的scipy.signal.find_peaks识别峰谷       # 实际上还可以计算1阶导斜率、使用2阶导正负变化 来判别极值点
    （3）根据阈值筛选 连续的峰值-谷值片段
    :param peak_valley: 峰谷值的阈值
    :param status_list: 状态列表, 肩-腰-膝 的角度变化列表
    :param len_sample: 需要获取的样本长度(送入模型的样本长度)，
    :return: list[list,...]
    """
    # 从status_list状态列表中 获取起始点{ start:， end: }
    from scipy.signal import find_peaks
    from scipy.ndimage import gaussian_filter

    int_list = np.array(status_list)
    smooth_signal = gaussian_filter(int_list, sigma=1)  # 高斯滤波 降噪

    peaks, _ = find_peaks(smooth_signal, height=peak_valley[0])  # 机器学习寻找波峰
    valleys, _ = find_peaks(-smooth_signal, height=-peak_valley[1])  # 寻找波谷

    peak_info = [p_idx for p_idx, val in zip(peaks, int_list[peaks]) if
                 status_list[p_idx] > peak_valley[0]]  # 过滤小于115°的峰
    valley_info = [v_idx for v_idx, val in zip(valleys, -smooth_signal[valleys]) if
                   status_list[v_idx] < peak_valley[1]]  # 过滤大于95°的谷

    # 匹配 有效峰-有效谷
    all_samples = list()
    for start_idx in peak_info:
        end_idx = min((vi for vi in valley_info if vi > start_idx), default=-1, key=lambda vi: (vi - start_idx, vi))
        len_index = end_idx - start_idx
        if len_index < 4:
            continue
        if len_index > len_sample:
            indexes = sample_list(lst=list(range(start_idx, end_idx + 1)), num_samples=len_sample)  # 均匀抽样到len_sample
        else:
            indexes = list(range(start_idx, end_idx + 1))
        all_samples.append(indexes)

    return all_samples


# ------------------------------------------以下是 作弊盲区范围 逻辑-------------------------------------------------------------------------
def blind_area_sitReach(main_skpt, main_box, det):
    """
    设定坐位体前屈 盲区：根据主体框和主体关键点设定盲区范围为梯形
    return 盲区区域
    """

    return


# ------------------------------------------以下是 50m 逻辑 -------------------------------------------------------------------

def point_in_polygon(x, y, points:list) -> bool:
    """
    # 50m标定框交集逻辑
    面积法判定(x,y)是否在points构成的四边形 内,
    TODO 要注意线段的位置和顺序
    Args:
        x, y: 检测框 的底边中点
        # 标定点: TrackLine0_UpMark, TrackLine0_MiddleMark，TrackLine1_UpMark, TrackLine1_MiddleMark
        points: [(x1,y1), (x2,y2), (x3,y3), (x4,y4)]

    Returns: satisfy True|False

    """
    def area(x1, y1, x2, y2, x3, y3):   # △面积
        return abs((x2-x1)*(y3-y1) - (y2-y1)*(x3-x1)) / 2.0

    points.sort(key=lambda p:(p[1],p[0]))
    if points[0][0] > points[1][0]:
        points[0], points[1] = points[1], points[0]
    if points[2][0] < points[3][0]:
        points[2], points[3] = points[3], points[2]


    x1, y1 = points[0]
    x2, y2 = points[1]
    x3, y3 = points[2]
    x4, y4 = points[3]

    A = area(x1, y1, x2, y2, x3, y3) + area(x1, y1, x4, y4, x3, y3)
    A1 = area(x, y, x1, y1, x2, y2)
    A2 = area(x, y, x2, y2, x3, y3)
    A3 = area(x, y, x3, y3, x4, y4)
    A4 = area(x, y, x1, y1, x4, y4)

    return A == (A1 + A2 + A3 + A4)


def compare_inter_max_EndCam(xyxy_list, points):
    """
    定义的50m终点画面的 指定起点可视区域
    在区域内的人体 面积最大的人
    """
    if xyxy_list is None:
        return None

    areas_list = []
    for xycl in xyxy_list:
        # 比较 ∩最大的bbox
        b1_x1, b1_y1, b1_x2, b1_y2 = xycl[0], xycl[1], xycl[2], xycl[3]
        # 判断 脚踝点 是否在区域内
        foot_pt = (xycl[6+45]+ xycl[6+48])*0.5, (xycl[6+46]+ xycl[6+49])*0.5
        if not point_in_polygon(foot_pt[0], foot_pt[1], list(points)):
            inter = 0
        else:   # 计算交集面积
            inter = overlap_area(b1_x1, b1_y1, b1_x2, b1_y2, points)

        areas_list.append(inter)

    max_area = max(areas_list)
    if max_area > 0:
        max_id = areas_list.index(max(areas_list))
        return xyxy_list[max_id]
    else:
        return None


def compare_inter_max(xyxy_list, points):
    """
    50m起点画面 交集面积最大的人体
    """
    if len(xyxy_list) == 1:
        return xyxy_list[0][0], xyxy_list[0][1], xyxy_list[0][2]
    else:
        areas_list = []
        for xycl in xyxy_list:
            # 比较 ∩最大的bbox
            b1_x1, b1_y1, b1_x2, b1_y2 = xycl[0][0], xycl[0][1], xycl[0][2], xycl[0][3]
            # 判断下边缘中点是否在区域内
            if not point_in_polygon((b1_x2 + b1_x2) * 0.5, b1_y2, list(points)):
                inter = 0
            else:  # 计算交集面积
                inter = overlap_area(b1_x1, b1_y1, b1_x2, b1_y2, points)
            areas_list.append(inter)

        max_id = areas_list.index(max(areas_list))
        return xyxy_list[max_id][0], xyxy_list[max_id][1], xyxy_list[max_id][2]


def overlap_area(x1, y1, x2, y2, points):
    """
    判断检测框矩形和标定框四边形的交集
    Args:
        x1,y1,x2,y2: 预测框左上右下坐标 xyxy
        points: 标定框[(x1,y1), (x2,y2), (x3,y3), (x4,y4)]

    Returns:

    """
    x5, y5 = points[0]
    x6, y6 = points[1]
    x7, y7 = points[2]
    x8, y8 = points[3]

    # 计算两个矩形的左上角和右下角坐标
    l1, r1, t1, b1 = min(x1, x2), max(x1, x2), min(y1, y2), max(y1, y2)
    l2, r2, t2, b2 = min(x5, x6, x7, x8), max(x5, x6, x7, x8), min(y5, y6, y7, y8), max(y5, y6, y7, y8)

    # 计算两个矩形在水平方向和垂直方向上的重叠长度
    w_overlap = max(0, min(r1, r2) - max(l1, l2))
    h_overlap = max(0, min(b1, b2) - max(t1, t2))

    # 计算交集面积
    area = w_overlap * h_overlap
    return area


def Multi_location_50mStart(width, parse_:dict):
    """
    input multi_location_list,
    # * 50mProj each school have a json file, no need resize_to2k_point
    """
    width = 1920
    # 判断标定是否与视频匹配
    max_pix = max(val for inner_dict in parse_.values()
                  for val in (inner_dict.values() if isinstance(inner_dict, dict) else [inner_dict]))
    close_rate_func = lambda x: min([(1920 / 2560), (1920 / 3840), (1920 / 1920)], key=lambda r: abs(r - x))

    rate = close_rate_func(1920 / max_pix)      # [1, 0.75, 0.5]
    parse_ = {k: {kk:int(vv*rate) for kk,vv in v.items()} if isinstance(v, dict)
             else v for k,v in parse_.items()}

    def line_segment(x_mid, y_mid, x_end, y_end,k=1/3) -> tuple:
        # 线段分割公式：取k=1/3
        x = (x_mid + k * x_end) / (1+k)
        y = (y_mid + k * y_end) / (1+k)
        return x, y

    # img1 = cv2.resize(im0s, (2560, 1440), interpolation=cv2.INTER_LINEAR)
    P1s, P2s = {}, {}
    for location in range(int(parse_['LocationNum'])+1):
        strExt = "TrackLine" + str(location)
        if int(parse_['Direction']) == 1:   # 需要将对应的标定框镜像处理 2560-x
            x1 = width - parse_[strExt].get("UpMark_x"); y1 = parse_[strExt].get("UpMark_y"); P1 = [x1, y1]
            x2 = parse_[strExt].get("MiddleMark_x"); y2 = parse_[strExt].get("MiddleMark_y")
            x3 = parse_[strExt].get("EndMark_x"); y3 = parse_[strExt].get("EndMark_y")
            x_mid, y_mid =line_segment(x2, y2, x3, y3, k=1/3)
            x_mid = width - x_mid
            P2 = [x_mid, y_mid]
            # cv2.circle(img1, (int(x1), int(y1)), radius=9, color=(0, 255, 0), thickness=-1)
            # cv2.circle(img1, (int(x2), int(y2)), radius=9, color=(0, 255, 0), thickness=-1)
            # cv2.circle(img1, (int(x3), int(y3)), radius=9, color=(0, 255, 0), thickness=-1)

        else:
            x1 = parse_[strExt].get("UpMark_x"); y1 = parse_[strExt].get("UpMark_y"); P1 = [x1, y1]
            x2 = parse_[strExt].get("MiddleMark_x"); y2 = parse_[strExt].get("MiddleMark_y")
            x3 = parse_[strExt].get("EndMark_x"); y3 = parse_[strExt].get("EndMark_y")
            x_mid, y_mid =line_segment(x2, y2, x3, y3, k=1/3)
            P2 = [x_mid, y_mid]
            # cv2.circle(img1, (int(x1), int(y1)), radius=9, color=(0, 255, 0), thickness=-1)
            # cv2.circle(img1, (int(x2), int(y2)), radius=9, color=(0, 255, 0), thickness=-1)
            # cv2.circle(img1, (int(x3), int(y3)), radius=9, color=(0, 255, 0), thickness=-1)
            # cv2.circle(img1, (int(x_mid), int(y_mid)), radius=9, color=(0, 0, 255), thickness=-1)

        P1s[location] = P1;     P2s[location] = P2
    # cv2.imwrite('2024_10_18_8.jpg', img1)
    return P1s, P2s


def Multi_location_50mEnd(width, parse_:dict):
    """
    input multi_location_list,
    # * 50mProj each school have a json file, no need resize_to2k_point
    """
    # 标定默认采用1080p的像素标定
    if not width == 1920:
        # 视频与标定不匹配，需要修改标定
        raise ValueError(f"需采用1920的像素画面标定")

    P1e, P2e, P3e = {}, {}, {}
    for location in range(int(parse_['LocationNum'])+1):
        strExt = "TrackLine" + str(location)
        if int(parse_['Direction']) == 1:   # 需要将对应的标定框镜像处理 1920-x
            x1 = width - parse_[strExt].get("UpMark_x"); y1 = parse_[strExt].get("UpMark_y")
            x2 = width - parse_[strExt].get("MiddleMark_x"); y2 = parse_[strExt].get("MiddleMark_y")
            x3 = width - parse_[strExt].get("EndMark_x"); y3 = parse_[strExt].get("EndMark_y")
        else:
            x1 = parse_[strExt].get("UpMark_x"); y1 = parse_[strExt].get("UpMark_y")
            x2 = parse_[strExt].get("MiddleMark_x"); y2 = parse_[strExt].get("MiddleMark_y")
            x3 = parse_[strExt].get("EndMark_x"); y3 = parse_[strExt].get("EndMark_y")

        P1, P2, P3 = [x1, y1], [x2, y2], [x3, y3]
        P1e[location] = P1;     P2e[location] = P2;      P3e[location] = P3


    return P1e, P2e, P3e


def overline_end(tracks, P3e):
    """
    # 判断目标人体是否过线, 存储返回过线人体的跟踪id
    """
    grade_person = {pos:[] for pos in range(len(P3e) - 1)}      # 存储过线人体的下标
    for pos in range(len(P3e) - 1):
        line_end = [P3e[pos][0], P3e[pos][1], P3e[pos + 1][0], P3e[pos + 1][1]]         # 终点线段
        delt_y_pos = (line_end[1] + line_end[3]) / 2
        # 单个测试跑道，循环所有检测到的人，找到符合过线条件逻辑的人
        for det_index, xyxy_track in enumerate(tracks):
            xc = xyxy_track[0] + 0.5 * (xyxy_track[2] - xyxy_track[0])
            within_deltx = min(line_end[0], line_end[2]) < xc < max(line_end[0], line_end[2])
            yc = xyxy_track[3]
            if yc > delt_y_pos:     # 确定过线
                if within_deltx:        # 确定在指定跑道内
                    grade_person.get(pos).append(xyxy_track[-4])     # 将出成绩的人体 跟踪id 存储

    # 是否有人过线
    overline_Person = not all(v==[] for v in grade_person.values())

    return grade_person if overline_Person else False


def calculate_region_r50(s1s2: list, m1m2:list, p0: Tuple) -> list:
    """
    s1s2: 起点线段s1s2,
    p0: 连续跟踪id的首帧 人体, 需要取下边缘中点;
    需要 M1,M2的坐标
    满足线段p1p2平行于s1s2，且过P0点，S1和S2起点坐标

        # 参数化P1在S1M1上的位置，参数为t
    # P1 = S1 + t * direction_s1m1
    # 同理，P2 = S2 + t * direction_s2m2

    # 假设P1P2经过点P，我们可以建立方程来解t
    # 方程基于P在P1P2线段上，即存在某个s使得 P = P1 + s*(P2 - P1)

    # 代入P1和P2的表达式：
    # P = S1 + t * direction_s1m1 + s*(S2 + t * direction_s2m2 - (S1 + t * direction_s1m1))

    # 假设s=1，即P位于P1和P2之间：方程变为 P = P1 + (P2 - P1)
    # 即 P = (P1 + P2) / 2，这里假设P是P1P2的中点

    # 根据这个假设，我们可以得到：
    # P = (S1 + t * direction_s1m1 + S2 + t * direction_s2m2) / 2
    # 解这个方程得到t的值

    return: 四边形s1, s2, p2, p1
    """
    s1, s2 = (s1s2[0], s1s2[1]), (s1s2[2], s1s2[3])
    m1, m2 = (m1m2[0], m1m2[1]), (m1m2[2], m1m2[3])
    if m1m2[0] > m1m2[1]:
        m1, m2 = m2, m1
    if s1s2[0] > s1s2[2]:
        s1, s2 = s2, s1     # s1靠画面左边
    p0 = (int(p0[0] + p0[2])*0.5), int(p0[3])
    # 如果P0离起点很近(10个像素)，则不计算区域
    if p0[1] - (s1s2[1] + s1s2[3])*0.5 < 10:
        return [None,None, None, None]


    # 将坐标转换为numpy数组
    s1 = np.array(s1)
    s2 = np.array(s2)
    m1 = np.array(m1)
    m2 = np.array(m2)
    p = np.array(p0)

    # 计算线段S1M1和S2M2的方向向量
    direction_s1m1 = m1 - s1
    direction_s2m2 = m2 - s2

    # TODO：注意在图像坐标系中，y轴向下增加，需要调整方向向量的符号 [-1.0, 1.0]
    # 计算系数矩阵和右侧向量
    A = np.column_stack((direction_s1m1 + direction_s2m2, np.array([-1.0, 1.0])))
    b = p - (s1 + s2) / 2

    # 解线性方程组 A * [t, c] = b
    try:
        t, c = np.linalg.solve(A, b)
    except np.linalg.LinAlgError:
        # 如果矩阵不可逆，使用最小二乘法求解
        t, c, _, _ = np.linalg.lstsq(A, b, rcond=None)

    # 计算P1和P2的坐标
    p1 = s1 + t * direction_s1m1
    p2 = s2 + t * direction_s2m2

    s1 = tuple(np.round(s1).astype(int))
    s2 = tuple(np.round(s2).astype(int))
    p2 = tuple(np.round(p2).astype(int))
    p1 = tuple(np.round(p1).astype(int))

    return [s1, s2, p2, p1]


# -----------------以下是pull_up 逻辑部分
def getHandsHead(hands,heads):
    left_hand,right_hand,head =[],[],[]
    if hands[0][0]< hands[1][0]:
        left_hand = hands[0]
        right_hand = hands[1]
    else:
        left_hand =hands[1]
        right_hand = hands[0]
    middle_heads = heads[left_hand[0]<heads[:,0]]
    middle_heads = middle_heads[middle_heads[:, 0] < right_hand[0]]
    # 返回头面积最大的那个
    if middle_heads is not None and len(middle_heads)!=1:
        area =0
        head_top1,head_top2=[],[]
        for middle_head_new in middle_heads:
            xyxy = middle_head_new[:4]
            area_0 =(xyxy[2]-xyxy[0])*(xyxy[3]-xyxy[1])
            if(area_0>area):
                area= area_0
                head_top2 = head_top1
                head_top1 = middle_head_new
        if head_top2 is not None and len(head_top2) != 0 and (head_top2[1]- left_hand[1]) <(head_top1[1]- left_hand[1]):
            head = head_top2
        else:
            head = head_top1

    elif len(middle_heads)==1:
        head = middle_heads[0]
    return left_hand,right_hand,head


def handlerupcountframes(top_line, head_top_line,bottom_line,frame_id,up,down,head):
    y = head[1]
    if y>head_top_line and not up and not down:
        up = False
        down = False
    elif y<head_top_line and not down:
        up=True
        down = False
    elif y>head_top_line and up and not down:
        up =False
        down = True
    elif y>head_top_line and y<bottom_line and not up and down:
        up =False
        down = True
    elif y>bottom_line and not up and down:
        up = False
        down = False
    return up,down


def getRatioIndexs(pkl_path):
    real_num_len = 16  # 16帧
    num_len = 14  # 16帧中保留前1帧和最后1帧
    with open(pkl_path, 'rb') as f:
        pklinfo = pickle.load(f)
        persons = list(pklinfo.values())
        dp = [1] * len(persons)
        for i in range(1, len(persons)):
           # print(persons[i]['head'][1])
            if persons[i]['head'][1] < persons[i - 1]['head'][1]:
                dp[i] = dp[i - 1] + 1
            elif persons[i]['head'][1] == persons[i - 1]['head'][1]:
                dp[i] = dp[i - 1]

        #print(dp)
        #print(max(dp))
        filterDp = dp[::-1]
        new_dp = []
        for i in range(1, len(filterDp)):
            if filterDp[i - 1] > 1:
                new_dp.append(filterDp[i - 1])
            elif filterDp[i - 1] == 1 and len(new_dp) < num_len:
                new_dp.append(filterDp[i - 1])
            elif filterDp[i - 1] == 1 and len(new_dp) == num_len:
                new_dp.append(filterDp[i - 1])
                break
            elif filterDp[i - 1] == 1 and len(new_dp) > num_len:
                new_dp.append((filterDp[i - 1]))
                break

        new_dp = new_dp[::-1]
        ratioDP = []
        outIndexs = []
        firstIndex, lastIndex = 0, 0
        if len(new_dp) < real_num_len:
            startIndex = (len(dp) - real_num_len)
        else:
            startIndex = (len(dp) - len(new_dp))
        endIndex = len(dp)

        for index in range(startIndex, endIndex+1):
            if index == startIndex:
                firstIndex = index
            elif index == endIndex:
                lastIndex = index
            else:
                ratioDP.append(index)

        # print("ratioDP",ratioDP)

        avg_interval = len(ratioDP) / float(num_len)
        base_offsets = np.arange(num_len) * avg_interval
        clip_offsets = base_offsets + avg_interval / 2.0
        frame_inds = clip_offsets[:, None] + np.arange(
            1)[None, :]
        frame_inds = np.concatenate(frame_inds)
        frame_inds = np.mod(frame_inds, len(ratioDP))
        frame_inds = frame_inds.astype(np.int32)

        outIndexs.append(firstIndex)
        for i in frame_inds:
            outIndex = ratioDP[i]
            outIndexs.append(outIndex)
        outIndexs.append(lastIndex)
        return outIndexs


def getCropBoxByPkl(pkl_path,height,precent):
    with open(pkl_path, 'rb') as dt:
        hands = list(pickle.load(dt).values())[0]
        left_hand = hands["left_hand"]
        right_hand = hands['right_hand']
        level_witdh = right_hand[2]-left_hand[0]
        level_Y = (left_hand[1]+left_hand[3]+right_hand[1]+right_hand[3])/4
        top_x =int(left_hand[0]-level_witdh)
        if top_x<0:
            top_x=0
        top_point = [top_x,int(level_Y)]
        botton_x=int(right_hand[2] + level_witdh)
        if botton_x>height:
            botton_x =height
        bottom_point = [botton_x,int(level_Y+(height-level_Y)*precent)]
        return top_point,bottom_point


def getRatioIndexs(pkl_path):
    real_num_len = 16  # 16帧
    num_len = 14  # 16帧中保留前1帧和最后1帧
    with open(pkl_path, 'rb') as f:
        pklinfo = pickle.load(f)
        persons = list(pklinfo.values())
        dp = [1] * len(persons)
        for i in range(1, len(persons)):
           # print(persons[i]['head'][1])
            if persons[i]['head'][1] < persons[i - 1]['head'][1]:
                dp[i] = dp[i - 1] + 1
            elif persons[i]['head'][1] == persons[i - 1]['head'][1]:
                dp[i] = dp[i - 1]

        #print(dp)
        #print(max(dp))
        filterDp = dp[::-1]
        new_dp = []
        for i in range(1, len(filterDp)):
            if filterDp[i - 1] > 1:
                new_dp.append(filterDp[i - 1])
            elif filterDp[i - 1] == 1 and len(new_dp) < num_len:
                new_dp.append(filterDp[i - 1])
            elif filterDp[i - 1] == 1 and len(new_dp) == num_len:
                new_dp.append(filterDp[i - 1])
                break
            elif filterDp[i - 1] == 1 and len(new_dp) > num_len:
                new_dp.append((filterDp[i - 1]))
                break

        new_dp = new_dp[::-1]
        ratioDP = []
        outIndexs = []
        firstIndex, lastIndex = 0, 0
        if len(new_dp) < real_num_len:
            startIndex = (len(dp) - real_num_len)
        else:
            startIndex = (len(dp) - len(new_dp))
        endIndex = len(dp)

        for index in range(startIndex, endIndex+1):
            if index == startIndex:
                firstIndex = index
            elif index == endIndex:
                lastIndex = index
            else:
                ratioDP.append(index)

        # print("ratioDP",ratioDP)

        avg_interval = len(ratioDP) / float(num_len)
        base_offsets = np.arange(num_len) * avg_interval
        clip_offsets = base_offsets + avg_interval / 2.0
        frame_inds = clip_offsets[:, None] + np.arange(
            1)[None, :]
        frame_inds = np.concatenate(frame_inds)
        frame_inds = np.mod(frame_inds, len(ratioDP))
        frame_inds = frame_inds.astype(np.int32)

        outIndexs.append(firstIndex)
        for i in frame_inds:
            outIndex = ratioDP[i]
            outIndexs.append(outIndex)
        outIndexs.append(lastIndex)
        return outIndexs



if __name__ == '__main__':
    pass
