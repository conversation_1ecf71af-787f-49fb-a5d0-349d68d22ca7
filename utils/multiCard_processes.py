# -*-coding:utf-8-*-
import copy
import multiprocessing as mp
from pathlib import Path


def split_sub_vid_Up(vid_path:str) ->list:
    """将视频路径获取到的mp4文件划分到k个子集列表"""
    sub_dirs = [str(sub) for sub in Path(vid_path).rglob('*.mp4') if sub.stat().st_size / 1024 > 1e4]  # 获取路径下的所有文件, 然后做匹配
    print(f"len(sub_dirs) is {len(sub_dirs)}")

    avg = int(len(sub_dirs) // 100) + 1         # 超过100， 按100的倍数叠加
    sub_k_dirs = [sub_dirs[i:i + avg] for i in range(0, len(sub_dirs), avg)]
    assert len(sub_dirs) > 0, f"No mp4 videos in {sub_dirs}"


    return sub_k_dirs



def split_sub_vid_reach(vid_path:str) ->list:
    """将视频路径获取到的mp4文件划分到k个子集列表"""
    sub_dirs = [str(sub) for sub in Path(vid_path).rglob('*.mp4') if sub.stat().st_size / 1024 > 100]  # 获取路径下的所有文件, 然后做匹配, >100kb的视频
    print(f"len(sub_dirs) is {len(sub_dirs)}")

    avg = int(len(sub_dirs) // 20) + 1         # 超过20， 按20的倍数叠加
    sub_k_dirs = [sub_dirs[i:i + avg] for i in range(0, len(sub_dirs), avg)]
    assert len(sub_dirs) > 0, f"No mp4 videos in {sub_dirs}"


    return sub_k_dirs


# -------------------------------------------主进程--------------------------------------------------------------------------------

def multiprocess_main_reach(dirs, process_func, arg):
    process_num_CPU = arg.process_num_CPU       # 设置 进程数
    card_ids = [int(card_id) for card_id in arg.card_ids.split(',')]      # 获取可用设备列表

    sub_dirs = split_sub_vid_reach(dirs)      # 划分子列表

    allocate_List = []
    for i, sub_dir in enumerate(sub_dirs):
        arg_branch = copy.deepcopy(arg)
        arg_branch.card_id = card_ids[i % len(card_ids)]            # 分配GPU设备
        # print(f"@Moss: we find and use device-id:{arg_branch.card_id} for free cuda infer")
        allocate_List.append((arg_branch, sub_dir))

    print(f'Processes Num:',process_num_CPU)
    with mp.get_context('spawn').Pool(processes=process_num_CPU) as pool:
        pool.starmap(process_func, allocate_List)           # 主函数

    return


def multiprocess_main_up(arg, process_subdirs):
    dirs = arg.dirs
    process_num_CPU = arg.process_num_CPU       # 设置 进程数
    card_ids = [int(card_id) for card_id in arg.card_ids.split(',')]      # 获取可用设备列表

    import multiprocessing as mp

    sub_dirs = split_sub_vid_Up(dirs)      # 划分子列表

    allocate_List = []
    for i, sub_dir in enumerate(sub_dirs):
        arg_branch = copy.deepcopy(arg)
        arg_branch.card_id = card_ids[i % len(card_ids)]            # 分配GPU设备
        # print(f"@Moss: we find and use device-id:{arg_branch.card_id} for free cuda infer")
        allocate_List.append((arg_branch, sub_dir))

    print(f'Processes Num:',process_num_CPU)
    ctx = mp.get_context('spawn')
    with ctx.Pool(processes=process_num_CPU) as pool:
        pool.starmap(process_subdirs, allocate_List)

    return



if __name__ == '__main__':
    pass