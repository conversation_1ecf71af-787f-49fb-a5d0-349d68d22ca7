# -*-coding:utf-8-*-
from GenVidInfo import *
from Gen_Samples import *

import os
from Config.devices_selected import select_device, get_free_gpu_ids



def main(data_cls):
    # ================================1. 先读配置项，读取视频, 保存data_info.pkl =====================================================================
    print(data_cls.videos_pth)
    if not Path(data_cls.get('save_dir')).exists():
        ori_save = data_cls['videos_pth']
        data_cls['save_dir'] = Path(ori_save).with_name(f"{Path(ori_save).name}_sampled").__str__()  # 采样数据存储位置

    # 获取 符合逻辑的关键帧idx和骨骼点
    data_dict = track_situp_videos(data_cls)

    data_cls['vid_info'] = data_dict  # 写入大类
    data_cls_pkl = data_cls

    # 基于关键帧序列 采样生成 样本帧索引id
    proj_info = data_cls_pkl[data_cls_pkl.proj_name]
    vid_info = gen_sitUp_suspects_samples(data_cls_pkl.vid_info, len_sample=proj_info.len_sample)

    # 生成单样本及单标签
    Path(data_cls_pkl.save_dir).mkdir(exist_ok=True, parents=True)  # 不存在则创建 存储路径
    invalid_vids = gen_sitUp_lab_fake_vid(vid_info, data_cls_pkl)

    if not len(invalid_vids) == 0:
        Path(data_cls_pkl.videos_pth + '_invalid_vids').mkdir(exist_ok=True)
        dst = Path(data_cls_pkl.videos_pth + '_invalid_vids')
        for src_pth in invalid_vids:
            dst_pth = str(Path(dst) / Path(src_pth).name)
            os.rename(src_pth, dst_pth)


def multiprocess_main(data_cls):
    import multiprocessing as mp

    sub_dirs = []
    for sub in Path(data_cls.videos_pth).glob('*'):
        if sub.is_dir():
            one_data_cls = copy.deepcopy(data_cls)
            one_data_cls.videos_pth = str(sub)
            sub_dirs.append(one_data_cls)

    core_num_CPU = mp.cpu_count()
    print(core_num_CPU)
    ctx = mp.get_context('spawn')
    with ctx.Pool(processes=core_num_CPU) as pool:
        # pool.starmap(process_subdirs, [(arg, sub_dir) for sub_dir in sub_dirs])
        pool.map(main, sub_dirs)             # map函数会遍历列表sub_dirs中的每个元素，传递给process_subdirs

    return






if __name__ == '__main__':
    data_cls = read_config(f"Config/baseline_sitUp_fake.yml")  # 读取所有配置内容, 如无必要，无需修改主函数
    device = select_device(get_free_gpu_ids(num_gpus=1, selected_device=data_cls.devices), batch_size=128)  # 用1张卡进行推理
    os.environ["CUDA_VISABLE_DEVICES"] = f'{device}'  # 选择空闲的卡
    print(f"@Moss: we find and use device {device} for free cuda infer")

    if data_cls.batch_Mod:
        print("Multi-process Mod:")
        multiprocess_main(data_cls)
    else:
        main(data_cls)
