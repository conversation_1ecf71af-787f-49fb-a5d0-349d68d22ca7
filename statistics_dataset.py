# -*-coding:utf-8-*-
"""
统计数据集分布情况
"""
import argparse
import os
from pathlib import Path

from concurrent.futures import ThreadPoolExecutor


class statistic_classify_data:
    def __init__(self, args):
        self.data_path = Path(args.path)

    def read_structure(self):
        labels_dir = [folder for folder in self.data_path.iterdir() if folder.is_dir()]
        labs_info_reach, labs_info_up = '', ''
        for lab_path in labels_dir:
            labs_info_reach += f'\n\n************************************** New-labels:{lab_path.name} *******************************\n'
            labs_info_up += f'\n\n************************************** New-labels:{lab_path.name} *******************************\n'
            sub_labs = [folder for folder in lab_path.iterdir() if folder.is_dir()]
            for sub in sub_labs:
                labs_info_reach += f'=======================sub-label:{sub.name}==========================================\n'
                labs_info_up += f'=======================sub-label:{sub.name}==========================================\n'
                scl_proj_sitUp = [folder for folder in sub.iterdir() if folder.is_dir() and 'sitUp' in folder.name]
                num_Up_scl, data_num_Up = 0, 0
                for path_search in scl_proj_sitUp:
                    data_num_Up = statistic_classify_data.read_files_multiprocess(path_search)
                    labs_info_up += f"{path_search.name}:\t\t {data_num_Up}\n"
                    print(lab_path.name, sub.name, path_search.name, data_num_Up)

                    num_Up_scl += data_num_Up           # 统计结果
                labs_info_up += f"sum_scl-Up: {num_Up_scl}\n"
                del num_Up_scl, data_num_Up

                num_Reach_scl, data_num_reach = 0, 0
                scl_proj_sitReach = [folder for folder in sub.iterdir() if folder.is_dir() and 'reach' in folder.name]
                for path_search in scl_proj_sitReach:
                    data_num_reach = statistic_classify_data.read_files_multiprocess(path_search)
                    num_Reach_scl += data_num_reach
                    print(lab_path.name, sub.name, path_search.name, data_num_reach)
                    labs_info_reach += f"{path_search.name}:\t\t {data_num_reach}\n"
                labs_info_reach += f"sum_scl-Reach: {num_Reach_scl}\n"
                del num_Reach_scl, data_num_reach


        with open(f'{str(self.data_path)}'+os.sep+'lab_info.txt', 'w') as f:
            f.write(labs_info_reach)
            f.write('\n\n\n\n++++++++++++++++++++++++++++++++++++ Up Project +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n\n\n\n')
            f.write(labs_info_up)



        return

    @staticmethod
    def read_files_multiprocess(path_search:Path):
        # 使用threadPool 并行处理目录
        sub_dirs = [d for d in path_search.iterdir() if d.is_dir()]
        total_files = sum(1 for _ in path_search.glob('*.pkl'))
        if len(sub_dirs):
            with ThreadPoolExecutor() as executor:
                future_res = [executor.submit(statistic_classify_data.count_files, d) for d in sub_dirs]        # 为每个子目录提交一个任务
                total_files += sum(future.result() for future in future_res)

        return total_files

    @staticmethod
    def count_files(dir_path:Path, endswith='.pkl'):
        """统计指定目录下pkl数量"""
        return sum(1 for _ in dir_path.rglob(f'*{endswith}'))



def _argparse():
    # -path:指定读取的训练数据保存路径
    parser = argparse.ArgumentParser(description='统计数据分布的应用程序工具.')
    parser.add_argument('-p', '--path', action='store', default=r'/root/share175/sport_datas/sit_and_reach/classify_cheating/train/skl_points', help='train data path to be censused!')
    parser.add_argument('-c', '--census', action='store', default='SCHOOL,ABCD', help='组合以形成不同的数据:ABCD,MONTH,SCHOOL,TAG')
    parser.add_argument('-t', '--datatype', action='store', default='classify', help='指定统计的数据类型：classify!')
    return parser.parse_args()


if __name__ == '__main__':
    args = _argparse()
    # 测试集
    args.path = f'/root/share175/sport_test/sit_and_reach/classify_cheat/skl_points_tiny'
    # 训练集
    # args.path = f'/root/share175/sport_datas/sit_and_reach/classify_cheating/train/skl_points'

    statistic_classify_data(args).read_structure()
