"""
created by @Moss 20240416
# Step-1
读取mp4视频生成 data_info.pkl 骨骼点,有效状态帧等信息
"""
import copy
import json
import math
import os
import pickle
import re
import shutil
import time
from typing import Union

import matplotlib.pyplot as plt
import torch
import onnxruntime
import numpy as np

import yaml
import cv2
from pathlib import Path
from Config.ConfigDict import ConfigDict

from utils.general import LoadImages, LoadFrames, LoadImages_pullup, scale_coords, saveImageToMp4, CVImageCrop, saveHnadInfoToTxt, saveHnadInfoToPkl
from utils.logics import solidball_logic, standjump_logic, standjump_target_logic,standjump_InAreafoot_logic,standjump_first_fall_logic, \
    situp_target_logic_1, situp_target_logic_2, sitreach_target_logic, Multi_sitreach_target_logic,suspect_target_logic, predel_marks_func,\
    sitReach_status_judge_logic, sitUp_status_judge_logic, \
    Multi_location_50mStart, overlap_area, point_in_polygon, compare_inter_max, IOU_limit,\
    Multi_location_50mEnd, overline_end, calculate_region_r50,\
    getHandsHead, handlerupcountframes, getRatioIndexs, getCropBoxByPkl
from proj_skpt.getSkpt_onnx import pose_v8_infer, pose_v8_detFoot, detect_board_v8_infer, detect_v5_infer, detect_v8_infer
from trackers.byte_tracker import BYTETracker
from proj_skpt.engine.results import Results

from Gen_Samples import gen_run50_lab_fake_img


from utils.plots import colors, plot_one_box, plot_skeleton_kpts
from PIL import Image


# ---------------------------------------以下是基础服务---------------------------------------------------------------
def read_config(config_pth: str) -> ConfigDict:
    """
    # 读取采集数据需要的必要参数
    :param config_pth: Config/baseline.yml
    :return:
    """
    # 读取baseline
    with open(file=config_pth, mode='r', encoding='utf-8') as f:
        data = yaml.safe_load(f)
        data_cls = ConfigDict(data)  # 参数变量转成可调用的方法

    if isinstance(data_cls.videos_pth, str):
        data_cls.videos_pth = re.sub(r'\\+', '/', data_cls.videos_pth)  # 兼容win路径中的\\ 和 \
    # 读取配置文件——标定区域
    if data_cls.proj_name in ['fake_stand_jump', 'soild_ball']:
        with open(file=Path(data_cls.mark_file).absolute(), mode='r', encoding='utf-8') as jf0:
            mark_info = json.load(jf0)
    elif data_cls.proj_name == 'fake_run50':
        mark_info_dict = get_run50_markfile(data_cls)
        mark_info = mark_info_dict
    elif data_cls.proj_name in ['fake_sit_reach', 'pull_up']:  # 坐位体前屈项目
        mark_info = None
    elif hasattr(data_cls, 'box_scale_thres'):  # 仰卧起坐项目
        mark_info = data_cls.box_scale_thres
    else:
        raise ValueError(f"@Moss 还在开发中......莫急")

    data_cls['mark_info'] = mark_info  # 写入data_cls

    return data_cls


def read_files(vid_pth: Union[str, list], Usedvid_lst=[]) -> list:
    """

    :param vid_pth: data_cls.videos_pth 来自config/baseline.yml的 videos_pth
    :param Usedvid_lst: 已经处理过的视频名, default:[]
    :return: [one_vid_pth,...]
    """
    if isinstance(vid_pth, list):
        files = [vid for vid in vid_pth if Path(vid).exists()]  # 适用于多进程直接传入列表
    elif isinstance(vid_pth, str):
        # exist
        assert Path(vid_pth).exists(), f"@Moss: {vid_pth} don't exist!"
        p = Path(vid_pth).absolute()  # os-agnostic absolute path
        if p.is_dir():
            files = [str(file) for file in list(p.rglob('*'))
                     if file.suffix in ['.mp4'] and file.name not in Usedvid_lst]       # , '.avi', '.mp4v'

        elif p.is_file() and str(p).endswith('mp4'):
            files = [str(p)]  # files
        else:
            raise Exception(f'ERROR: No videos found in {str(p)}')
    else:
        raise TypeError(f"{vid_pth.__class__} is not in [str, list]")

    return files


def get_run50_markfile(data_cls):
    """
    50m从项目中 或 视频路径中 获取标定json文件
    终点ip：207
    起点ip: 1个json 或 2个json
    2个json： 视频名[有ip + 无second] 对应唯一ip
    """
    mark_file_dict = {}

    mark_start_pth = Path(data_cls.mark_file).absolute()
    mark_start2_pth = Path(data_cls.mark_file2).absolute()
    mark_end_pth = Path(data_cls.mark_end).absolute()

    if mark_end_pth.exists() and mark_start_pth.exists():
        mark_file_dict['207'] = mark_end_pth
        start_ip1 = re.search(r"(\d+)_", mark_start_pth.name).group(1)
        mark_file_dict[start_ip1] = mark_start_pth
        if mark_start2_pth.exists():
            start_ip2 = re.search(r"(\d+)_", mark_start2_pth.name).group(1)
            mark_file_dict[start_ip2] = mark_start2_pth
    else:
        print(f"@Moss: we get [*_ip_6.json] file from {data_cls.videos_pth}")
        all_json = list(Path(data_cls.videos_pth).glob('*.json'))

        for js_file in all_json:
            if '207' in js_file.name:
                mark_end_pth = js_file
                mark_file_dict['207'] = mark_end_pth
            else:
                start_ip = re.search(r"(\d+)_", js_file.name).group(1)
                mark_file_dict[start_ip] = js_file

    for ip_k, pth_v in mark_file_dict.items():
        with open(file=pth_v, mode='r', encoding='utf-8') as jf:
            mark_info = json.load(jf)
        mark_file_dict[ip_k] = mark_info


    return mark_file_dict


def mark_re_match(vid_name, mark_info, names_lst, name_ip_dict):
    """50m 匹配视频名中的ip与标定mark_info的键值对"""
    if all(x not in vid_name for x in ['.207', '_second']):
        start_ip = re.findall(r"\.(\d+)_", vid_name)[0]
        mark_start = mark_info[start_ip]
    elif '207' in vid_name:
        # 唯一的val即 起点标定, 如果有2个起点，则只能默认取1个204 取不到就取180
        mark_start, = mark_info.values() if len(mark_info)==1 else (mark_info.get('204', mark_info['180']),)
    else:  # 视频无ip 有second
        str_sced = vid_name.split('_second')[0]
        ip_lst = [name_ip_dict[str_] for str_ in names_lst if str_sced in str_]
        ip_sced = ip_lst[0] if len(names_lst) and len(ip_lst) else '204'
        mark_start = mark_info.get(ip_sced, mark_info.get('180', mark_info.get('185')))

    return mark_start


def saveformed(data_dict: dict, data_base: tuple, status: int, skpt: np, img_save: any):
    """
    # 存储状态信息 和可能有的骨骼信息
    :param img_save: 无人/不符合False，1时存储skpt绘制后的图像
    :param skpt: 骨骼关键点
    :param data_dict: 存储视频信息
    :param data_base: 基本信息
    :param status: 数据状态：-1无人，0框内不符合逻辑，1有效数据
    :return:
    """
    if len(data_base) == 5:  # 坐位体前屈专用 board_info
        path, im_shape, frame_id, video_frames, board_info = data_base
    else:
        path, im_shape, frame_id, video_frames = data_base
    if path not in data_dict.keys():
        data_dict[path] = {'img_shape': im_shape, 'video_frames': video_frames, 'frame_id': [frame_id]}
        data_dict[path]['obstruct_frames'] = []  # 新增干扰人员帧,也可以用于 落地脚检测帧
        if len(data_base) == 5:
            data_dict[path]['board_info'] = board_info
        data_dict[path]['status'] = [status]
        data_dict[path]['skpt'] = [skpt]
    else:
        data_dict[path]['frame_id'].append(frame_id)
        data_dict[path]['status'].append(status)
        data_dict[path]['skpt'].append(skpt)

    return



def crop_logic(target, board_xy):
    """单人体前屈 裁剪图像逻辑"""
    # 获取主体臂长
    Pt_pose, box = target[6:], target[:4]

    elbow_len = abs(box[2] - min(Pt_pose[21], Pt_pose[24]))          # 手肘到指尖de 距离
    topLx = box[0] - elbow_len
    topLx = 0 if topLx < 0 else topLx       # 兼容边缘

    board_high = abs(box[3] - board_xy[3])
    topLy = box[1] - board_high
    topLy = 0 if topLy < 0 else topLy

    topRx = board_xy[2]
    topRy = 1080

    return int(topLx), int(topLy), int(topRx), int(topRy)


def crop_logic_Multi(target, board_xy, per_box, pos):
    """多人-体前屈 裁剪图像逻辑
    target: torch.Size([57])
    board_xy: torch.Size([4])
    per_box: list(Tensor([1]), Tensor([1]), Tensor([1]), Tensor([1]));  per_box[-1]: board_topLy + board_H + 0.5 * board_W
    pos: int, 目标所在位置
    """
    # 获取主体臂长
    Pt_pose, box = target[6:], target[:4]

    elbow_len = abs(box[2] - min(Pt_pose[21], Pt_pose[24]))          # 手肘到指尖de 距离
    topLx = box[0] - elbow_len
    topLx = 0 if topLx < 0 else topLx       # 兼容边缘

    board_high = abs(box[3] - board_xy[3])      # 人体下边缘 与 板子下边缘 的差值
    if pos in [5,6]:
        topLy = box[1] - board_high
        topLy = 0 if topLy < 0 else topLy
    elif pos in [1,2, 3,4]:
        topLy = board_xy[1] - 1.5* abs(board_xy[3]-board_xy[1])

    topRx = board_xy[2]
    topRy = per_box[-1] + 0.15*abs(board_xy[2]-board_xy[0])          # (H + 0.5W) + 0.15W = 0.65W

    return int(topLx), int(topLy), int(topRx), int(topRy)


def save_images(cropped_img, im0s, path, scale_val, frame_id, multi=False):


    dir_pth = Path(path).parents[0]
    main_name = Path(path).stem.rsplit('.')[0]
    if multi:
        sample_name = f"{main_name}_Pos{scale_val}_fid{frame_id}.jpg"
    else:
        sample_name = f"{main_name}_val{scale_val}_fid{frame_id}.jpg"
    save_pth_crop = dir_pth / f"{dir_pth.name}_cropped"
    save_pth_ori = dir_pth / f"{dir_pth.name}_oriImgs"
    save_pth_crop.mkdir(exist_ok=True)
    save_pth_ori.mkdir(exist_ok=True)

    # sample_name = re.compile(r'[\u4e00-\u9fa5]+').sub(r'CN', sample_name)

    crop_pth = str(save_pth_crop) + os.sep + sample_name
    ori_pth = str(save_pth_ori) + os.sep + sample_name

    success_crop, crop_im = cv2.imencode('.jpg', cropped_img)
    if success_crop:
        with open(crop_pth, 'wb') as f:
            f.write(crop_im)

    success_ori, ori_im = cv2.imencode('.jpg', im0s)
    if success_ori:
        with open(ori_pth, 'wb') as f:
            f.write(ori_im)
        print('saved_Img.')

    return


def calculate_iou(box1, box2):
    """from avery 20250109"""
    # 解析边界框
    # 假设边界框的格式为 [x_min, y_min, x_max, y_max]
    x1_min, y1_min, x1_max, y1_max = box1
    x2_min, y2_min, x2_max, y2_max = box2
    # 计算交集的坐标
    inter_min_x = max(x1_min, x2_min)
    inter_min_y = max(y1_min, y2_min)
    inter_max_x = min(x1_max, x2_max)
    inter_max_y = min(y1_max, y2_max)
    # 计算交集的宽度和高度
    inter_width = max(0, inter_max_x - inter_min_x)
    inter_height = max(0, inter_max_y - inter_min_y)
    # 计算交集的面积
    inter_area = inter_width * inter_height
    # 计算每个边界框的面积
    box1_area = (x1_max - x1_min) * (y1_max - y1_min)
    box2_area = (x2_max - x2_min) * (y2_max - y2_min)
    # 计算并集的面积
    union_area = box1_area + box2_area - inter_area
    # 计算IoU
    iou = inter_area / float(union_area) if union_area != 0 else 0.0
    return iou


def shutMove_fileRun50(path, file_name, type='_', save_dir=''):
    err_dir = Path(save_dir + type)
    err_dir.mkdir(exist_ok=True)
    shutil.move(path, err_dir / file_name)  # move vid

    return


def extract_det_pos(trackPose_lst, trackDet_lst, track_pos, track_imgs, limit=40, Pe=list, S1S2=list, M1M2=list):
    """
    根据指定跑道的跟踪id： track_pos
    将历史的跟踪目标：trackPose_lst
    中对应跟踪id的人体姿态提取出来
    Pe: 对应跑道的middle标定
    S1S2M1M2: 起点处的标定、中间的标定
    要求超过40帧; 要求跟踪首帧在标定Middle线之外

    跟踪到的图像 track_imgs
    """
    res = list()
    trac = 0
    t_k = -1
    for k, (pos, det, im0s) in enumerate(zip(trackPose_lst, trackDet_lst, track_imgs)):
        if det is None:
            res.append(None)
            continue
        # 获取跟踪id列
        track_ids = det[:, -4]          # 跟踪id是track的第5列
        mask = track_ids == track_pos       # 查找对应跟踪id索引
        indices = torch.nonzero(torch.tensor(mask), as_tuple=True)[0]
        if indices.numel() <= 0:
            res.append(None)
            continue

        # 提取对应的行
        select_pos = pos[indices]
        select_det = det[indices]
        if trac == 0:
            t_k = k         # 记录首帧位置
            s1s2_m2m1 = calculate_region_r50(s1s2=S1S2, m1m2=M1M2, p0=select_det)
            res.append([select_det, select_pos, im0s, s1s2_m2m1])      # 存储主要信息
        else:
            res.append([select_det, select_pos])  # 存储主要信息

        trac += 1

    if trac > limit:        # 条件1： 有效跟踪>40f
        res_0_det = next((x[0] for x in res if x is not None), None)
        if res_0_det[3] < Pe[1]:        # 条件2： 默认跟踪到的第1个有效帧的人体，在middle标定线之外
            return res, t_k

    return False, -1


# ------------------------------ 以下是算法服务 ------------------------------------------------------------------------
def track_pullup_videos(data_cls: ConfigDict) ->dict:
    """
    引体向上 防作弊项目
    # :param model_info: 姿态模型信息 {pth:模型路径, imgsz:推理时使用的输入图像尺寸}
    # :param mark_info: 标定信息 such {'vid_width': 1920, 'ReadyPt1_x': 左上, 'ReadyPt2_x': 右下}
    # :param vid_pth: 视频数据路径 --来自配置文件Config/baseline.yml
    :return:
    """
    up, down = False, False
    top_line  = 0 # 水平线
    head_top_line = 0 # 头过杆基线
    bottom_line = 0  # 头部最低点
    up_frameIds = {}   # 保存id
    up_frames =[]      #保存上升图像
    videos_clip_out =data_cls.videos_clip_out
    vid_width = data_cls.vid_ori_width
    vid_crop_width = data_cls.vid_crop_width
    vid_pth, model_info, mark_info = data_cls.videos_pth, data_cls.model_info, data_cls.mark_info
    model_info = data_cls.model_info

    files = read_files(vid_pth)         # 数据列表

    dataset = LoadImages_pullup(files, img_size=model_info['infer_size'], vid_width=vid_width)
    # 循环 视频-> frames

    data_dict, way, board_width, board_info, xyxy_board = {}, None, int, {}, None          # 用于存储有效数据信息
    session_hand_head = onnxruntime.InferenceSession(model_info['yolo_detect'], providers=['CUDAExecutionProvider'])

    for path, img, im0s, vid_id, frame_id in dataset:
        print(f'video {dataset.count + 1}/{dataset.nf} ({dataset.frame}/{dataset.video_frames}) {Path(path).name}: ', end=' ')
        # model_inference
        image_crop = CVImageCrop(im0s,vid_crop_width,vid_crop_width)
        # dets_list = detect_v5_infer(session_hand_head, img_ori=image_crop, img_size=model_info['infer_size'])
        dets_list = detect_v8_infer(session_hand_head, img_ori=image_crop, img_size=model_info['infer_size'])
        if dets_list is None or len(dets_list[0])==0 or frame_id==1:
            up, down = False, False
            top_line = 0  # 水平线
            head_top_line = 0  # 头过杆基线
            bottom_line = 0  # 头部最低点
            up_frameIds = {}  # 保存id
            up_frames = []  # 保存上升图像
            print("Not be detected hand or head")
            continue
        print("This Frame Find Det Obj")
        dets = dets_list[0]
        hand_or_backhand = torch.logical_or(dets[:, -1] == data_cls.pullup_detcls["hand"], dets[:, -1] == data_cls.pullup_detcls["backhand"])
        # hands = dets[dets[:, -1] == data_cls.pullup_detcls["hand"]]       # 仅支持正手
        hands = dets[hand_or_backhand]            # 手：0正手 1反手
        heads = dets[dets[:,-1]==data_cls.pullup_detcls["head"]]           # 头:2
        if len(hands) != 2 or len(heads) < 1:  # 有且只有2个正手握
            up, down = False, False
            top_line = 0  # 水平线
            head_top_line = 0  # 头过杆基线
            bottom_line = 0  # 头部最低点
            up_frameIds = {}  # 保存id
            up_frames = []  # 保存上升图像
            continue
        left_hand,right_hand,head = getHandsHead(hands,heads)
        if len(head) >0:
            if top_line==0:
                top_line = (left_hand[3]+left_hand[1]+right_hand[3]+right_hand[1])/4
            if head_top_line ==0:
                head_top_line= top_line - (head[3]-head[1])/3*1
            if bottom_line == 0:
                bottom_line = top_line+(head[1]-top_line)/5*3
            print("Right Pullup action")

            up,down = handlerupcountframes(top_line,head_top_line,bottom_line,frame_id,up,down,head)
            if not up and not down:
                print("Up doing......", frame_id)
                up_frameIds.update({frame_id:{'left_hand':left_hand,'right_hand':right_hand,'head':head}})
                up_frames.append(image_crop)
            elif up and not down:
                if len(up_frameIds) > 4:
                    # print("============",up_frameIds)
                    saveImageToMp4(path, videos_clip_out,list(up_frameIds.keys())[0], len(up_frameIds), up_frames,vid_crop_width,vid_crop_width)
                    # saveHnadInfoToTxt(path, videos_clip_out,up_frameIds[0], len(up_frameIds),left_hand,right_hand)
                    saveHnadInfoToPkl(path, videos_clip_out,up_frameIds)
                up_frameIds = {}
                up_frames =[]
                print("Over doing。。。。",frame_id)
            elif not up and down:
                if len(up_frameIds)>4:
                    print("============",up_frameIds)
                    saveImageToMp4(path,videos_clip_out, list(up_frameIds.keys())[0], len(up_frameIds), up_frames)
                    #saveHnadInfoToTxt(path, videos_clip_out, up_frameIds[0], len(up_frameIds), left_hand, right_hand)
                    saveHnadInfoToPkl(path, videos_clip_out, up_frameIds)
                up_frameIds = {}
                up_frames =[]
                print("down doing。。。。", frame_id)



    return data_dict


def track_pullup_videos_toframes(data_cls: ConfigDict) -> dict:
    """
    引体向上 防作弊项目
    # :param model_info: 姿态模型信息 {pth:模型路径, imgsz:推理时使用的输入图像尺寸}
    # :param mark_info: 标定信息 such {'vid_width': 1920, 'ReadyPt1_x': 左上, 'ReadyPt2_x': 右下}
    # :param vid_pth: 视频数据路径 --来自配置文件Config/baseline.yml
    :return:
    """
    videos_clip_out =data_cls.videos_clip_out
    vid_width = data_cls.vid_crop_width
    vid_crop_height_precent = data_cls.vid_crop_height_precent #裁剪的高度缩放比例，底部过短，就是1
    vid_pth, model_info, mark_info = data_cls.videos_pth, data_cls.model_info, data_cls.mark_info
    model_info = data_cls.model_info
    if videos_clip_out is not None:
        files = read_files(vid_pth+"/"+videos_clip_out)         # 数据列表
    else:
        files =read_files(vid_pth)

    dataset = LoadImages_pullup(files, img_size=model_info['infer_size'], vid_width=vid_width)
    # 循环 视频-> frames
    abs_path ,txt_path =None,None
    top_point, bottom_point = None,None
    for path, img, im0s, vid_id, frame_id in dataset:
        print(f'video {dataset.count + 1}/{dataset.nf} ({dataset.frame}/{dataset.video_frames}) {Path(path).name}: ', end=' ')
        print("\n")
        # model_inference
        op_status =False
        frm_idexes =[]
        current_path = (Path(path).parent/ Path(path).stem).__str__()
        fileName = Path(path).stem
        frame_num = int(fileName.rsplit("_", 1)[-1])
        pkl_path = current_path + ".pkl"
        if frame_num >16:
          frm_idexes = getRatioIndexs(pkl_path)
        else:
            op_status = True
        if frame_id in frm_idexes:
            op_status =True
            frame_id = frm_idexes.index(frame_id)+1
        if op_status:
            if abs_path is None or abs_path!=current_path:
                abs_path =current_path
               # top_point,bottom_point =getCropBox(txt_path,1440)
                top_point,bottom_point =getCropBoxByPkl(pkl_path, vid_width, vid_crop_height_precent)

            if not os.path.exists(abs_path):
                os.mkdir(abs_path)
            croped_image = im0s[top_point[1]:bottom_point[1],top_point[0]:bottom_point[0]]
            cv2.imwrite(abs_path+"/"+str(frame_id)+".jpg",croped_image)


def param_videos(vid_pth, model_info=None, mark_info=None) -> dict:
    """
    :param model_info: 姿态模型信息 {pth:模型路径, imgsz:推理时使用的输入图像尺寸}
    :param mark_info: 标定信息 such {'vid_width': 1920, 'ReadyPt1_x': 左上, 'ReadyPt2_x': 右下}
    :param vid_pth: 视频数据路径 --来自配置文件Config/baseline.yml
    :return:
    """

    files = read_files(vid_pth)  # 数据列表

    dataset = LoadImages(files, img_size=model_info['infer_size'], vid_width=mark_info.get('vid_width'))
    # 循环 视频-> frames

    data_dict = {}  # 用于存储有效数据信息
    session = onnxruntime.InferenceSession(model_info['yolo_pose'], providers=['CUDAExecutionProvider'])
    # assert len(session.get_providers()) == 2, f"@Moss: {session.get_providers()} may use CPU Infer,please check your ENV"
    for path, img, im0s, vid_id, frame_id in dataset:
        print(f'video {dataset.count + 1}/{dataset.nf} ({dataset.frame}/{dataset.video_frames}) {Path(path).name}: ',
              end=' ')
        # model_inference
        preds = pose_v8_infer(session, img=img)

        data_base = (path, im0s.shape, frame_id, dataset.video_frames)
        for i, det in enumerate(preds):  # detections per image
            if len(det) == 0:  # 'No person in Img'
                saveformed(data_dict, data_base, status=-1, skpt=None, img_save=False)  # -1: 无人状态, 无骨骼数据
                continue

            # Rescale boxes from img_size to im0 size
            det[:, :4] = scale_coords(img.shape[2:], det[:, :4], im0s.shape, kpt_label=False)
            det[:, 6:] = scale_coords(img.shape[2:], det[:, 6:], im0s.shape, kpt_label=True, step=3)

            _index = solidball_logic(mark_info, det=det)  # 实心球逻辑区域
            if _index is False:
                saveformed(data_dict, data_base, status=0, skpt=None, img_save=False)  # 0: 有人但不符合逻辑, 无骨骼数据
                continue
            else:  # 存储有效数据
                saveformed(data_dict, data_base, status=1, skpt=det[_index],
                           img_save=im0s)  # 1: 有人且符合逻辑, 存储骨骼数据 和 img信息
                break

    return data_dict


def param_standjump_videos(vid_pth, model_info=None, mark_info=None) -> dict:
    """
    :param model_info: 姿态模型信息 {pth:模型路径, imgsz:推理时使用的输入图像尺寸}
    :param mark_info: 标定信息 such {'vid_width': 1920, 'ReadyPt1_x': 左上, 'ReadyPt2_x': 右下}
    :param vid_pth: 视频数据路径 --来自配置文件Config/baseline.yml
    :return:
    """

    files = read_files(vid_pth)  # 数据列表

    dataset = LoadImages(files, img_size=model_info['infer_size'], vid_width=mark_info.get('vid_width'))
    # 循环 视频-> frames

    data_dict = {}  # 用于存储有效数据信息
    session = onnxruntime.InferenceSession(model_info['yolo_pose'], providers=['CUDAExecutionProvider'])
    assert len(
        session.get_providers()) == 2, f"@Moss: {session.get_providers()} may use CPU Infer,please check your ENV"
    for path, img, im0s, vid_id, frame_id in dataset:
        print(f'video {dataset.count + 1}/{dataset.nf} ({dataset.frame}/{dataset.video_frames}) {Path(path).name}: ',
              end=' ')
        # model_inference
        preds = pose_v8_infer(session, img=img)

        data_base = (path, im0s.shape, frame_id, dataset.video_frames)
        for i, det in enumerate(preds):  # detections per image
            if len(det) == 0:  # 'No person in Img'
                saveformed(data_dict, data_base, status=-1, skpt=None, img_save=False)  # -1: 无人状态, 无骨骼数据
                continue

            # Rescale boxes from img_size to im0 size
            det[:, :4] = scale_coords(img.shape[2:], det[:, :4], im0s.shape, kpt_label=False)
            det[:, 6:] = scale_coords(img.shape[2:], det[:, 6:], im0s.shape, kpt_label=True, step=3)

            _index = standjump_logic(mark_info, det=det)
            if _index is False:
                saveformed(data_dict, data_base, status=0, skpt=None, img_save=False)  # 0: 有人但不符合逻辑, 无骨骼数据
                continue
            else:  # 存储有效数据
                saveformed(data_dict, data_base, status=1, skpt=det[_index],
                           img_save=im0s)  # 1: 有人且符合逻辑, 存储骨骼数据 和 img信息
                break

    return data_dict


def track_standjump_videos(vid_pth, model_info=None, mark_info=None) -> dict:
    """
    use byteTrack 追踪算法
    :param model_info: 姿态模型信息 {pth:模型路径, imgsz:推理时使用的输入图像尺寸}
    :param mark_info: 标定信息 such {'vid_width': 1920, 'ReadyPt1_x': 左上, 'ReadyPt2_x': 右下}
    :param vid_pth: 视频数据路径 --来自配置文件Config/baseline.yml
    :return:
    """

    # 循环 视频-> frames
    files = read_files(vid_pth)  # 数据列表
    dataset = LoadImages(files, img_size=model_info['infer_size'], vid_width=mark_info.get('vid_width'))

    data_dict, use_id, vid_idx = {}, None, 0  # 用于存储有效数据信息, use_id 为跟踪目标id, vid_idx为第1个视频
    session = onnxruntime.InferenceSession(model_info['yolo_pose'], providers=['CUDAExecutionProvider'])
    # assert len(session.get_providers()) == 2, f"@Moss: {session.get_providers()} may use CPU Infer,please check your ENV"
    for path, img, im0s, vid_id, frame_id in dataset:
        print(f'video {vid_id + 1}/{dataset.nf} ({dataset.frame}/{dataset.video_frames}) {Path(path).name}: ', end=' ')
        if frame_id == 1:
            tracker = BYTETracker(frame_rate=25)  # 初始化追踪器
            rec_t = True  # 在每开始循环1个新视频，定位标记要清零

        # model_inference
        preds = pose_v8_infer(session, img=img)

        data_base = (path, im0s.shape, frame_id, dataset.video_frames)
        for i, det in enumerate(preds):  # detections per image
            im0 = im0s.copy()
            if len(det) == 0:  # 'No person in Img'
                saveformed(data_dict, data_base, status=-1, skpt=None, img_save=False)  # -1: 无人状态, 无骨骼数据
                continue
            # 目标跟踪
            results = []
            det[:, :4] = scale_coords(img.shape[2:], det[:, :4], im0.shape, kpt_label=False).round()
            det[:, 6:] = scale_coords(img.shape[2:], det[:, 6:], im0.shape, kpt_label=True, step=3).round()
            pred_kpts = det[:, 6:].view(len(det), *[17, 3])  # ref_skpt = det[:, 6:].clone()
            img_path = None
            results.append(Results(im0s, path=img_path, names={0: 'person'}, boxes=det[:, :6], keypoints=pred_kpts))
            det_track = results[i].boxes.cpu().numpy()

            # print(det_track.xyxy.astype(int))
            tracks = tracker.update(det_track, im0s)  # 更新跟踪器
            # print(tracks.astype(int))

            if len(tracks) == 0:
                saveformed(data_dict, data_base, status=-2, skpt=None, img_save=False)  # -2: 跟踪丢失
                continue

            idx = tracks[:, -1].astype(int)
            results[i] = results[i][idx]
            results[i].update(boxes=torch.as_tensor(tracks[:, :-1]))  # results[i].boxes.boxes

            # 确定跟踪 主体目标
            init_idx = standjump_target_logic(mark_info, det_track=results[i].boxes.xyxy)  # 确定标定框内的跟踪目标所在的row
            track_id = results[i].boxes.id[init_idx]
            if init_idx is not False:  # 框内有人， 但不一定是目标
                if rec_t:
                    use_id = track_id  # 存储该跟踪id 仅第1次储存的为目标,
                rec_t = False
            if (use_id is not None) and (use_id in results[i].boxes.id):  # if 跟踪目标已确定，且存在画面中
                # 判断跟踪目标是否在框内，此时只能传1个跟踪目标，防止框内有其他人
                use_idx = int(torch.where(use_id == results[i].boxes.id)[0])
                target_in_box = standjump_target_logic(mark_info, det_track=results[i].boxes.xyxy[use_idx].unsqueeze(0))  # results[i].boxes.xyxy
            else:
                # 跟踪目标已确定，但跟踪丢失
                saveformed(data_dict, data_base, status=-2, skpt=None, img_save=im0s)
                continue

            # print(row_idx)
            track_one = results[i].boxes.data[use_idx][:-1]  # 目标人体框
            det_skpt = results[i].keypoints.data[use_idx].view(-1)  # 目标人体姿态
            det_ = torch.cat((track_one, det_skpt), dim=0)

            # 有效数据status: 0: 跟踪主体在 准备区域;  1: 跟踪主体 起跳/离开；
            saveformed(data_dict, data_base, status=1 if target_in_box is False else 0, skpt=det_, img_save=im0s)

            print(results[i].boxes.id, f"track-id{use_id}")  # list(idx).index(0)
            # plot_one_box(det_, im0s, label=f'person id {use_id}', color=colors(1, True), line_thickness=2, kpt_label=False)
            # plot_skeleton_kpts(im0s, kpts=det_skpt, steps=3, orig_shape=im0s.shape[:2], kpt_num=17)  # 绘制姿态点
            # image = Image.fromarray(im0s)
            # image.save(f"out_{frame_id}.png")

    return data_dict


def track_standjump_logic_videos(vid_pth, model_info=None, mark_info=None) -> dict:
    """
    use byteTrack 追踪算法
    # 增加逻辑判断立定跳远垫子内的首次落地点，@Moss 20241125        TODO： logic
    :param model_info: 姿态模型信息 {pth:模型路径, imgsz:推理时使用的输入图像尺寸}
    :param mark_info: 标定信息 such {'vid_width': 1920, 'ReadyPt1_x': 左上, 'ReadyPt2_x': 右下}
    :param vid_pth: 视频数据路径 --来自配置文件Config/baseline.yml
    :return:
    """

    # 循环 视频-> frames
    files = read_files(vid_pth)  # 数据列表
    dataset = LoadImages(files, img_size=model_info['infer_size'], vid_width=mark_info.get('vid_width'))
    # dataset = LoadImages(files, img_size=model_info['infer_size'], vid_width=mark_info.get('vid_width'), mirror=mark_info.get('mirror'))

    data_dict, use_id, vid_idx = {}, None, 0  # 用于存储有效数据信息, use_id 为跟踪目标id, vid_idx为第1个视频

    use_trt = model_info.get('trt_infer', False)
    if use_trt:
        model_path = model_info['yolo_pose']
        p, f = os.path.split(model_path)  # 增加Engine前缀
        engine_path = os.path.join(p, 'Engine_' + f)
        engine_file = Path(engine_path).with_suffix('.engine')
        print('Trt: ', engine_file)
        if not engine_file.exists():
            print('Onnx model to engine. Please Wait')
            from tensorRT.trt_inference import build_engine
            engine = build_engine(model_path, True)
            if engine:
                print('Engine successfully created!')
        else:
            print('Engine model exist. and Load it.')
            from tensorRT.trt_inference import PoseTensorRTInference
            session = PoseTensorRTInference(engine_file, conf=0.5, iou=0.4)
    else:
        session = onnxruntime.InferenceSession(model_info['yolo_pose'], providers=['CUDAExecutionProvider'])

    session_detfoot = onnxruntime.InferenceSession(model_info['det_foot'], providers=['CUDAExecutionProvider'])
    up_down_ptList = predel_marks_func(mark_info)  # 标定信息扩展
    # assert len(session.get_providers()) == 2, f"@Moss: {session.get_providers()} may use CPU Infer,please check your ENV"
    for path, img, im0s, vid_id, frame_id, oriVid_shape in dataset:
        print(f'video {vid_id + 1}/{dataset.nf} ({dataset.frame}/{dataset.video_frames - 1}) {Path(path).name}: ', end=' ')
        if im0s is None:
            continue

        if frame_id == 1:
            # Jump_LtoR = mark_info.get('mirror') == 'True'           # 从左边跳
            tracker = BYTETracker(frame_rate=25)  # 初始化追踪器
            rec_t = True                    # 在每开始循环1个新视频，定位标记要清零
            near_foot_a = False             # 初始化第1帧在框内的foot


        # t1 = time.time()
        preds = pose_v8_infer(session, img=img)              # model_inference
        # t2 = time.time()
        # print(t2-t1)

        data_base = (path, im0s.shape, frame_id, dataset.video_frames)
        for i, det in enumerate(preds):  # detections per image
            im0 = im0s.copy()
            if len(det) == 0:  # 'No person in Img'
                saveformed(data_dict, data_base, status=-1, skpt=None, img_save=False)  # -1: 无人状态, 无骨骼数据
                continue
            # 目标跟踪
            results = []
            det[:, :4] = scale_coords(img.shape[2:], det[:, :4], im0.shape, kpt_label=False).round()
            det[:, 6:] = scale_coords(img.shape[2:], det[:, 6:], im0.shape, kpt_label=True, step=3).round()
            pred_kpts = det[:, 6:].view(len(det), *[17, 3])  # ref_skpt = det[:, 6:].clone()
            img_path = None
            results.append(Results(im0s, path=img_path, names={0: 'person'}, boxes=det[:, :6], keypoints=pred_kpts))
            det_track = results[i].boxes.cpu().numpy()

            # print(det_track.xyxy.astype(int))
            tracks = tracker.update(det_track, im0s)  # 更新跟踪器
            # print(tracks.astype(int))

            if len(tracks) == 0:
                saveformed(data_dict, data_base, status=-2, skpt=None, img_save=False)  # -2: 跟踪丢失
                continue

            idx = tracks[:, -1].astype(int)
            results[i] = results[i][idx]
            results[i].update(boxes=torch.as_tensor(tracks[:, :-1]))  # results[i].boxes.boxes

            # 确定跟踪 主体目标
            init_idx = standjump_target_logic(mark_info, det_track=results[i].boxes.xyxy)           # 确定标定框内的跟踪目标所在的row
            track_id = results[i].boxes.id[init_idx]
            if init_idx is not False:               # 框内有人， 但不一定是目标
                if rec_t:
                    use_id = track_id               # 存储该跟踪id 仅第1次储存的为目标,
                rec_t = False
            if (use_id is not None) and (use_id in results[i].boxes.id):  # if 跟踪目标已确定，且存在画面中
                # 判断 跟踪目标是否在框内，此时只能传1个跟踪目标，防止框内有其他人
                use_idx = int(torch.where(use_id == results[i].boxes.id)[0])
                target_in_box = standjump_target_logic(mark_info, det_track=results[i].boxes.xyxy[use_idx].unsqueeze(0))  # results[i].boxes.xyxy
            else:
                # 跟踪目标已确定，但跟踪丢失
                saveformed(data_dict, data_base, status=-2, skpt=None, img_save=im0s)
                continue

            # print(row_idx)
            track_one = results[i].boxes.data[use_idx][:-1]             # 目标人体框
            det_skpt = results[i].keypoints.data[use_idx].view(-1)  # 目标人体姿态
            det_ = torch.cat((track_one, det_skpt), dim=0)

            # if frame_id == 94 or frame_id == 140:
            #     cv2.rectangle(im0s, (up_down_ptList[6][0], up_down_ptList[6][1]), (up_down_ptList[2], up_down_ptList[3]), color=(248, 248, 255), thickness=2 * 1 // 3, lineType=cv2.LINE_AA)  # 绘制 配置box
            #     cv2.rectangle(im0s, (int(pred_foots[0][0]), int(pred_foots[0][1])),
            #                   (int(pred_foots[0][2]), int(pred_foots[0][3])), color=(248, 248, 255), thickness=2 * 1 // 3,
            #                   lineType=cv2.LINE_AA)  # 绘制 配置box
            #     image = Image.fromarray(im0s)
            #     image.save(f"out_{frame_id}.png")
            
            if target_in_box is False:

                # 逐帧检测脚，判断脚是否在跳跃区域内
                pred_foots = pose_v8_detFoot(session_detfoot, img0s=im0s, imgsz=model_info['foot_size'])
                near_foot_b = standjump_InAreafoot_logic(mark_info, pred_foots,  up_down_ptList)  # 在区域内 且 离camera最近的foot
                print(near_foot_b)
                # 判断与前1个foot的x，y位移
                if near_foot_a is False:
                    near_foot_a = near_foot_b
                elif near_foot_b is not False:       # 前后帧都存在，且在框内
                    falled = standjump_first_fall_logic(near_foot_a, near_foot_b)
                    if not falled:
                        near_foot_a = near_foot_b
                    elif not data_dict[path].get('falled_frame'):       # 保存 第1次 落地帧
                        mean_footX = (near_foot_b[0] + near_foot_b[2]) * 0.5
                        pix_len_scales = abs(up_down_ptList[0] - up_down_ptList[-2][0])         # 跳远垫子像素长度
                        len_50cm = pix_len_scales / 6              # 大约50cm的阈值
                        if abs(mean_footX - det_[6:][45]) < len_50cm or abs(mean_footX - det_[6:][45]) < len_50cm:
                            data_dict[path]['obstruct_frames'] += [near_foot_b]         # 存储 落地帧
                            score_pix = abs((up_down_ptList[0] + up_down_ptList[2]) / 2 - (near_foot_b[0] + near_foot_b[2]) / 2)
                            if score_pix <= 0.33 * pix_len_scales:
                                falled_stauts = 4       # 4 表示 落地帧 跳的很远2m及以上
                            elif pix_len_scales / 3 < score_pix <= pix_len_scales / 1.5:
                                falled_stauts = 3       # 3 表示 落地帧 正常落地区间
                            else:
                                falled_stauts = 2       # 跳的很近，1.2m以内
                            saveformed(data_dict, data_base, status=falled_stauts, skpt=det_, img_save=im0s)  # 2: 真落地 跟踪主体脚踝点 与 落地脚 x方向在1m内 且在落地区域；
                            print(f"falled foot {near_foot_b[:4]}")
                            near_foot_a = near_foot_b
                            continue
                        else:
                            near_foot_a = near_foot_b

            # 有效数据status: 0: 跟踪主体在 准备区域;  1: 跟踪主体 起跳/离开；
            saveformed(data_dict, data_base, status=1 if target_in_box is False else 0, skpt=det_, img_save=im0s)
            print(results[i].boxes.id.tolist(), f"track-id:{int(use_id)}")  # list(idx).index(0)




            # plot_one_box(det_, im0s, label=f'person id {use_id}', color=colors(1, True), line_thickness=2, kpt_label=False)
            # plot_skeleton_kpts(im0s, kpts=det_skpt, steps=3, orig_shape=im0s.shape[:2], kpt_num=17)  # 绘制姿态点
            # image = Image.fromarray(im0s)
            # image.save(f"out_{frame_id}.png")

    return data_dict



def track_situp_videos_logic(data_cls: ConfigDict) -> dict:
    """
    仰卧起坐 防作弊项目
    # @Moss 加入逻辑 过滤盲区蹲下的人体      20241018 14:30 TODO
    :param data_cls 配置信息汇总 字典类
    model_info: 姿态模型信息 {pth:模型路径, imgsz:推理时使用的输入图像尺寸}
    mark_info: 标定信息 such {'vid_width': 1920, 'ReadyPt1_x': 左上, 'ReadyPt2_x': 右下}
    vid_pth: 视频数据路径 --来自配置文件Config/baseline.yml
    :return:
    """
    session = data_cls.get('session', None)
    num_bodys = data_cls.fake_sit_up.num_bodys
    vid_pth, model_info, mark_info = data_cls.videos_pth, data_cls.model_info, data_cls.mark_info

    files = read_files(vid_pth)  # 数据列表

    dataset = LoadImages(files, img_size=model_info['infer_size'], vid_width=1920)
    # 循环 视频-> frames

    data_dict, xyxy_first = {}, False
    # saved_img, obstruct_frames = True,
    if not session:
        session = onnxruntime.InferenceSession(model_info['yolo_pose'], providers=['CUDAExecutionProvider'])
        # assert len(session.get_providers()) == 2, f"@Moss: {session.get_providers()} may use CPU Infer,please check your ENV"
    for path, img, im0s, vid_id, frame_id, _ in dataset:
        print(f'video {dataset.count + 1}/{dataset.nf} ({dataset.frame}/{dataset.video_frames}) {Path(path).name}: ',
              end=' ')
        if frame_id == 1:
            xyxy_first = False  # 用于存储有效数据信息, 存储首个有效帧的人体框坐标

        # model_inference
        preds = pose_v8_infer(session, img=img)  # 默认v8

        data_base = (path, im0s.shape, frame_id, dataset.video_frames)

        for i, det in enumerate(preds):  # detections per image
            if len(det) == 0:  # 'No person in Img'
                saveformed(data_dict, data_base, status=-1, skpt=None, img_save=False)  # -1: 无人状态, 无骨骼数据
                continue

            # Rescale boxes from img_size to im0 size
            det[:, :4] = scale_coords(img.shape[2:], det[:, :4], im0s.shape, kpt_label=False)
            det[:, 6:] = scale_coords(img.shape[2:], det[:, 6:], im0s.shape, kpt_label=True, step=3)

            # 根据第1帧：获取目标框的索引，确定目标主体的人体框坐标
            if not xyxy_first:
                xyxy_first = situp_target_logic_1(mark_info, det=det)
                _index = torch.where(torch.all(torch.eq(det[:, :4], torch.stack(xyxy_first)), dim=1))[0].item() if xyxy_first else False
            else:
                # 判断每帧的人体框，是否符合首帧目标框de交集面积，从而找到该人体idx
                _index = situp_target_logic_2(xyxy_first, det=det)
            if _index is False:
                saveformed(data_dict, data_base, status=0, skpt=None, img_save=False)  # 0: 有人但不符合逻辑, 无骨骼数据
                continue

            # 存储有效数据: 获取目标周围的 作弊嫌疑人, top_suspects[0]是目标人体
            top_suspects, way = suspect_target_logic(num_bodys=num_bodys, index=_index, det=det)
            suspects = [det[_index, :]]
            for suspect_idx in top_suspects[1:]:
                if len(suspect_idx):
                    suspects.append(det[suspect_idx[0], :])
                else:
                    suspects.append(None)
            # 记录角度：判断目标T的角度[肩-膝-脚], 是否符合95°和115°的阈值，确定躺平和促膝状态
            angle_val = sitUp_status_judge_logic(way, det_index=det[_index, 6:])
            saveformed(data_dict, data_base, status=angle_val, skpt=suspects, img_save=im0s)  # 1: 有人且符合逻辑, 存储骨骼数据 和 img信息
            print(f"angle-{angle_val}")

            # if angle_val == 115:
            #     main_box = det[_index,:4]
            #     main_skpt = det[_index, 6:]
            #     box_left, waist_centre = (main_box[0], main_skpt[[10,13]].mean()-abs(main_skpt[19]-main_skpt[16])), \
            #                              (main_skpt[[33,36]].mean()+0.5*abs(main_skpt[42]-main_skpt[36]), main_skpt[[34,37]].mean())         # 框右下角取 腰点中心
            #     cv2.rectangle(im0s, (int(box_left[0]), int(box_left[1])), (int(waist_centre[0]), int(waist_centre[1])),
            #                   color=(0, 0, 255), thickness=5, lineType=cv2.LINE_AA)  # 绘制 配置box
            #     for _index in range(len(det)):
            #         plot_one_box(det[_index, :4].squeeze(), im0s, label=f'person {det[_index, 4]}',
            #                          color=colors(int(det[_index, 5]), True), line_thickness=2, kpt_label=False)
            #         plot_skeleton_kpts(im0s, kpts=det[_index, 6:].squeeze(), steps=3, orig_shape=im0s.shape[:2], kpt_num=17)  # 绘制姿态点
            #
            #     image1 = Image.fromarray(im0s, 'RGB')
            #     image1.save(f'test_demo/{Path(path).stem}_{angle_val}_{frame_id}.png')


            # 过滤 盲区逻辑
            if angle_val >= 125:
                main_box = det[_index,:4]
                main_skpt = det[_index, 6:]             # 主体的骨骼点
                # 框的左上角是主体人体框x， y取 耳点中点+肩点y差值
                box_left, waist_centre = (main_box[0], main_skpt[[10,13]].mean()-abs(main_skpt[19]-main_skpt[16])), \
                                         (main_skpt[[33,36]].mean()+0.5*abs(main_skpt[42]-main_skpt[36]), main_skpt[[34,37]].mean())         # 框右下角取 腰点中心
                for idx in range(len(det)):
                    if idx == _index:
                        continue
                    # 判断point点(腰或膝) 是否在定义的box内
                    waist_l, waist_r = is_point_inbox(point=det[idx, 6:][[33,34]], left_top=box_left, right_down=waist_centre), \
                                       is_point_inbox(point=det[idx, 6:][[36,37]], left_top=box_left, right_down=waist_centre)        # 腰
                    knee_l, knee_r = is_point_inbox(point=det[idx, 6:][[39,40]], left_top=box_left, right_down=waist_centre), \
                                       is_point_inbox(point=det[idx, 6:][[42,43]], left_top=box_left, right_down=waist_centre)        # 膝
                    if waist_l or waist_r:
                        if knee_l or knee_r:
                            data_dict[path]['obstruct_frames'].append(frame_id)

                # 测试
                if angle_val == 838:
                    cv2.rectangle(im0s, (int(box_left[0]), int(box_left[1])),(int(waist_centre[0]), int(waist_centre[1])),
                                  color=(0, 0, 255), thickness=5, lineType=cv2.LINE_AA)  # 绘制 配置box
                    for idx in range(len(det)):
                        plot_one_box(det[idx, :4].squeeze(), im0s, label=f'person {det[idx, 4]}',
                                     color=colors(int(det[idx, 5]), True), line_thickness=2, kpt_label=False)
                        plot_skeleton_kpts(im0s, kpts=det[idx, 6:].squeeze(), steps=3,
                                           orig_shape=im0s.shape[:2], kpt_num=17)  # 绘制姿态点

                    image1 = Image.fromarray(im0s, 'RGB')
                    image1.save(f'test_demo/{Path(path).stem}_{angle_val}_{frame_id}.png')

            break

        #

    del session, dataset
    return data_dict


def track_situp_videos_fusion(data_cls: ConfigDict) -> dict:
    """
    仰卧起坐-防作弊 PoseRGB双流融合 采集项目      created by @Moss 20250617
    :param data_cls 配置信息汇总 字典类
    model_info: 姿态模型信息 {pth:模型路径, imgsz:推理时使用的输入图像尺寸}
    mark_info: 标定信息 such {'vid_width': 1920, 'ReadyPt1_x': 左上, 'ReadyPt2_x': 右下}
    vid_pth: 视频数据路径 --来自配置文件Config/baseline.yml
    :return:
    """
    session = data_cls.get('session', None)
    num_bodys = data_cls.fusion_sit_up.num_bodys
    vid_pth, model_info, mark_info = data_cls.videos_pth, data_cls.model_info, data_cls.mark_info

    files = read_files(vid_pth)  # 数据列表

    dataset = LoadImages(files, img_size=model_info['infer_size'], vid_width=1920)
    # 循环 视频-> frames

    data_dict, xyxy_first = {}, False
    # saved_img, obstruct_frames = True,
    if not session:
        use_trt = model_info.get('trt_infer', False)
        if use_trt:
            model_path = model_info['yolo_pose']
            p, f = os.path.split(model_path)  # 增加Engine前缀
            engine_path = os.path.join(p, 'Engine_' + f)
            engine_file = Path(engine_path).with_suffix('.engine')
            print('Trt: ', engine_file)
            if not engine_file.exists():
                print('Onnx model to engine. Please Wait')
                from tensorRT.trt_inference import build_engine
                engine = build_engine(model_path, True)
                if engine:
                    print('Engine successfully created!')
            else:
                print('Engine model exist. and Load it.')
                from tensorRT.trt_inference import PoseTensorRTInference
                session = PoseTensorRTInference(engine_file, conf=0.5, iou=0.4)
        else:
            session = onnxruntime.InferenceSession(model_info['yolo_pose'], providers=['CUDAExecutionProvider'])
            # assert len(session.get_providers()) == 2, f"@Moss: {session.get_providers()} may use CPU Infer,please check your ENV"

    for path, img, im0s, vid_id, frame_id, _ in dataset:
        print(f'video {dataset.count + 1}/{dataset.nf} ({dataset.frame}/{dataset.video_frames}) {Path(path).name}: ', end=' ')
        if frame_id == 1:
            xyxy_first = False  # 用于存储有效数据信息, 存储首个有效帧的人体框坐标

        # model_inference
        preds = pose_v8_infer(session, img=img)  # 默认v8

        data_base = (path, im0s.shape, frame_id, dataset.video_frames)

        for i, det in enumerate(preds):  # detections per image
            if len(det) == 0:  # 'No person in Img'
                saveformed(data_dict, data_base, status=-1, skpt=None, img_save=False)  # -1: 无人状态, 无骨骼数据
                continue

            # Rescale boxes from img_size to im0 size
            det[:, :4] = scale_coords(img.shape[2:], det[:, :4], im0s.shape, kpt_label=False)
            det[:, 6:] = scale_coords(img.shape[2:], det[:, 6:], im0s.shape, kpt_label=True, step=3)

            det = torch.from_numpy(det).float() if isinstance(det, np.ndarray) else det
            # 根据第1帧：获取目标框的索引，确定目标主体的人体框坐标
            if not xyxy_first:
                xyxy_first = situp_target_logic_1(mark_info, det=det)
                _index = torch.where(torch.all(torch.eq(det[:, :4], torch.stack(xyxy_first)), dim=1))[0].item() if xyxy_first else False
            else:
                # 判断每帧的人体框，是否符合首帧目标框de交集面积，从而找到该人体idx
                _index = situp_target_logic_2(xyxy_first, det=det)
            if _index is False:
                saveformed(data_dict, data_base, status=0, skpt=None, img_save=False)  # 0: 有人但不符合逻辑, 无骨骼数据
                continue

            # 存储有效数据: 获取目标周围的 作弊嫌疑人, top_suspects[0]是目标人体
            top_suspects, way = suspect_target_logic(num_bodys=num_bodys, index=_index, det=det)
            suspects = [det[_index, :]]         # 第1个是目标主体
            for suspect_idx in top_suspects[1:]:
                if len(suspect_idx):
                    suspects.append(det[suspect_idx[0], :])
                else:
                    suspects.append(None)
            # 记录角度：判断目标T的角度[肩-膝-脚], 是否符合115°和95°的阈值，确定躺平和促膝状态
            angle_val = sitUp_status_judge_logic(way, det_index=det[_index, 6:])        # 状态码: 角度
            saveformed(data_dict, data_base, status=angle_val, skpt=suspects, img_save=im0s)  # 1: 有人且符合逻辑, 存储骨骼数据 和 img信息
            print(f"angle-{angle_val}")

            break

        #

    del session, dataset
    return data_dict


def track_sitreach_videos_logic(data_cls: ConfigDict, multiprocess=False) -> dict:
    """
    坐位体前屈 防作弊项目
    # @Moss 加入逻辑 过滤盲区蹲下的人体      2024102118 15:50 TODO
    # :param model_info: 姿态模型信息 {pth:模型路径, imgsz:推理时使用的输入图像尺寸}
    # :param mark_info: 标定信息 such {'vid_width': 1920, 'ReadyPt1_x': 左上, 'ReadyPt2_x': 右下}
    # :param vid_pth: 视频数据路径 --来自配置文件Config/baseline.yml
    :return:
    """
    num_bodys = data_cls.fake_sit_reach.num_bodys
    vid_pth, model_info, mark_info = data_cls.videos_pth, data_cls.model_info, data_cls.mark_info
    board_model = data_cls.board_model

    files = read_files(vid_pth)  # 数据列表

    dataset = LoadImages(files, img_size=model_info['infer_size'], vid_width=1920)
    # 循环 视频-> frames
    data_dict, way, board_width, board_info, xyxy_board = {}, None, int, {}, None  # 用于存储有效数据信息
    if not multiprocess:
        session = onnxruntime.InferenceSession(model_info['yolo_pose'], providers=['CUDAExecutionProvider'],
                                               provider_options=[{'device_id': data_cls.card_id}])
        session_board = onnxruntime.InferenceSession(board_model['yolo_detect'], providers=['CUDAExecutionProvider'],
                                                     provider_options=[{'device_id': data_cls.card_id}])  # 板检测
        # assert len(session.get_providers()) == 2, f"@Moss: {session.get_providers()} may use CPU Infer,please check your ENV"
    else:
        session, session_board = data_cls.get('session', None), data_cls.get('session_board', None)

    for path, img, im0s, vid_id, frame_id in dataset:
        print(f'video {dataset.count + 1}/{dataset.nf} ({dataset.frame}/{dataset.video_frames}) {Path(path).name}:',end=' ')
        if img is None:
            continue
        # model_inference
        preds = pose_v8_infer(session, img=img)  # version='v10'
        if not multiprocess:
            xyxy_board, way, board_width, board_info = detect_board_v8_infer(session_board, img_ori=im0s,
                                                                             img_size=board_model['infer_size'])  # 板检测-获取板子的坐标
            if frame_id == 1:
                first_width = copy.deepcopy(board_width)
            if xyxy_board is False or abs(first_width - board_width) > 100:
                dataset.jump_video = True
                continue
        else:
            if frame_id == 1:
                xyxy_board, way, board_width, board_info = detect_board_v8_infer(session_board, img_ori=im0s,
                                                                                 img_size=board_model[
                                                                                     'infer_size'])  # 板检测-获取板子的坐标
            if xyxy_board is False:
                dataset.jump_video = True
                continue

        data_base = (path, im0s.shape, frame_id, dataset.video_frames, board_info)
        for i, det in enumerate(preds):  # detections per image
            if len(det) == 0:  # 'No person in Img'
                saveformed(data_dict, data_base, status=-1, skpt=None, img_save=False)  # -1: 无人状态, 无骨骼数据
                continue

            # Rescale boxes from img_size to im0 size
            det[:, :4] = scale_coords(img.shape[2:], det[:, :4], im0s.shape, kpt_label=False)
            det[:, 6:] = scale_coords(img.shape[2:], det[:, 6:], im0s.shape, kpt_label=True, step=3)

            # 获取目标框的索引
            _index = sitreach_target_logic(mark_info=board_width, det=det, way=way)
            if _index is False:
                saveformed(data_dict, data_base, status=0, skpt=None, img_save=False)  # 0: 有人但不符合逻辑, 无骨骼数据
                continue
            # 存储有效数据
            # 获取目标周围(TBS)的 作弊嫌疑人, top_suspects[0]是目标人体
            top_suspects, _ = suspect_target_logic(num_bodys=num_bodys, index=_index, det=det)  # (T, [B], [S])
            suspects = [det[_index, :]]
            for suspect_idx in top_suspects[1:]:
                if len(suspect_idx):
                    suspects.append(det[suspect_idx[0], :])
                else:
                    suspects.append(None)
            # 记录刻度：判断目标T的手腕点是否在板框内, 是否达到最远距离
            scale_val = sitReach_status_judge_logic(way, det_index=det[_index, 6:], pred_board=xyxy_board)
            saveformed(data_dict, data_base, status=scale_val, skpt=suspects, img_save=im0s)  # 1: 有人且符合逻辑, 存储骨骼数据 和 img信息
            print(scale_val)


            if scale_val > 0:
                main_box = det[_index][:4]
                main_skpt = det[_index, 6:]             # 主体的骨骼点
                # 框是一个梯形
                if way == 'R':
                    box_left = (main_box[0], main_skpt[[10,13]].mean()-abs(main_skpt[19]-main_skpt[16])*0.70)       # 左上角
                    foot_centre = (max(main_skpt[[39,42]].mean(), main_skpt[[21,24]].mean()), main_box[3])         # 框右下角
                    C_angle90 = (main_box[0], main_box[3])
                    B_leftPt = (main_box[0]-abs(max(main_skpt[33], main_skpt[36])- main_box[0]), main_box[3])         # 左下角
                else:
                    box_left = (min(main_skpt[[39,42]].mean(), main_skpt[[21,24]].mean()), main_skpt[[10, 13]].mean() - abs(main_skpt[19] - main_skpt[16]) * 0.70)
                    foot_centre = (main_box[2], main_box[3])         #
                    C_angle90 = foot_centre
                    B_leftPt = (main_box[3]+abs(max(main_skpt[33], main_skpt[36])- main_box[2]), main_box[3])


                for idx in range(len(det)):
                    if idx == _index:
                        continue
                    # 判断point点(手腕或腰) 是否在定义的box内 box为 直角△+矩形框
                    wrist_l, wrist_r = is_point_inbox(point=det[idx, 6:][[27,28]], left_top=box_left, right_down=foot_centre), \
                                       is_point_inbox(point=det[idx, 6:][[30,31]], left_top=box_left, right_down=foot_centre)        # 手腕
                    wrist_l2, wrist_r2 = is_point_inRtriangle(A=box_left, B=B_leftPt, C=C_angle90, P=det[idx, 6:][[27,28]]), \
                                         is_point_inRtriangle(A=box_left, B=B_leftPt, C=C_angle90, P=det[idx, 6:][[30,31]]),        # 三角形内包手腕
                    waist_l, waist_r = is_point_inbox(point=det[idx, 6:][[33,34]], left_top=box_left, right_down=foot_centre), \
                                       is_point_inbox(point=det[idx, 6:][[36,37]], left_top=box_left, right_down=foot_centre)        # 腰
                    knee_l2, knee_r2 = is_point_inRtriangle(A=box_left, B=B_leftPt, C=C_angle90, P=det[idx, 6:][[39,40]]), \
                                         is_point_inRtriangle(A=box_left, B=B_leftPt, C=C_angle90, P=det[idx, 6:][[42,43]]),        # 三角形内包膝盖
                    if wrist_l or wrist_r or wrist_l2 or wrist_r2:
                        if waist_l or waist_r or knee_l2 or knee_r2:
                            data_dict[path]['obstruct_frames'].append(frame_id)
            break

    return data_dict


def track_sitreach_videos_img(data_cls: ConfigDict, multiprocess=False) -> dict:
    """
    单人-坐位体前屈 防作弊项目 新方案 获取原始图片和裁剪后的图片
    # fixed 从原始视频中，获取出成绩的帧 img 抽样保存     20241105 14:43 TODO
    # :param model_info: 姿态模型信息 {pth:模型路径, imgsz:推理时使用的输入图像尺寸}
    # :param mark_info: 标定信息 such {'vid_width': 1920, 'ReadyPt1_x': 左上, 'ReadyPt2_x': 右下}
    # :param vid_pth: 视频数据路径 --来自配置文件Config/baseline.yml
    :return:   *.jpg
    """
    num_bodys = data_cls.fake_sit_reach.num_bodys
    vid_pth, model_info, mark_info = data_cls.videos_pth, data_cls.model_info, data_cls.mark_info
    board_model = data_cls.board_model

    files = read_files(vid_pth)  # 数据列表

    dataset = LoadImages(files, img_size=model_info['infer_size'], vid_width=1920)
    # 循环 视频-> frames
    data_dict, way, board_width, board_info, xyxy_board = {}, None, int, {}, None  # 用于存储有效数据信息
    if not multiprocess:
        session = onnxruntime.InferenceSession(model_info['yolo_pose'], providers=['CUDAExecutionProvider'],
                                               provider_options=[{'device_id': data_cls.card_id}])
        session_board = onnxruntime.InferenceSession(board_model['yolo_detect'], providers=['CUDAExecutionProvider'],
                                                     provider_options=[{'device_id': data_cls.card_id}])  # 板检测
        # assert len(session.get_providers()) == 2, f"@Moss: {session.get_providers()} may use CPU Infer,please check your ENV"
    else:
        session, session_board = data_cls.get('session', None), data_cls.get('session_board', None)

    for path, img, im0s, vid_id, frame_id, _ in dataset:
        print(f'video {dataset.count + 1}/{dataset.nf} ({dataset.frame}/{dataset.video_frames -1}) {Path(path).name}:',end=' ')
        if frame_id == 1:
            max_scale = 0

        if img is None:
            continue
        # model_inference
        preds = pose_v8_infer(session, img=img)  # version='v10'
        if not multiprocess:
            xyxy_board, way, board_width, board_info = detect_board_v8_infer(session_board, img_ori=im0s,
                                                                             img_size=board_model['infer_size'])  # 板检测-获取板子的坐标
            if frame_id == 1:
                fid, save_img = 0, True         # 初始化 是否保存图片
                first_width = copy.deepcopy(board_width)
            if xyxy_board is False:                         # or abs(first_width - board_width) > 100
                dataset.jump_video = True
                print(f"Jump_thisVid as single-board{xyxy_board}")
                continue
        else:
            if frame_id == 1:
                xyxy_board, way, board_width, board_info = detect_board_v8_infer(session_board, img_ori=im0s,
                                                                                 img_size=board_model[
                                                                                     'infer_size'])  # 板检测-获取板子的坐标
            if xyxy_board is False:
                dataset.jump_video = True
                continue

        data_base = (path, im0s.shape, frame_id, dataset.video_frames, board_info)
        scale_val = 0
        for i, det in enumerate(preds):  # detections per image
            if len(det) == 0:  # 'No person in Img'
                saveformed(data_dict, data_base, status=-1, skpt=None, img_save=False)  # -1: 无人状态, 无骨骼数据
                continue

            # Rescale boxes from img_size to im0 size
            det[:, :4] = scale_coords(img.shape[2:], det[:, :4], im0s.shape, kpt_label=False)
            det[:, 6:] = scale_coords(img.shape[2:], det[:, 6:], im0s.shape, kpt_label=True, step=3)

            # 获取目标框的索引
            _index = sitreach_target_logic(mark_info=first_width, det=det, way=way)
            if _index is False:
                saveformed(data_dict, data_base, status=0, skpt=None, img_save=False)  # 0: 有人但不符合逻辑, 无骨骼数据
                continue

            # 记录刻度：判断目标T的手腕点是否在板框内, 是否达到最远距离
            scale_val = sitReach_status_judge_logic(way, det_index=det[_index, 6:], pred_board=xyxy_board)
            saveformed(data_dict, data_base, status=scale_val, skpt=False, img_save=im0s)  # 1: 有人且符合逻辑, 存储骨骼数据 和 img信息
            print(scale_val, end=' ')
            if max_scale <= scale_val:
                max_scale = scale_val
                scale_val_max, scale_fid = scale_val, frame_id
                saved_im0s = im0s
                target = det[_index, :]

        if save_img and max_scale > 0 and frame_id==dataset.video_frames-1:
            if way == 'L':
                saved_im0s = np.fliplr(saved_im0s)  # 水平镜像
                # 镜像板坐标
                xyxy_board = torch.tensor([1920 - xyxy_board[2], xyxy_board[1], 1920 - xyxy_board[0], xyxy_board[3]])
                board_info['xyxy_board'] = xyxy_board
                # 镜像姿态点
                indices = torch.arange(0, 51, 3)
                target[6:][indices] = 1920 - target[6:][indices]
                target[:6][[0, 2]] = 1920 - target[:6][[0, 2]]
                rb, lb = copy.deepcopy(target[:6][0]), copy.deepcopy(target[:6][2])
                target[:6][0] = lb
                target[:6][2] = rb
                print(target[:6])
            board_xy = board_info['xyxy_board']
            topLx, topLy, topRx, topRy = crop_logic(target, board_xy)
            if topLx < topRx and topLy < topRy:
                cropped_img = saved_im0s[topLy:topRy, topLx:topRx]  # 裁剪
                save_images(cropped_img, saved_im0s, path, scale_val_max, scale_fid)
        else:
            print(f'score: {scale_val}/{max_scale}')

    return data_dict


def track_MultiSitreach_videos_img(data_cls: ConfigDict, multiprocess=False):
    """
    多人-坐位体前屈 防作弊项目 新方案 获取原始图片和裁剪后的图片
    # :param model_info: 姿态模型信息 {pth:模型路径, imgsz:推理时使用的输入图像尺寸}
    # :param mark_info: 标定信息 such {'vid_width': 1920, 'ReadyPt1_x': 左上, 'ReadyPt2_x': 右下}
    # :param vid_pth: 视频数据路径 --来自配置文件Config/baseline.yml
    :return:   *.jpg
    """
    vid_pth, model_info, mark_info = data_cls.videos_pth, data_cls.model_info, data_cls.mark_info
    board_model = data_cls.board_model

    files = read_files(vid_pth)  # 数据列表

    dataset = LoadImages(files, img_size=model_info['infer_size'], vid_width=1920)
    # 循环 视频-> frames
    data_dict, way, board_width, board_info, xyxy_board = {}, None, int, {}, None  # 用于存储有效数据信息
    if not multiprocess:
        session = onnxruntime.InferenceSession(model_info['yolo_pose'], providers=['CUDAExecutionProvider'],
                                               provider_options=[{'device_id': data_cls.card_id}])
        session_board = onnxruntime.InferenceSession(board_model['yolo_detect'], providers=['CUDAExecutionProvider'],
                                                     provider_options=[{'device_id': data_cls.card_id}])  # 板检测
        # assert len(session.get_providers()) == 2, f"@Moss: {session.get_providers()} may use CPU Infer,please check your ENV"
    else:
        session, session_board = data_cls.get('session', None), data_cls.get('session_board', None)

    for path, img, im0s, vid_id, frame_id, oriVid_shape in dataset:
        print(f'video {dataset.count + 1}/{dataset.nf} ({dataset.frame}/{dataset.video_frames -1}) {Path(path).name}:',end=' ')
        if img is None:
            continue
        # model_inference
        preds = pose_v8_infer(session, img=img)  # version='v10'
        if frame_id == 1:
            board_pos = int(Path(path).name.split('_face')[0][-1])
            _, _, _, board_info_lst = detect_board_v8_infer(session_board, img_ori=im0s,
                                                            img_size=board_model['infer_size'], board_pos=board_pos, vid_H=oriVid_shape[0])  # 板检测-获取板子的坐标
            fid, save_img, max_scale, use_target = 0, True, 0, False  # 初始化 是否保存图片
            scale_val = None
            # 第1帧需要检测到板子，否则跳过该Vid
            if board_info_lst is False or len(board_info_lst)==0:
                dataset.jump_video = True
                print(f"Jump_thisVid as M-board{board_info_lst}")
                continue
            else:
                board_info = board_info_lst[0]
                use_pos, way = board_info['pos'], board_info['way']
                xyxy_board = board_info['xyxy_board']
                per_box = board_info['per_box']


        data_base = (path, im0s.shape, frame_id, dataset.video_frames, board_info_lst)
        for i, det in enumerate(preds):  # detections per image
            if len(det) == 0:  # 'No person in Img'
                saveformed(data_dict, data_base, status=-1, skpt=None, img_save=False)  # -1: 无人状态, 无骨骼数据
                continue

            # Rescale boxes from img_size to im0 size
            det[:, :4] = scale_coords(img.shape[2:], det[:, :4], im0s.shape, kpt_label=False)
            det[:, 6:] = scale_coords(img.shape[2:], det[:, 6:], im0s.shape, kpt_label=True, step=3)

            # 获取{board_id: person_Skpt}
            per_id_dict = Multi_sitreach_target_logic(mark_info=board_info_lst, det=det)
            target = per_id_dict[use_pos-1]
            if target is False:
                saveformed(data_dict, data_base, status=0, skpt=None, img_save=False)  # 0: 有人但不符合逻辑, 无骨骼数据
                continue

            # 记录刻度：判断目标T的手腕点是否在板框内, 是否达到最远距离
            scale_val = sitReach_status_judge_logic(way, det_index=target[6:], pred_board=xyxy_board)
            saveformed(data_dict, data_base, status=scale_val, skpt=False, img_save=im0s)  # 1: 有人且符合逻辑, 存储骨骼数据 和 img信息
            print(scale_val, end=' ')
            if max_scale <= scale_val:
                max_scale = scale_val
                scale_val_max, scale_fid = scale_val, frame_id
                use_target = target
                saved_im0s = im0s

        # 所有帧结束：logic 判决阶段
        if frame_id == dataset.video_frames - 1:
            if (use_target is not False) and save_img and max_scale > 0:
                if way == 'L':
                    saved_im0s = np.fliplr(saved_im0s)  # 水平镜像
                    # 镜像板坐标
                    xyxy_board = torch.tensor([1920 - xyxy_board[2], xyxy_board[1], 1920 - xyxy_board[0], xyxy_board[3]])
                    # 镜像姿态点
                    indices = torch.arange(0, 51, 3)
                    use_target[6:][indices] = 1920 - use_target[6:][indices]
                    use_target[:6][[0, 2]] = 1920 - use_target[:6][[0, 2]]
                    rb, lb = copy.deepcopy(use_target[:6][0]), copy.deepcopy(use_target[:6][2])
                    use_target[:6][0] = lb
                    use_target[:6][2] = rb

                topLx, topLy, topRx, topRy = crop_logic_Multi(use_target, xyxy_board, per_box=per_box, pos=use_pos)
                if topLx < topRx and topLy < topRy:
                    cropped_img = saved_im0s[topLy:topRy, topLx:topRx]  # 裁剪
                    save_images(cropped_img, saved_im0s, path, use_pos, scale_fid, multi=True)
        else:
            print(f'score: {scale_val}/{max_scale}')




    return data_dict


def track_situp_videos(data_cls: ConfigDict) -> dict:
    """
    仰卧起坐 防作弊项目
    :param data_cls 配置信息汇总 字典类
    model_info: 姿态模型信息 {pth:模型路径, imgsz:推理时使用的输入图像尺寸}
    mark_info: 标定信息 such {'vid_width': 1920, 'ReadyPt1_x': 左上, 'ReadyPt2_x': 右下}
    vid_pth: 视频数据路径 --来自配置文件Config/baseline.yml
    :return:
    """
    session = data_cls.get('session', None)
    num_bodys = data_cls.fake_sit_up.num_bodys
    vid_pth, model_info, mark_info = data_cls.videos_pth, data_cls.model_info, data_cls.mark_info

    files = read_files(vid_pth)  # 数据列表

    dataset = LoadImages(files, img_size=model_info['infer_size'], vid_width=1920)
    # 循环 视频-> frames

    data_dict, xyxy_first = {}, False
    if not session:
        session = onnxruntime.InferenceSession(model_info['yolo_pose'], providers=['CUDAExecutionProvider'])
        # assert len(session.get_providers()) == 2, f"@Moss: {session.get_providers()} may use CPU Infer,please check your ENV"
    for path, img, im0s, vid_id, frame_id in dataset:
        print(f'video {dataset.count + 1}/{dataset.nf} ({dataset.frame}/{dataset.video_frames}) {Path(path).name}: ', end=' ')
        if frame_id == 1:
            xyxy_first = False  # 用于存储有效数据信息, 存储首个有效帧的人体框坐标

        # model_inference
        preds = pose_v8_infer(session, img=img)  # 默认v8

        data_base = (path, im0s.shape, frame_id, dataset.video_frames)
        for i, det in enumerate(preds):  # detections per image
            if len(det) == 0:  # 'No person in Img'
                saveformed(data_dict, data_base, status=-1, skpt=None, img_save=False)  # -1: 无人状态, 无骨骼数据
                continue

            # Rescale boxes from img_size to im0 size
            det[:, :4] = scale_coords(img.shape[2:], det[:, :4], im0s.shape, kpt_label=False)
            det[:, 6:] = scale_coords(img.shape[2:], det[:, 6:], im0s.shape, kpt_label=True, step=3)

            # 根据第1帧：获取目标框的索引，确定目标主体的人体框坐标
            if not xyxy_first:
                xyxy_first = situp_target_logic_1(mark_info, det=det)
                _index = torch.where(torch.all(torch.eq(det[:, :4], torch.stack(xyxy_first)), dim=1))[
                    0].item() if xyxy_first else False
            else:
                # 判断每帧的人体框，是否符合首帧目标框de交集面积，从而找到该人体idx
                _index = situp_target_logic_2(xyxy_first, det=det)
            if _index is False:
                saveformed(data_dict, data_base, status=0, skpt=None, img_save=False)  # 0: 有人但不符合逻辑, 无骨骼数据
                continue

            # 存储有效数据: 获取目标周围的 作弊嫌疑人, top_suspects[0]是目标人体
            top_suspects, way = suspect_target_logic(num_bodys=num_bodys, index=_index, det=det)
            suspects = [det[_index, :]]
            for suspect_idx in top_suspects[1:]:
                if len(suspect_idx):
                    suspects.append(det[suspect_idx[0], :])
                else:
                    suspects.append(None)
            # 记录角度：判断目标T的角度[肩-膝-脚], 是否符合95°和115°的阈值，确定躺平和促膝状态
            angle_val = sitUp_status_judge_logic(way, det_index=det[_index, 6:])
            saveformed(data_dict, data_base, status=angle_val, skpt=suspects,
                       img_save=im0s)  # 1: 有人且符合逻辑, 存储骨骼数据 和 img信息
            print(f"angle-{angle_val}")

            break

        #

    del session, dataset
    return data_dict


def track_sitreach_videos(data_cls: ConfigDict, multiprocess=False) -> dict:
    """
    坐位体前屈 防作弊项目
    # :param model_info: 姿态模型信息 {pth:模型路径, imgsz:推理时使用的输入图像尺寸}
    # :param mark_info: 标定信息 such {'vid_width': 1920, 'ReadyPt1_x': 左上, 'ReadyPt2_x': 右下}
    # :param vid_pth: 视频数据路径 --来自配置文件Config/baseline.yml
    :return:
    """
    num_bodys = data_cls.fake_sit_reach.num_bodys
    vid_pth, model_info, mark_info = data_cls.videos_pth, data_cls.model_info, data_cls.mark_info
    board_model = data_cls.board_model

    files = read_files(vid_pth)  # 数据列表

    dataset = LoadImages(files, img_size=model_info['infer_size'], vid_width=1920)
    # 循环 视频-> frames
    data_dict, way, board_width, board_info, xyxy_board = {}, None, int, {}, None  # 用于存储有效数据信息
    if not multiprocess:
        session = onnxruntime.InferenceSession(model_info['yolo_pose'], providers=['CUDAExecutionProvider'],
                                               provider_options=[{'device_id': data_cls.card_id}])
        session_board = onnxruntime.InferenceSession(board_model['yolo_detect'], providers=['CUDAExecutionProvider'],
                                                     provider_options=[{'device_id': data_cls.card_id}])  # 板检测
        # assert len(session.get_providers()) == 2, f"@Moss: {session.get_providers()} may use CPU Infer,please check your ENV"
    else:
        session, session_board = data_cls.get('session', None), data_cls.get('session_board', None)

    for path, img, im0s, vid_id, frame_id in dataset:
        print(f'video {dataset.count + 1}/{dataset.nf} ({dataset.frame}/{dataset.video_frames}) {Path(path).name}:',end=' ')
        if img is None:
            continue
        # model_inference
        preds = pose_v8_infer(session, img=img)  # version='v10'
        if not multiprocess:
            xyxy_board, way, board_width, board_info = detect_board_v8_infer(session_board, img_ori=im0s,
                                                                             img_size=board_model[
                                                                                 'infer_size'])  # 板检测-获取板子的坐标
            if frame_id == 1:
                first_width = copy.deepcopy(board_width)
            if xyxy_board is False or abs(first_width - board_width) > 100:
                dataset.jump_video = True
                continue
        else:
            if frame_id == 1:
                xyxy_board, way, board_width, board_info = detect_board_v8_infer(session_board, img_ori=im0s,
                                                                                 img_size=board_model[
                                                                                     'infer_size'])  # 板检测-获取板子的坐标
            if xyxy_board is False:
                dataset.jump_video = True
                continue

        data_base = (path, im0s.shape, frame_id, dataset.video_frames, board_info)
        for i, det in enumerate(preds):  # detections per image
            if len(det) == 0:  # 'No person in Img'
                saveformed(data_dict, data_base, status=-1, skpt=None, img_save=False)  # -1: 无人状态, 无骨骼数据
                continue

            # Rescale boxes from img_size to im0 size
            det[:, :4] = scale_coords(img.shape[2:], det[:, :4], im0s.shape, kpt_label=False)
            det[:, 6:] = scale_coords(img.shape[2:], det[:, 6:], im0s.shape, kpt_label=True, step=3)

            # 获取目标框的索引
            _index = sitreach_target_logic(mark_info=board_width, det=det)
            if _index is False:
                saveformed(data_dict, data_base, status=0, skpt=None, img_save=False)  # 0: 有人但不符合逻辑, 无骨骼数据
                continue
            # 存储有效数据
            # 获取目标周围(TBS)的 作弊嫌疑人, top_suspects[0]是目标人体
            top_suspects, _ = suspect_target_logic(num_bodys=num_bodys, index=_index, det=det)  # (T, [B], [S])
            suspects = [det[_index, :]]
            for suspect_idx in top_suspects[1:]:
                if len(suspect_idx):
                    suspects.append(det[suspect_idx[0], :])
                else:
                    suspects.append(None)
            # 记录刻度：判断目标T的手腕点是否在板框内, 是否达到最远距离
            scale_val = sitReach_status_judge_logic(way, det_index=det[_index, 6:], pred_board=xyxy_board)
            saveformed(data_dict, data_base, status=scale_val, skpt=suspects, img_save=im0s)  # 1: 有人且符合逻辑, 存储骨骼数据 和 img信息
            print(scale_val)
            break

    return data_dict


def track_sitreach_videos_RunImgs(data_cls: ConfigDict, multiprocess=False) -> dict:
    """
    坐位体前屈 防作弊项目 新方案
        视频中的图像分类，分类是否正常、作弊和干扰
    # :param model_info: 姿态模型信息 {pth:模型路径, imgsz:推理时使用的输入图像尺寸}
    # :param mark_info: 标定信息 such {'vid_width': 1920, 'ReadyPt1_x': 左上, 'ReadyPt2_x': 右下}
    # :param vid_pth: 视频数据路径 --来自配置文件Config/baseline.yml
    :return:
    """
    vid_pth, model_info, mark_info = data_cls.videos_pth, data_cls.model_info, data_cls.mark_info
    board_model = data_cls.board_model

    files = read_files(vid_pth)  # 数据列表

    dataset = LoadImages(files, img_size=model_info['infer_size'], vid_width=1920)
    # 循环 视频-> frames
    data_dict, way, board_width, board_info, xyxy_board = {}, None, int, {}, None  # 用于存储有效数据信息
    if not multiprocess:
        session = onnxruntime.InferenceSession(model_info['yolo_pose'], providers=['CUDAExecutionProvider'],
                                               provider_options=[{'device_id': data_cls.card_id}])
        session_board = onnxruntime.InferenceSession(board_model['yolo_detect'], providers=['CUDAExecutionProvider'],
                                                     provider_options=[{'device_id': data_cls.card_id}])  # 板检测
        # assert len(session.get_providers()) == 2, f"@Moss: {session.get_providers()} may use CPU Infer,please check your ENV"
    else:
        session, session_board = data_cls.get('session', None), data_cls.get('session_board', None)

    for path, img, im0s, vid_id, frame_id in dataset:
        print(f'video {dataset.count + 1}/{dataset.nf} ({dataset.frame}/{dataset.video_frames}) {Path(path).name}:',end=' ')
        if img is None:
            continue
        # model_inference
        preds = pose_v8_infer(session, img=img)  # version='v10'
        if not multiprocess:
            xyxy_board, way, board_width, board_info = detect_board_v8_infer(session_board, img_ori=im0s,
                                                                             img_size=board_model[
                                                                                 'infer_size'])  # 板检测-获取板子的坐标
            if frame_id == 1:
                first_width = copy.deepcopy(board_width)
            if xyxy_board is False or abs(first_width - board_width) > 100:
                dataset.jump_video = True
                continue
        else:
            if frame_id == 1:
                fid = 0         # 初始化出成绩的 帧的数量
                xyxy_board, way, board_width, board_info = detect_board_v8_infer(session_board, img_ori=im0s,
                                                                                 img_size=board_model['infer_size'])  # 板检测-获取板子的坐标
            if xyxy_board is False:
                dataset.jump_video = True
                continue

        data_base = (path, im0s.shape, frame_id, dataset.video_frames, board_info)
        for i, det in enumerate(preds):  # detections per image
            if len(det) == 0:  # 'No person in Img'
                saveformed(data_dict, data_base, status=-1, skpt=None, img_save=False)  # -1: 无人状态, 无骨骼数据
                continue

            # Rescale boxes from img_size to im0 size
            det[:, :4] = scale_coords(img.shape[2:], det[:, :4], im0s.shape, kpt_label=False)
            det[:, 6:] = scale_coords(img.shape[2:], det[:, 6:], im0s.shape, kpt_label=True, step=3)

            # 获取目标框的索引
            _index = sitreach_target_logic(mark_info=board_width, det=det, way=way)
            if _index is False:
                saveformed(data_dict, data_base, status=0, skpt=None, img_save=False)  # 0: 有人但不符合逻辑, 无骨骼数据
                continue
            # 存储有效数据
            # 记录刻度：判断目标T的手腕点是否在板框内, 是否达到最远距离
            scale_val = sitReach_status_judge_logic(way, det_index=det[_index, 6:], pred_board=xyxy_board)
            saveformed(data_dict, data_base, status=scale_val, skpt=det[_index], img_save=im0s)  # 1: 有人且符合逻辑, 存储主体目标骨骼数据 和 img信息
            if scale_val > 0:
                # 间隔判断该帧的状态
                fid += 1
                if fid % 3 == 1:  # %3: 间隔2帧做 图像推理
                    im0s = cv2.cvtColor(im0s, cv2.COLOR_BGR2RGB)
                    im0s = Image.fromarray(im0s)
                    img0s = data_cls.data_transform(im0s)  # expand batch dimension
                    img0s = torch.unsqueeze(img0s, dim=0)  # read class_indict
                    a_data = img0s.numpy()

                    cls_model = data_cls.classify_model
                    pred_np = cls_model.run([cls_model.get_outputs()[0].name], {cls_model.get_inputs()[0].name: a_data})[0]
                    pred = torch.from_numpy(pred_np)
                    predict = int(torch.argmax(torch.softmax(pred, dim=1)))

                    if predict > 0:         # 作弊或 干扰 类别
                        data_dict[path]['class_pre'] = predict          # 将模型的作弊违规结果 保存

                        # 写入
                        flg_txt_pred = data_cls.save_dir + f'/rec_pred_{predict}.txt'
                        with open(flg_txt_pred, 'a+') as flg:
                            flg.writelines(path + '\n')
                        print(scale_val, f', Write pred:{predict} ')

                        dataset.jump_video = True
                        continue
                    else:
                        print(scale_val)

            break

    for path, data in data_dict.items():
        if data.get('class_pre') is None:
            flg_txt_pred = data_cls.save_dir + f"/rec_pred_{'Normal'}.txt"
            with open(flg_txt_pred, 'a+') as flg:
                flg.writelines(path + '\n')

    return data_dict


def filter_run50_gradeVids(data_cls: ConfigDict) -> None:
    """
    50m 防作弊项目 筛选50m项目不出成绩/误触发视频
    :return:
    """
    vid_pth, save_dir, model_info = data_cls.videos_pth, data_cls.save_dir, data_cls.model_info

    files = read_files(vid_pth)  # 数据列表
    session = onnxruntime.InferenceSession(model_info['yolo_pose'], providers=['CUDAExecutionProvider'])
    dataset = LoadFrames(files, img_size=model_info['infer_size'], vid_width=1920)     # data init

    num = 1         # 计数器
    for path, img0, img, imgZ, count in dataset:
        file_name = Path(path).name
        if dataset.jump_video is False:
            if img0 is True:        # 判定is 相似帧
                shutMove_fileRun50(path, file_name, type='Hist', save_dir=save_dir)
                print(file_name, 'No-endCam')
            else:   # 存储有效视频 img0 is None or others
                # yolo-pose v8
                preds = pose_v8_infer(session, img=img)  # version='v10'
                # detections per image
                if len(preds[0])==0:         # 无人的状态
                    shutMove_fileRun50(path, file_name, type='NoStart', save_dir=save_dir)
                    print(file_name, 'No-Start')
                else:
                    Path(save_dir).mkdir(exist_ok=True)
                    shutil.move(path, Path(save_dir) / file_name)  # move vid
                    print(file_name, f'saved {num}-th')
                    num += 1
        else:
            print(file_name, 'short-vid')       # 短视频留在原地


    return


def track_run50_videos(data_cls: ConfigDict):
    """
    50m 防作弊项目  数据采集
    todo: 利用GPU的同时，可以同时使用CPU生成数据
    :return:
    """
    all_dets = {}       # 初始化姿态检测点存储的位置{frame_id: det}
    vid_pth, model_info, mark_info = data_cls.videos_pth, data_cls.model_info, data_cls.mark_info
    mark_end = mark_info['207']         # 默认207为终点标定
    mark_info.pop('207', None)

    if data_cls.Used_vid_txt == 'default':
        used_vids_pth = Path(vid_pth).parent / (Path(vid_pth).stem + '.txt')
    used_vids_pth.touch(exist_ok=True)
    with open(used_vids_pth, 'r', encoding="utf-8") as ok_f:
        Usedvid_lst = [line.strip() for line in ok_f.readlines()]

    files = read_files(vid_pth, Usedvid_lst)  # 数据列表


    # 获取ip和对应的name
    name_ip_dict = {}
    for fi in files:
        a_name = Path(fi).name
        if not all(x not in a_name for x in ['.207', 'second']):
            continue
        name_ip_dict[a_name] = re.findall(r"\.(\d+)_", a_name)[0]
    names_lst = list(name_ip_dict.keys())

    use_trt = model_info.get('trt_infer', False)
    if use_trt:
        model_path = model_info['yolo_pose']
        p, f = os.path.split(model_path)  # 增加Engine前缀
        engine_path = os.path.join(p, 'Engine_' + f)
        engine_file = Path(engine_path).with_suffix('.engine')
        print('Trt: ', engine_file)
        if not engine_file.exists():
            print('Onnx model to engine. Please Wait')
            from tensorRT.trt_inference import build_engine
            engine = build_engine(model_path, True)
            if engine:
                print('Engine successfully created!')
        else:
            print('Engine model exist. and Load it.')
            from tensorRT.trt_inference import PoseTensorRTInference
            session = PoseTensorRTInference(engine_file, conf=0.5, iou=0.4)
    else:
        session = onnxruntime.InferenceSession(model_info['yolo_pose'], providers=['CUDAExecutionProvider'])
        # assert len(session.get_providers()) == 2, f"@Moss: {session.get_providers()} may use CPU Infer,please check your ENV"

    dataset = LoadImages(files, img_size=model_info['infer_size'], vid_width=1920)
    for path, img, im0s, vid_id, frame_id, oriVid_shape in dataset:
        vid_name = Path(path).name
        print(f'video {dataset.count + 1}/{dataset.nf} ({dataset.frame}/{dataset.video_frames + 1}) {vid_name}: ', end=' ')

        if dataset.video_frames > 50*25:
            dataset.jump_video = True
            print(f'This Vid is too long more than 50s.')
            continue
        if frame_id == 1:
            data_dict = {}          # 记录信息的字典
            Start_Cam, endCam_rec = True, False  # 从起点画面开始记录，终点画面默认关闭
            mark_start = mark_re_match(vid_name, mark_info, names_lst, name_ip_dict)    # 根据视频名中的ip匹配标定

            P1s, P2s = Multi_location_50mStart(oriVid_shape[1], parse_=mark_start)

            det_results, pose_results, det_frames, np_frames = [[] for _ in range(len(P1s) - 1)], [[] for _ in range(len(P1s) - 1)], \
                                                               [[] for _ in range(len(P1s) - 1)], [[] for _ in range(len(P1s) - 1)]
            lose_frames = [[] for _ in range(len(P1s))]
            check_imgs = [[] for _ in range(len(P1s) - 1)]
            StartCam_Info = (det_results, pose_results, det_frames, np_frames, lose_frames, check_imgs)
            lose_frame_id = []
            all_dets = {fr_id:None for fr_id in range(1, dataset.video_frames + 1)}

            # TODO 记录当前帧信息
            with open(used_vids_pth, 'a+', encoding="utf-8") as ok_f:
                ok_f.write(f'{vid_name}\n')


        preds = pose_v8_infer(session, img=img)  # version='v10'

        data_base = (path, im0s.shape, frame_id, dataset.video_frames)
        xyxy_lists = [[] for _ in range(len(P1s) - 1)]

        det = preds[0]
        det = torch.from_numpy(det).float() if isinstance(det, np.ndarray) else det
        # breakpoint()
        im0 = im0s.copy()
        if len(det) == 0:  # 'No person in Img'
            print(f"No person this frame")
            lose_frame_id.append(frame_id)
            saveformed(data_dict, data_base, status=-1, skpt=None, img_save=False)  # -1: 无人状态, 无骨骼数据
            continue


        # Rescale boxes from img_size to im0 size
        det[:, :4] = scale_coords(img.shape[2:], det[:, :4], im0s.shape, kpt_label=False)
        det[:, 6:] = scale_coords(img.shape[2:], det[:, 6:], im0s.shape, kpt_label=True, step=3)
        all_dets[frame_id] = det

        if frame_id <= 4 * 25 or Start_Cam:
            check_imgs = run50_startCam_logic(StartCam_Info, xyxy_lists, im0s, frame_id, det, P1s, P2s)
            print('StartCam')

        # 6秒之后的视频，起点标定的位置是否 有符合开始条件的人体
        if frame_id > 3*25 and all([not x for x in xyxy_lists]) and Start_Cam:
            print('Judge')
            Start_Cam, endCam_rec = False, True     # 开启终点画面，关闭起点
            # endCam_pth = Path(path).parent / f"endCam_{Path(path).stem}_{frame_id}.jpg"
            # cv2.imwrite(endCam_pth.__str__(), im0s)
            # 初始化追踪器
            tracker = BYTETracker(frame_rate=25)
            print(f"初始化跟踪器 {tracker}")
            trackPose_lst, trackDet_lst = [], []
            # 加载终点标定, 终点默认的标定文件 是1080p
            P1e, P2e, P3e = Multi_location_50mEnd(width=1920, parse_=mark_end)
            used_tid_lst = []
            # 初始化跟踪图像
            track_imgs = []
            end_fid = 0

        if not endCam_rec:
            # print(f'起点画面未关闭')
            continue

        # 全图跟踪
        tracker_tuple = run50_endCam_tracker(tracker, im0s, det, track_imgs)
        if tracker_tuple is None:
            # saveformed(data_dict, data_base, status=-2, skpt=None, img_save=False)  # -2: 跟踪丢失
            print(f"未跟踪到人体, 可能跟踪丢失")
            trackPose_lst.append(None), trackDet_lst.append(None)
            continue
        idx, track_id, pose_track, box_track, tracks = tracker_tuple
        trackPose_lst.append(pose_track), trackDet_lst.append(tracks)       # 记录跟踪id for all person
        end_fid += 1                # 跟新1次track， 序号+1
        # print(f"end_fid-{end_fid}")
        print(track_id)

        # 测试代码
        # for track_i in tracks:
        #     track_pos = track_i[-4]
        #     t_box = track_i[:4].astype(int)
        #     plot_one_box(t_box, im0s, label=f' tId-{track_pos}', color=colors(0, True), line_thickness=4)
        # cv2.imwrite(f'{frame_id}.jpg', im0s)


        if not end_fid > (4 * 25):
            continue

        # 4秒(100+frame)时间后，先判断跟踪到的人中 过线，
        trackid_dict = overline_end(tracks, P3e)
        if not trackid_dict:
            continue
        # 有人在跑道内过线, 获取其所有信息
        for pos, trackid_lst in trackid_dict.items():     # {pos:[det_i]}
            if len(trackid_lst) == 0:
                continue
            # 理论上，当前帧1个测试位只有1个过线
            track_pos = trackid_lst[0]         # 获取过线的跟踪id
            # 再判断 时间内的人体是否在跑道内 超过40帧
            trackSkpt_pos, t_k = extract_det_pos(trackPose_lst, trackDet_lst, track_pos, track_imgs, limit=40, Pe=P2e[pos],
                                                 S1S2=[P1e[pos][0], P1e[pos][1], P1e[pos + 1][0], P1e[pos + 1][1]],
                                                 M1M2=[P2e[pos][0], P2e[pos][1], P2e[pos + 1][0], P2e[pos + 1][1]])
            if not trackSkpt_pos or track_pos in used_tid_lst:
                # 跟踪id不符合要求 或 跟踪id已在循环中使用过
                continue

            print('存储有效骨骼信息，及对应图像、帧数')
            End_star = frame_id - end_fid + 1       # 开始引入跟踪的 终点画面切入大概时间
            Trac_star = End_star + t_k              # 跟踪id首个不为None的frame_id


            base_info = pos, path, im0, im0s, frame_id, oriVid_shape, check_imgs, [P1s, P2s, P1e, P2e, P3e], [End_star, Trac_star, all_dets]

            # 画图 及 保存样本
            try:
                # 判断这些帧是否在四边形的区域内有人体以及取最大的人体
                gen_run50_lab_fake_img(base_info, trackSkpt_pos, track_pos)
            except Exception as e:
                print(e)
                continue

            used_tid_lst.append(track_pos)          # 已经过线的跟踪id不再使用


    return


def run50_endCam_tracker(tracker, im0s, det, track_imgs):
    """
    50m终点画面跟踪及跟踪器更新；
    return 跟新后的跟踪信息
    """
    results = []
    pred_kpts = det[:, 6:].view(len(det), *[17, 3])  # ref_skpt = det[:, 6:].clone()
    results.append(Results(im0s, path=None, names={0: 'person'}, boxes=det[:, :6], keypoints=pred_kpts))
    det_track = results[0].boxes.cpu().numpy()

    # print(det_track.xyxy.astype(int))
    tracks = tracker.update(det_track, im0s)  # 更新跟踪器
    track_len = len(tracks)

    if track_len == 0:
        return None
    # 更新跟踪信息
    idx = tracks[:, -1].astype(int)
    results[0] = results[0][idx]
    results[0].update(boxes=torch.as_tensor(tracks[:, :-1]))  # results[i].boxes.boxes

    track_id = tracks[:, -4].astype(int)        # 跟踪id
    pose_track = results[0].keypoints.data      # 跟踪的姿态
    box_track = results[0].boxes.xyxy.numpy()  # 跟踪框
    tracks[np.arange(track_len), :4] = det_track.xyxy[idx, :]  # 跟踪器前4列 对应替换为 实际检测框

    track_imgs.append(im0s)

    tracker_tuple = idx, track_id, pose_track, box_track, tracks

    return tracker_tuple


# -----------------------------------以下是逻辑判断---------------------------------------------------------------------------------
def run50_startCam_logic(StartCam_Info, xyxy_lists, im0s, frame_id, det, P1s, P2s):
    """
    50m起点信息获取及逻辑判断
    """
    im0 = im0s.copy()
    det_results, pose_results, det_frames, np_frames, lose_frames, check_imgs = StartCam_Info
    for pos in range(len(P1s) - 1):
        # 单个测试位，循环所有检测到的人，找到有交集的人，再筛选出交集最大的人
        points = [P1s[pos], P2s[pos], P1s[pos + 1], P2s[pos + 1]]
        for det_index, (*xyxy, conf, cls) in enumerate(reversed(det[:, :6])):
            # TODO: run50Proj net logic is xyxy[0]+0.5*(xyxy[2]-xyxy[0]) ∈ IOU and IOU >= 1000 pix
            area = overlap_area(x1=xyxy[0], y1=xyxy[1], x2=xyxy[2], y2=xyxy[3], points=points)
            if area > 1000:
                insider = point_in_polygon(x=xyxy[0] + 0.5 * (xyxy[2] - xyxy[0]), y=xyxy[3], points=points)
                if insider:
                    xyxy_lists[pos].append((xyxy, conf, cls))  # 保存备选点_multi

        if len(xyxy_lists[pos]) > 0:
            xyxy, conf, cls = compare_inter_max(xyxy_lists[pos], points)  # 取出交集面积最大的bbox

            xyxy_exp = torch.tensor(xyxy).expand(det[:, :4].size(0), 4)  # 将xyxy扩展到det相同的行数
            _index = torch.where(torch.all(torch.eq(det[:, :4], xyxy_exp), dim=1))[0].item()  # 取出 与选定的xyxy一致的det[index]， 保存该帧骨骼
            # det_results[pos].append(det[_index, :5].numpy())
            # pose_results[pos].append(det[_index, 6:].numpy())
            # det_frames[pos].append(frame_id)
            # np_frames[pos].append(im0s)

            # TODO 在单帧图片上绘制框
            start_im0 = copy.deepcopy(im0)
            plot_one_box(xyxy, start_im0, label=f' Frame{frame_id}-{conf:.2f}', color=colors(0, True), line_thickness=2,
                         kpt_label=True, kpts=det[_index, 6:], steps=3, orig_shape=im0s.shape[:2])
            # check_imgs[pos].append(start_im0)
            if not check_imgs[pos]:
                check_imgs[pos]=[start_im0]       # 只取满足条件的第1帧
        else:
            lose_frames[pos].append(frame_id)

    return check_imgs


def recover_det(i, det, img, im0, im0s):
    """
    将onnx的输出det 恢复到原图，以及构造跟踪需要的results结构
    :param i:
    :param det:
    :param img:
    :param im0:
    :param im0s:
    :return:
    """
    results = []
    det[:, :4] = scale_coords(img.shape[2:], det[:, :4], im0.shape, kpt_label=False).round()
    det[:, 6:] = scale_coords(img.shape[2:], det[:, 6:], im0.shape, kpt_label=True, step=3).round()
    pred_kpts = det[:, 6:].view(len(det), *[17, 3])  # ref_skpt = det[:, 6:].clone()
    img_path = None
    results.append(Results(im0s, path=img_path, names={0: 'person'}, boxes=det[:, :6], keypoints=pred_kpts))
    det_track = results[i].boxes.cpu().numpy()

    return det_track, results


def recover_det_haunch(i, det, img, im0, im0s):
    """
    跟踪人体胯部中点，外扩200*200的框作为 跟踪框{C++实验得出的方案，适用于立定跳远1080p画面}
    将onnx的输出det 恢复到原图，以及构造跟踪需要的results结构
    :param i:
    :param det:
    :param img:
    :param im0:
    :param im0s:
    :return:
    """
    results = []
    det[:, :4] = scale_coords(img.shape[2:], det[:, :4], im0.shape, kpt_label=False).round()
    det[:, 6:] = scale_coords(img.shape[2:], det[:, 6:], im0.shape, kpt_label=True, step=3).round()
    pred_kpts = det[:, 6:].view(len(det), *[17, 3])  # ref_skpt = det[:, 6:].clone()

    # 获取胯部中点
    haunch = np.zeros_like(det[:, :6])
    for r in range(len(det)):
        Xc, Yc = det[r, 6:][[33, 36]].mean(), det[r, 6:][[34, 37]].mean()  # 第帧 胯部中点
        haunch[r, :4] = [Xc - 100, Yc - 100, Xc + 100, Yc + 100]  # 替换前4列
    haunch[:, 4:] = det[:, 4:6]  # 保持后2列不变

    img_path = None
    # results.append(Results(im0s, path=img_path, names={0: 'person'}, boxes=det[:, :6], keypoints=pred_kpts))
    results.append(Results(im0s, path=img_path, names={0: 'person'}, boxes=haunch, keypoints=pred_kpts))
    det_track = results[i].boxes.cpu().numpy()

    return det_track, results


def is_point_inbox(point, left_top, right_down):
    # 确定检测的矩形框边界
    left, right = min(left_top[0], right_down[0]), max(left_top[0], right_down[0])
    bottom, top = min(left_top[1], right_down[1]), max(left_top[1], right_down[1])

    # 判断点point是否在矩形内
    if left <= point[0] <=right and bottom <= point[1] <= top:
        return True

    return False


def is_point_inCircle(point, Zero, axis_Pt):
    """
    point: 待检测的点
    Zero: 圆心
    axis_Pt: 圆上的1点
    """
    # 计算圆的半径
    r = math.sqrt((axis_Pt[0] - Zero[0])**2 + (axis_Pt[1]-Zero[1])**2)

    # 计算Point到圆心的距离
    d = math.sqrt((point[0]-Zero[0])**2 + (point[1]-Zero[1])**2)

    # 判断point 是否在圆内
    return d < r


def triangle_area(A, B, C):
    """计算三角形ABC的面积"""
    x1, y1 = A
    x2, y2 = B
    x3, y3 = C
    return abs(x1*(y2-y3) + x2*(y3-y1) + x3*(y1-y2)) / 2.0


def is_point_inRtriangle(A, B, C, P):
    """判断点P 是否在直角△ABC内, C为直角顶点"""
    # area ABC
    area_ABC = triangle_area(A, B, C)
    # area ABP
    area_ABP = triangle_area(A, B, P)
    # area BCP
    area_BCP = triangle_area(B, C, P)
    # area CAP
    area_CAP = triangle_area(C, A, P)
    # if 3个小三角的面积之和 <= 大△, 则P在ABC内
    return area_ABP + area_BCP + area_CAP <= area_ABC



if __name__ == '__main__':
    #  0

    data_cls = read_config(f"Config/baseline.yml")  # 读取所有配置内容, 如无必要，无需修改主函数

    # 获取 符合逻辑的关键帧idx和骨骼点
    data_dict = param_videos(vid_pth=data_cls.videos_pth, model_info=data_cls.model_info,
                             mark_info=data_cls.mark_info)

    # 存储信息
    with open(f'{data_cls.videos_pth}/data_info.pkl', 'wb') as f:
        pickle.dump(data_dict, f)

    # 测试
    # if frame_id == 75:
    #     print(1)
    #     plot_one_box(det[:, :4].squeeze(), im0s, label=f'person {det[:, 4]}',
    #                  color=colors(int(det[:, 5]), True), line_thickness=2, kpt_label=False)
    #     plot_skeleton_kpts(im0s, kpts=det[:, 6:].squeeze(), steps=3, orig_shape=im0s.shape[:2],
    #                        kpt_num=17)  # 绘制姿态点
    #     from PIL import Image
    #
    #     image1 = Image.fromarray(im0s, 'RGB')
    #     image1.save('out.png')

    # 画图测试异常
    # for _index in range(len(det)):
    #     plot_one_box(det[_index, :4].squeeze(), im0s, label=f'person {det[_index, 4]}',
    #                  color=colors(int(det[_index, 5]), True), line_thickness=2, kpt_label=False)
    #     plot_skeleton_kpts(im0s, kpts=det[_index, 6:].squeeze(), steps=3, orig_shape=im0s.shape[:2],
    #                        kpt_num=17)  # 绘制姿态点
    # image1 = Image.fromarray(im0s, 'RGB')
    # image1.save(f'test_demo/outangle{angle_val}_{frame_id}.png')

    # 逻辑画图
    # obstruct_frames.append(frame_id)
    # if saved_img:
    #     plot_one_box(det[_index, :4].squeeze(), im0s, label=f'person {det[_index, 4]}',
    #                  color=colors(int(det[_index, 5]), True), line_thickness=2, kpt_label=False)
    #     plot_skeleton_kpts(im0s, kpts=det[idx, 6:].squeeze(), steps=3, orig_shape=im0s.shape[:2], kpt_num=17)  # 绘制姿态点
    #     image1 = Image.fromarray(im0s, 'RGB')
    #     image1.save(f'test_demo/{Path(path).stem}_{angle_val}_{frame_id}.png')
    # saved_img = False               # 仅保存1次

    # 盲区采用圆心的策略
    """
                    main_skpt = det[_index, 6:]             # 主体的骨骼点
                # 圆心: 距离摄像头最近的胯点， 圆上1点: 耳中心
                Zero_center, head_centre = (min(main_skpt[33], main_skpt[36]), max(main_skpt[34], main_skpt[37])), \
                                         (main_skpt[[9,12]].mean(), main_skpt[[10,13]].mean())         # 圆上1点
                for idx in range(len(det)):
                    if idx == _index:
                        continue
                    # 判断point点(腕或膝) 是否在定义的 圆内 Zero, axis_Pt
                    wrist_l, wrist_r = is_point_inCircle(point=det[idx, 6:][[27,28]], Zero=Zero_center, axis_Pt=head_centre), \
                                       is_point_inCircle(point=det[idx, 6:][[30,31]], Zero=Zero_center, axis_Pt=head_centre)        # 腰
                    knee_l, knee_r = is_point_inCircle(point=det[idx, 6:][[39,40]], Zero=Zero_center, axis_Pt=head_centre), \
                                       is_point_inCircle(point=det[idx, 6:][[42,43]], Zero=Zero_center, axis_Pt=head_centre)        # 膝
                    if wrist_l or wrist_r:
                        if knee_l or knee_r:
                            data_dict[path]['obstruct_frames'].append(frame_id)
    """
