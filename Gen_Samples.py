# -*-coding:utf-8-*-
"""
created by @Moss 20240416
# Step-2
读取生成的 data_info.pkl 有效帧等信息，生成出需要的样本及视频标签；
"""
import copy
import os
import pickle
import torch
import cv2
import numpy as np
from pathlib import Path

import yaml
import json
import logging
from tqdm import tqdm

from utils.general import LoadImages, plot_sitUp_status
from Config.ConfigDict import ConfigDict
from utils.logics import find_seqParts, find_fakeParts,read_markInfo, \
    find_sitReach_fakeParts, find_sitUp_fakeParts,\
    read_markInfo_standjump,\
    compare_inter_max_EndCam
from utils.plots import colors, plot_one_box,plot_skeleton_kpts

from Config.statistics.Mod_timer import mod_timer
from Config.statistics.Mod_logger import init_logger
logger = init_logger(log_dir='./logs', log_level=logging.INFO)      # logging.INFO, WARNING...

from PIL import Image


def read_vidConfig(config_pth: str):
    with open(file=config_pth, mode='r', encoding='utf-8') as f:
        data = yaml.safe_load(f)
        data_cls = ConfigDict(data)  # 参数变量转成可调用的方法

    # 读取配置文件——标定区域
    with open(file=Path(data_cls.mark_file).absolute(), mode='r', encoding='utf-8') as jf:
        mark_info = json.load(jf)
    data_cls['mark_info'] = mark_info       # 写入data_cls

    assert Path(f'{data_cls.videos_pth}/data_info.pkl').exists(), f"@Moss: No file in {data_cls.videos_pth}/data_info.pkl， Please <EMAIL>"
    with open(f'{data_cls.videos_pth}/data_info.pkl', 'rb') as dt:
        vid_info = pickle.load(dt)

    data_cls['vid_info'] = vid_info         # 写入大类

    return data_cls


# ------------------------------------以下是 根据存储的关键点，生成对应视频的pkl样本----------------------------------------------------------------
def gen_samples(vid_info, total_frames, interval_frames, sample_rate) ->dict:
    """
    单人实心球 误触发 根据存储的关键点，汇总并生成需要的样本
    :param interval_frames:
    :param total_frames:
    :param sample_rate:
    :param vid_info:
    :return:
    """

    for path, data_info in vid_info.items():
        vid_info[path]['video_name'] = Path(path).name
        vid_info[path]['total_frames'] = total_frames           # 采集的总帧数
        vid_info[path]['interval_frames'] = interval_frames           # 最小采集间隔

        status_list = data_info['status']
        seq_parts = find_seqParts(status_list, total_frames, interval_frames)       # 划分有效帧片段
        vid_info[path]['seq_parts'] = seq_parts            # 添加字段: 待采样的备选序列
        # 间隔抽样
        idx_samples = [a_part[:: sample_rate] for a_part in seq_parts]
        vid_info[path]['idx_samples'] = idx_samples      # 添加字段: 采样后的样本序列下标

        # 生成 抽样后的关键点字段 (25, 57)
        skpt_samples, skpts = [np.empty((len(x), 57)) for x in idx_samples], vid_info[path]['skpt']         # 这里创建的是 空的数据位 len(x)容错每个样本不定长
        for id, idx_aSample in enumerate(idx_samples):
            for i, idx in enumerate(idx_aSample):
                skpt_samples[id][i] = skpts[idx]
        vid_info[path]['skpt_samples'] = skpt_samples

        # 同时保存一份未抽样的原始点序列 (75, 57)
        ori_samples, skpts = [np.empty((len(x), 57)) for x in seq_parts], vid_info[path]['skpt']         # 这里创建的是 空的数据位 len(x)容错每个样本不定长
        for id, idx_aSample in enumerate(seq_parts):
            for i, idx in enumerate(idx_aSample):
                ori_samples[id][i] = skpts[idx]
        vid_info[path]['ori_samples'] = ori_samples


    return vid_info



def gen_fake_samples(vid_info, len_sample, act_frames) ->dict:
    """
     立定跳远场景理解(违规检测)： 根据存储的关键点，汇总并生成需要的样本
     1个视频 是1个样本, 定义样本总长度为75帧
    :param len_sample:
    :param act_frames:
    :param vid_info:
    :return:
    """

    for path, data_info in vid_info.items():
        vid_info[path]['video_name'] = Path(path).name
        vid_info[path]['act_frames'] = act_frames           # 动作帧 数量
        vid_info[path]['len_sample'] = len_sample           # 样本总帧数

        status_list = data_info['status']
        # ============================逻辑 划分有效帧片段============================================================
        seq_parts = find_fakeParts(status_list, len_sample, act_frames)

        vid_info[path]['seq_parts'] = seq_parts                     # 添加字段: 待采样的备选序列
        vid_info[path]['total_frames'] = len(status_list)                 # 添加字段：视频原始帧数  备用

        # 生成 抽样后的关键点字段 (75, 51)
        skpts = vid_info[path]['skpt']         # 这里创建的是 空的数据位 len(x)容错每个样本不定长
        skpt_samples = [skpts[idx] for idx in seq_parts]        # 根据索引生成骨骼序列 数据
        vid_info[path]['skpt_samples'] = skpt_samples               # 添加骨骼数据字段

        # tmp_list = []
        # for x in [-1, 0, 1, 2]:
        #     tmp_list.append(len([s for s in status_list if s == x]))
        # tmp_list.append(Path(path).name)
        # print(tmp_list)
        # continue

    return vid_info



def gen_sitReach_suspects_samples(vid_info, len_min) ->dict:
    """
     坐位体前屈-场景理解(违规检测)： 根据存储的关键点，汇总并生成需要的样本
     1个视频 是1个样本, 定义样本总长度为 不定长帧
    :param len_min:
    :param vid_info:
    :return:
    """
    vid_info_ori = copy.deepcopy(vid_info)
    for path, data_info in vid_info_ori.items():
        vid_info[path]['video_name'] = Path(path).name
        vid_info[path]['len_min'] = len_min           # 样本总帧数

        status_list = data_info['status']
        if len(status_list) < 10:
            del vid_info[path]
            continue
        # ============================逻辑 划分有效帧片段============================================================
        seq_parts = find_sitReach_fakeParts(status_list, len_min=len_min)
        # ---------------------------------------------------------------------------------------------
        vid_info[path]['seq_parts'] = seq_parts                     # 添加字段: 待采样的备选序列
        vid_info[path]['total_frames'] = len(status_list)
        # 添加字段：视频原始帧数  备用

        # 生成 抽样后的关键点字段 (75, 51)
        skpts = vid_info[path]['skpt']         # 这里创建的是 空的数据位 len(x)容错每个样本不定长
        # skpt_samples = [skpts[idx] for idx in seq_parts]  # 根据索引生成骨骼序列 数据
        # vid_info[path]['skpt_samples'] = skpt_samples  # 添加骨骼数据字段
        try:
            skpt_samples = [skpts[idx] for idx in seq_parts]        # 根据索引生成骨骼序列 数据
            vid_info[path]['skpt_samples'] = skpt_samples           # 添加骨骼数据字段
        except Exception as e:
            print(e)
            with open('ErrVideos.txt', 'a') as fe:
                json.dump(f'{path}', fe, indent=4)

    return vid_info


def gen_sitUp_suspects_samples(vid_info, len_sample, plot_waves=True) ->dict:
    """
     仰卧起坐-场景理解(违规检测)： 根据存储的关键点，汇总并生成需要的样本
     1个视频的1个逻辑clip 是1个样本, 定义样本总长度为 不定长帧
    :param plot_waves:  是否需要画图
    :param len_sample:
    :param vid_info:
    :return:
    """

    for path, data_info in vid_info.items():
        vid_info[path]['video_name'] = Path(path).name
        vid_info[path]['len_sample'] = len_sample           # 样本总帧数

        status_list = data_info['status']

        if plot_waves:      # 绘制和保存 状态列表波形轨迹
            plot_sitUp_status(status_list, save_path=Path(path).with_suffix('.png').__str__())
        # ============================逻辑 划分有效帧片段============================================================
        seq_parts = find_sitUp_fakeParts(status_list, peak_valley=[115, 95], len_sample=len_sample)
        # ---------------------------------------------------------------------------------------------
        vid_info[path]['seq_parts'] = seq_parts                     # 添加字段: 待采样的备选序列
        vid_info[path]['total_frames'] = len(status_list)                 # 添加字段：视频原始帧数  备用

        # 生成 抽样后的关键点字段 (len, [57, [], []])
        skpt_samples, skpts = [[] for _ in seq_parts], vid_info[path]['skpt']         # 这里创建的是 空的数据位
        for id, idx_aSample in enumerate(seq_parts):
            for i, idx in enumerate(idx_aSample):
                skpt_samples[id].append(skpts[idx])
        vid_info[path]['skpt_samples'] = skpt_samples                   # 所有样本保存

    return vid_info


def gen_sitUp_fusion_samples(vid_info, len_sample, plot_waves=True) ->dict:
    """
     仰卧起坐-场景理解(违规检测) PoseRGB： 根据存储的关键点，汇总并生成需要的样本
     1个视频的1个逻辑clip 是1个样本, 定义样本总长度为 不定长帧
    :param plot_waves:  是否需要画图
    :param len_sample:
    :param vid_info:
    :return:
    """

    for path, data_info in vid_info.items():
        vid_info[path]['video_name'] = Path(path).name
        vid_info[path]['len_sample'] = len_sample           # 样本总帧数

        status_list = data_info['status']

        if plot_waves:      # 绘制和保存 状态列表波形轨迹
            plot_sitUp_status(status_list, save_path=Path(path).with_suffix('.png').__str__())
        # ============================逻辑 划分有效帧片段============================================================
        seq_parts = find_sitUp_fakeParts(status_list, peak_valley=[115, 95], len_sample=len_sample)
        # ---------------------------------------------------------------------------------------------
        vid_info[path]['seq_parts'] = seq_parts                     # 添加字段: 待采样的备选序列
        vid_info[path]['total_frames'] = len(status_list)                 # 添加字段：视频原始帧数  备用

        # 生成 抽样后的关键点字段 (len, [57, [], []])
        skpt_samples, skpts = [[] for _ in seq_parts], vid_info[path]['skpt']         # 这里创建的是 空的数据位
        for id, idx_aSample in enumerate(seq_parts):
            for i, idx in enumerate(idx_aSample):
                skpt_samples[id].append(skpts[idx])
        vid_info[path]['skpt_samples'] = skpt_samples                   # 所有样本保存

    return vid_info


# --------------------------------------- 基础服务 -------------------------------------------------------------
def deal_videoLabs(sample_info:dict, video_pth:str, model_info:dict, mark_info:dict, proj_name:str, save_oriImg=False, fall_foot=None) -> list:
    """
    生成pkl数据样本 和 视频标签
    :param save_oriImg: 默认False 是否保存原始图像
    :param proj_name:  项目名称-
    :param sample_info: 样本信息
    :param model_info: 推理模型信息
    :param video_pth: 单个视频的路径
    :param mark_info: 标定信息
    :param fall_foot: 脚检测落地帧
    :return:
    """
    dataset = LoadImages([video_pth], img_size=model_info['infer_size'], vid_width=mark_info.get('vid_width', 1920))      # 单个视频
    # dataset = LoadImages([video_pth], img_size=model_info['infer_size'], vid_width=mark_info.get('vid_width', 1920), mirror=mark_info.get('mirror'))      # 单个视频
    det_one = sample_info['skpt']
    if proj_name == 'soild_ball':
        pt_xyxy = read_markInfo(mark_info)  # 左上点，右下点 标定
    elif proj_name in ['stand_jump', 'fake_stand_jump']:
        pt_xyxy = read_markInfo_standjump(mark_info)  # 左上点，右下点 标定  || 新方案自动标定
        # 获取 落地脚
        status = sample_info.get('status')
        fall_foot = sample_info.get('obstruct_frames')[0]
        fall_frame = next((i for i,x in enumerate(status) if x in [2,3,4]), -1)         # 首个2状态
        start_frame = next((i for i,x in enumerate(status) if x==1), 0)         # 首个1状态
        # 获取落地轨迹 脚踝点
        track_detL = [(int(aFrame[6:][48]), int(aFrame[6:][49])) for aFrame in det_one[:fall_frame] if aFrame is not None]
        track_detR = [(int(aFrame[6:][45]), int(aFrame[6:][46])) for aFrame in det_one[:fall_frame] if aFrame is not None]

        pt_xyxy = (pt_xyxy, fall_foot, (start_frame, fall_frame), [track_detL, track_detR])


    elif proj_name == 'fake_sit_reach':
        pt_xyxy = tuple(mark_info['xyxy_board'])
        way = mark_info['way']
        scale_val = sample_info['status']           # 刻度值
    elif proj_name == 'fake_sit_up':
        pt_xyxy = (0,0,0,0)
        angle_val = sample_info['status']           # 角度值
    elif proj_name == 'fusion_sit_up':
        pt_xyxy = (0,0,0,0)
        angle_val = sample_info['status']           # 角度值
        peak_parts = sum(sample_info['peak_parts'], [])           # 原图下标

    else:
        raise ValueError(f"@Moss: 标定信息未指定！")

    imgs_seq = []
    ori_img0s_seq = [] if save_oriImg else None
    for path, img, im0s, vid_id, frame_id, _ in tqdm(dataset):
        ori_img = copy.deepcopy(im0s)
        ori_img0s_seq.append(ori_img) if save_oriImg else None  # 是否保存原始数据

        if proj_name in ['fake_sit_reach','fake_sit_up', 'fusion_sit_up']:           # 多人项目需要画多个人体
            if len(det_one) >= frame_id and det_one[frame_id-1] is not None:
                fx = det_one[frame_id - 1]         # 排除 IndexError: list index out of range
            else:
                imgs_seq.append(None)  # 存储         # len(det_one) < abs(frame_id-1)
                continue
            if proj_name == 'fake_sit_reach':
                plot_blindcolor = (0,0,255) if scale_val[frame_id - 1] > 0 else (255,255,255)       # 根据刻度 判断框的颜色
                black_img = plot_multiperson_vidImgs_reach(im0s, det_one, frame_id, pt_xyxy, mirror=way, plot_blindcolor=plot_blindcolor)          # 生成vid标签
            elif proj_name == 'fake_sit_up':
                plot_blindcolor = (0, 0, 255) if angle_val[frame_id - 1] >= 115 else (255, 255, 255)   # 根据角度 判断框的颜色
                black_img = plot_multiperson_vidImgs_sitUp(im0s, det_one, frame_id, pt_xyxy, mirror=mark_info.get('mirror'), plot_blindcolor=plot_blindcolor)  # 生成vid标签
            elif proj_name == 'fusion_sit_up':
                plot_blindcolor = (0, 0, 255) if angle_val[frame_id - 1] >= 115 else (255, 255, 255)   # 根据角度 判断框的颜色
                black_img = plot_multiperson_vidFusion_sitUp(im0s, det_one, frame_id, pt_xyxy, plot_blindcolor=plot_blindcolor)  # 生成vid标签
                if frame_id - 1 in peak_parts:
                    black_img = black_img, ori_img
                else:
                    black_img = black_img, None

        elif proj_name in ['soild_ball', 'stand_jump', 'fake_stand_jump']:
            if frame_id - 1 >= len(det_one):
                imgs_seq.append(None)
                continue
            if det_one[frame_id - 1] is None or len(det_one[frame_id - 1]) == 0:
                imgs_seq.append(None)
                continue
            # cv2.imwrite(f'tst-{frame_id}.jpg', im0s)        # 测试代码
            black_img = plot_vidImgs(im0s, det_one, frame_id, pt_xyxy, mirror=mark_info.get('mirror'))          # 生成vid标签

        imgs_seq.append(black_img)       # 存储

    return imgs_seq if not save_oriImg else [imgs_seq, ori_img0s_seq]


def plot_vidImgs(im0s, det_one,frame_id, pt_xyxy, mirror=None):
    """
    绘制单张图像作为视频标签的一部分
    :param mirror: 该视频是否需要镜像
    :param im0s: 单视频原始图像
    :param det_one: 单视频唯一检测姿态点
    :param frame_id: 当前图像对应的帧
    :param pt_xyxy: 标定xyxy
    :return:
    """
    if not isinstance(pt_xyxy[-1], list):
        pt_xl, pt_yl, pt_xr, pt_yr, up_down_ptList = pt_xyxy  # 解包 标定 上起点，下起点
    else:
        pt_xys, fall_foot, start_fall_frames, track_det = pt_xyxy
        pt_xl, pt_yl, pt_xr, pt_yr, up_down_ptList = pt_xys
        start_frame, fall_frame = start_fall_frames
        track_detL, track_detR = track_det


    # 在图像左上角1/x画一个黑色背景的点
    baseShape = (int(im0s.shape[0] / 2), int(im0s.shape[1] / 2))  # 1080/2, 1902/2
    black_img = np.zeros((1080, 1920, 3), dtype=np.uint8)
    skl_img = copy.deepcopy(black_img)

    Action_img = copy.deepcopy(black_img)
    if up_down_ptList and frame_id <= fall_frame:
        plot_skeleton_kpts(Action_img, kpts=det_one[fall_frame][6:], steps=3, orig_shape=skl_img.shape[:2], kpt_num=17)  # 在黑白图片上 绘制姿态点
        center_frame = start_frame + (fall_frame - start_frame) // 2
        if det_one[center_frame] is not None:
            plot_skeleton_kpts(Action_img, kpts=det_one[center_frame][6:], steps=3, orig_shape=skl_img.shape[:2], kpt_num=17)  # 在黑白图片上 绘制姿态点
        plot_skeleton_kpts(Action_img, kpts=det_one[start_frame][6:], steps=3, orig_shape=skl_img.shape[:2], kpt_num=17)  # 在黑白图片上 绘制姿态点
        Action_img = cv2.resize(Action_img, (baseShape[1], baseShape[0]))
        black_img[baseShape[0]:, 0:baseShape[1]] = Action_img  # 贴图左下角

    # 在原始图像上画人体和标定
    plot_one_box(det_one[frame_id - 1][:4], im0s, label=f'person {det_one[frame_id - 1][4]:.2f}',
                 color=colors(int(det_one[frame_id - 1][4]), True), line_thickness=2, kpt_label=False)
    plot_skeleton_kpts(im0s, kpts=det_one[frame_id - 1][6:], steps=3, orig_shape=im0s.shape[:2], kpt_num=17)  # 绘制姿态点
    cv2.rectangle(im0s, (pt_xl, pt_yl), (pt_xr, pt_yr), color=(248, 248, 255), thickness=2 * 1 // 3, lineType=cv2.LINE_AA)  # 绘制 配置box
    if up_down_ptList:          # 绘制 刻度区域, 落地帧脚
        if frame_id <= fall_frame:
            cv2.rectangle(im0s, (int(fall_foot[0]), int(fall_foot[1])), (int(fall_foot[2]), int(fall_foot[3])),
                          color=(0, 0, 255), thickness=3, lineType=cv2.LINE_AA)                     # 绘制 配置box
            # 绘制落地轨迹
            for idx in range(len(track_detL)-1):
                cv2.line(im0s, track_detL[idx], track_detL[idx+1], (248, 248, 255), 3)
                cv2.line(im0s, track_detR[idx], track_detR[idx+1], (0, 0, 255), 2)


        pts = np.array([[up_down_ptList[0], up_down_ptList[1]],[up_down_ptList[6][0], up_down_ptList[6][1]],
                        [up_down_ptList[7][0], up_down_ptList[7][1]], [up_down_ptList[2], up_down_ptList[3]]], np.int32).reshape((-1,1,2))
        cv2.polylines(im0s, [pts], isClosed=True, color=(0, 255, 0), thickness=2, lineType=cv2.LINE_AA)     # 落地区域

    plot_skeleton_kpts(skl_img, kpts=det_one[frame_id - 1][6:], steps=3, orig_shape=skl_img.shape[:2], kpt_num=17)  # 在黑白图片上 绘制姿态点
    cv2.rectangle(skl_img, (pt_xl, pt_yl), (pt_xr, pt_yr), color=(248, 248, 255), thickness=2 * 1 // 3, lineType=cv2.LINE_AA)  # 在黑白图片上 绘制 配置准备区box

    im0s = cv2.resize(im0s, (baseShape[1], baseShape[0]))
    skl_img = cv2.resize(skl_img, (baseShape[1], baseShape[0]))

    black_img[0:baseShape[0], 0:baseShape[1]] = im0s  # 贴图左上角
    black_img[baseShape[0]:, baseShape[1]:] = skl_img  # 贴图右下角

    return black_img


def plot_multiperson_vidImgs(im0s, det_multi,frame_id, pt_xyxy, mirror=None):
    """
    'fake_sit_up' 项目， 绘制单张图像作为视频标签的一部分
    :param mirror: 该视频是否需要镜像
    :param im0s: 单视频原始图像
    :param det_multi: 单视频唯一检测姿态点
    :param frame_id: 当前图像对应的帧
    :param pt_xyxy: 标定xyxy
    :return:
    """
    pt_xl, pt_yl, pt_xr, pt_yr = pt_xyxy        # 解包 标定
    pt_xl, pt_yl, pt_xr, pt_yr = int(pt_xl), int(pt_yl), int(pt_xr), int(pt_yr)
    # 在图像左上角1/x画一个黑色背景的点
    baseShape = (int(im0s.shape[0] / 2), int(im0s.shape[1] / 2))  # 1080/2, 1902/2
    black_img = np.zeros((1080, 1920, 3), dtype=np.uint8)
    skl_img = copy.deepcopy(black_img)
    # 在原始图像上画人体和标定
    for det_one in det_multi[frame_id - 1]:
        if det_one is None:
            continue
        if isinstance(det_one, list):
            det_one = det_one[0]
        plot_one_box(det_one[:4], im0s, label=f'person {det_one[4]:.2f}',
                     color=colors(int(det_one[4]), True), line_thickness=2, kpt_label=False)
        plot_skeleton_kpts(im0s, kpts=det_one[6:], steps=3, orig_shape=im0s.shape[:2], kpt_num=17)  # 绘制姿态点
        plot_skeleton_kpts(skl_img, kpts=det_one[6:], steps=3, orig_shape=skl_img.shape[:2], kpt_num=17)  # 在黑色背景上 绘制姿态点

    cv2.rectangle(im0s, (pt_xl, pt_yl), (pt_xr, pt_yr), color=(248, 248, 255), thickness=2 * 1 // 3, lineType=cv2.LINE_AA)  # 绘制 配置box
    cv2.rectangle(skl_img, (pt_xl, pt_yl), (pt_xr, pt_yr), color=(248, 248, 255), thickness=2 * 1 // 3, lineType=cv2.LINE_AA)  # 在黑色背景上 绘制 配置box

    im0s = cv2.resize(im0s, (baseShape[1], baseShape[0]))
    skl_img = cv2.resize(skl_img, (baseShape[1], baseShape[0]))

    black_img[0:baseShape[0], 0:baseShape[1]] = im0s  # 贴图左上角
    black_img[baseShape[0]:, baseShape[1]:] = skl_img  # 贴图右下角

    return black_img


def plot_multiperson_vidImgs_sitUp(im0s, det_multi,frame_id, pt_xyxy, mirror=None, plot_blindcolor=None):
    """
    'fake_sit_up' 项目， 绘制单张图像作为视频标签的一部分;  绘制盲区-红白框
    :param mirror: 该视频是否需要镜像
    :param im0s: 单视频原始图像
    :param det_multi: 单视频唯一检测姿态点
    :param frame_id: 当前图像对应的帧
    :param pt_xyxy: 标定xyxy
    :return:
    """
    pt_xl, pt_yl, pt_xr, pt_yr = pt_xyxy        # 解包 标定
    pt_xl, pt_yl, pt_xr, pt_yr = int(pt_xl), int(pt_yl), int(pt_xr), int(pt_yr)
    # 在图像左上角1/x画一个黑色背景的点
    baseShape = (int(im0s.shape[0] / 2), int(im0s.shape[1] / 2))  # 1080/2, 1902/2
    black_img = np.zeros((1080, 1920, 3), dtype=np.uint8)
    skl_img = copy.deepcopy(black_img)
    # 在原始图像上画 盲区、  人体和标定
    main_box = det_multi[frame_id - 1][0][:4]
    main_skpt = det_multi[frame_id - 1][0][6:]

    # 框的左上角是主体人体框x， y取 耳点中点+肩点y差值
    box_left, waist_centre = (main_box[0], main_skpt[[10, 13]].mean() - abs(main_skpt[19] - main_skpt[16])), \
                             (main_skpt[[33, 36]].mean() + 0.5 * abs(main_skpt[42] - main_skpt[36]),
                              main_skpt[[34, 37]].mean())

    cv2.rectangle(im0s, (int(box_left[0]), int(box_left[1])), (int(waist_centre[0]), int(waist_centre[1])),
                  color=plot_blindcolor, thickness=5, lineType=cv2.LINE_AA)  # 绘制 配置box


    for det_one in det_multi[frame_id - 1]:
        if det_one is None:
            continue
        if isinstance(det_one, list):
            det_one = det_one[0]
        plot_one_box(det_one[:4], im0s, label=f'person {det_one[4]:.2f}',
                     color=colors(int(det_one[4]), True), line_thickness=2, kpt_label=False)
        plot_skeleton_kpts(im0s, kpts=det_one[6:], steps=3, orig_shape=im0s.shape[:2], kpt_num=17)  # 绘制姿态点
        plot_skeleton_kpts(skl_img, kpts=det_one[6:], steps=3, orig_shape=skl_img.shape[:2], kpt_num=17)  # 在黑色背景上 绘制姿态点

    cv2.rectangle(im0s, (pt_xl, pt_yl), (pt_xr, pt_yr), color=(248, 248, 255), thickness=2 * 1 // 3, lineType=cv2.LINE_AA)  # 绘制 配置box
    cv2.rectangle(skl_img, (pt_xl, pt_yl), (pt_xr, pt_yr), color=(248, 248, 255), thickness=2 * 1 // 3, lineType=cv2.LINE_AA)  # 在黑色背景上 绘制 配置box

    im0s = cv2.resize(im0s, (baseShape[1], baseShape[0]))
    skl_img = cv2.resize(skl_img, (baseShape[1], baseShape[0]))

    black_img[0:baseShape[0], 0:baseShape[1]] = im0s  # 贴图左上角
    black_img[baseShape[0]:, baseShape[1]:] = skl_img  # 贴图右下角

    return black_img


def plot_multiperson_vidFusion_sitUp(im0s, det_multi,frame_id, pt_xyxy, plot_blindcolor=None):
    """
    'fake_sit_up' 项目， 绘制单张图像作为视频标签的一部分;  同时保存原始图片
    :param mirror: 该视频是否需要镜像
    :param im0s: 单视频原始图像
    :param det_multi: 单视频唯一检测姿态点
    :param frame_id: 当前图像对应的帧
    :param pt_xyxy: 标定xyxy
    :return:
    """
    pt_xl, pt_yl, pt_xr, pt_yr = pt_xyxy        # 解包 标定
    pt_xl, pt_yl, pt_xr, pt_yr = int(pt_xl), int(pt_yl), int(pt_xr), int(pt_yr)

    # skl_img = copy.deepcopy(im0s)
    skl_img = im0s
    # 在原始图像上画 盲区、  人体和标定
    main_box = det_multi[frame_id - 1][0][:4]
    main_skpt = det_multi[frame_id - 1][0][6:]

    # 框的左上角是主体人体框x， y取 耳点中点+肩点y差值
    box_left, waist_centre = (main_box[0], main_skpt[[10, 13]].mean() - abs(main_skpt[19] - main_skpt[16])), \
                             (main_skpt[[33, 36]].mean() + 0.5 * abs(main_skpt[42] - main_skpt[36]),
                              main_skpt[[34, 37]].mean())

    cv2.rectangle(skl_img, (int(box_left[0]), int(box_left[1])), (int(waist_centre[0]), int(waist_centre[1])),
                  color=plot_blindcolor, thickness=5, lineType=cv2.LINE_AA)  # 绘制 配置box


    for det_one in det_multi[frame_id - 1]:
        if det_one is None:
            continue
        if isinstance(det_one, list):
            det_one = det_one[0]
        plot_one_box(det_one[:4], skl_img, label=f'person {det_one[4]:.2f}',
                     color=colors(int(det_one[4]), True), line_thickness=2, kpt_label=False)
        plot_skeleton_kpts(skl_img, kpts=det_one[6:], steps=3, orig_shape=skl_img.shape[:2], kpt_num=17)  # 在黑色背景上 绘制姿态点

    cv2.rectangle(skl_img, (pt_xl, pt_yl), (pt_xr, pt_yr), color=(248, 248, 255), thickness=2 * 1 // 3, lineType=cv2.LINE_AA)  # 在黑色背景上 绘制 配置box

    # res_img = cv2.vconcat([skl_img, im0s])
    res_img = skl_img
    return res_img




def plot_multiperson_vidImgs_reach(im0s, det_multi,frame_id, pt_xyxy, mirror='way', plot_blindcolor=None):
    """
    'fake_sit_reach'项目， 绘制单张图像作为视频标签的一部分
    :param mirror: 该视频是否需要镜像
    :param im0s: 单视频原始图像
    :param det_multi: 单视频唯一检测姿态点
    :param frame_id: 当前图像对应的帧
    :param pt_xyxy: 标定xyxy
    :return:
    """
    pt_xl, pt_yl, pt_xr, pt_yr = pt_xyxy        # 解包 标定
    pt_xl, pt_yl, pt_xr, pt_yr = int(pt_xl), int(pt_yl), int(pt_xr), int(pt_yr)
    # 在图像左上角1/x画一个黑色背景的点
    baseShape = (int(im0s.shape[0] / 2), int(im0s.shape[1] / 2))  # 1080/2, 1902/2
    black_img = np.zeros((1080, 1920, 3), dtype=np.uint8)
    skl_img = copy.deepcopy(black_img)

    # 在原始图像上画 盲区、  人体和标定
    main_box = det_multi[frame_id - 1][0][:4]
    main_skpt = det_multi[frame_id - 1][0][6:]
    if mirror == 'R':
        box_left = (main_box[0], main_skpt[[10, 13]].mean() - abs(main_skpt[19] - main_skpt[16]) * 0.70)             # 左上角
        foot_centre = (max(main_skpt[[39, 42]].mean(), main_skpt[[21, 24]].mean()),  main_box[3])                       # 右下角
        B_leftPt = main_box[0] - abs(max(main_skpt[33], main_skpt[36]) - main_box[0]), main_box[3]              # 左下角
        top_right = foot_centre[0], box_left[1]
    else:
        box_left = (min(main_skpt[[39, 42]].mean(), main_skpt[[21, 24]].mean()),
                    main_skpt[[10, 13]].mean() - abs(main_skpt[19] - main_skpt[16]) * 0.70)     # 左上
        B_leftPt = (box_left[0], main_box[3])           # 左下
        top_right = main_box[2], box_left[1]      # 右上
        foot_centre = main_box[2]+abs(max(main_skpt[33], main_skpt[36])- main_box[2]), main_box[3]            # 右下角

    points = np.array([(box_left[0].item(), box_left[1].item()),(top_right[0].item(), top_right[1].item()),
                       (foot_centre[0].item(), foot_centre[1].item()), (B_leftPt[0].item(), B_leftPt[1].item())],
                      dtype=np.int32).reshape((-1,1,2))
    cv2.polylines(im0s, [points], color=plot_blindcolor, isClosed=True, thickness=5)       # red

    for det_one in det_multi[frame_id - 1]:
        if det_one is None:
            continue
        if isinstance(det_one, list):
            det_one = det_one[0]
        plot_one_box(det_one[:4], im0s, label=f'person {det_one[4]:.2f}',
                     color=colors(int(det_one[4]), True), line_thickness=2, kpt_label=False)
        plot_skeleton_kpts(im0s, kpts=det_one[6:], steps=3, orig_shape=im0s.shape[:2], kpt_num=17)  # 绘制姿态点
        plot_skeleton_kpts(skl_img, kpts=det_one[6:], steps=3, orig_shape=skl_img.shape[:2], kpt_num=17)  # 在黑色背景上 绘制姿态点

    # image1 = Image.fromarray(im0s, 'RGB')
    # image1.save(f'test_demo/tst_{frame_id}.png')

    cv2.rectangle(im0s, (pt_xl, pt_yl), (pt_xr, pt_yr), color=(248, 248, 255), thickness=2 * 1 // 3, lineType=cv2.LINE_AA)  # 绘制 配置box
    cv2.rectangle(skl_img, (pt_xl, pt_yl), (pt_xr, pt_yr), color=(248, 248, 255), thickness=2 * 1 // 3, lineType=cv2.LINE_AA)  # 在黑色背景上 绘制 配置box

    im0s = cv2.resize(im0s, (baseShape[1], baseShape[0]))
    skl_img = cv2.resize(skl_img, (baseShape[1], baseShape[0]))

    black_img[0:baseShape[0], 0:baseShape[1]] = im0s  # 贴图左上角
    black_img[baseShape[0]:, baseShape[1]:] = skl_img  # 贴图右下角

    return black_img


def plot_markInfo(marks, im0):
    for mark in marks:
        for point in mark.values():
            cv2.circle(im0, point, radius=5, color=(0,255,0), thickness=-1)     # 绿色标定

    return


# ---------------------------------------- 以下是生成样本和标签-----------------------------------------------------------

def gen_lab_vid(vid_info, data_cls):
    """
    单人实心球 触发项目 生成样本和视频标签
    :param data_cls:  用于获取 样本保存文件夹data_cls.save_dir
    :param vid_info:
    :return: 返回无效视频路径
    """
    save_pth = data_cls.save_dir
    proj_name = data_cls.proj_name

    invalid_vids = []
    for path, sample_info in vid_info.items():
        # 提取基本信息
        vid_stem = Path(sample_info['video_name']).stem
        img_shape = sample_info['img_shape']
        total_frames, interval = sample_info['total_frames'], sample_info['interval_frames']
        seq_parts, idx_samples = sample_info['seq_parts'], sample_info['idx_samples']
        mark_info = data_cls.mark_info

        if len(seq_parts) == 0:
            logger.info(f"@Moss: No valid seq_parts in {path}")     # 针对手工录制的视频,跳过无效视频
            invalid_vids.append(path)
            continue

        logger.info(f"{str(Path(path).name)}: ")        # 当前 有效视频 loading:
        imgs_seq = deal_videoLabs(sample_info, video_pth=path, model_info=data_cls.model_info, mark_info=mark_info, proj_name=proj_name)        # 存储单个视频的姿态点到有效样本的图像上

        save_dir = save_pth + os.sep + Path(path).stem
        Path(save_dir).mkdir(exist_ok=True)
        for id, a_skpts in enumerate(sample_info['skpt_samples']):
            # 生成pkl
            sample_name = vid_stem + f'_total{total_frames}_loop{idx_samples[id][0]}_{idx_samples[id][-1]}.pkl'

            # TODO 加入预处理
            det_skpts = a_skpts[:, 6:]
            if mark_info.get('mirror') is not None:         # 'True' 镜像处理
                det_skpts[:, ::3] = mark_info.get('vid_width') - det_skpts[:, ::3]
            one_skpts_x, one_skpts_y = a_skpts[0, 6:][[15, 18]].mean(), a_skpts[0, 6:][[16, 19]].mean()        # 获取 首帧51个关键点的肩点中心
            det_skpts[:, ::3] -= one_skpts_x            # 以肩点中心为圆心的切换
            det_skpts[:, 1::3] -= one_skpts_y

            a_sample = {'img_shape':img_shape, 'vid_pth':path, 'total_frames':total_frames, 'interval':interval,
                        'sample_name': sample_name, 'pred_skpts':det_skpts, 'ori_all': sample_info['ori_samples'][id],
                        'seq_vid': seq_parts[id], 'idx_sample':idx_samples[id], 'ori_skpts': a_skpts}
            # 存储 姿态点序列 样本信息
            with open(save_dir+os.sep+sample_name, 'wb') as fs:
                pickle.dump(a_sample, fs)           # saved pkl

            # 生成vids & 存储
            lab_videoName = Path(sample_name).with_suffix('.avi').__str__()
            vid_writer = cv2.VideoWriter(save_dir+os.sep+lab_videoName, cv2.VideoWriter_fourcc(*'XVID'), 25, (1920, 1080))  # 创建保存视频的对象
            with mod_timer():
                for idf in a_sample['idx_sample']:
                    vid_writer.write(imgs_seq[idf])  # saving avi

            del sample_name, lab_videoName, vid_writer, a_sample,

    return invalid_vids



def gen_lab_fake_vid(vid_info, data_cls):
    """
    立定跳远 作弊检测：生成样本和视频标签
    :param data_cls:  用于获取 样本保存文件夹data_cls.save_dir
    :param vid_info:
    :return: 返回无效视频路径
    """
    save_pth = data_cls.save_dir
    proj_name = data_cls.proj_name

    invalid_vids = []
    for path, sample_info in vid_info.items():
        # 提取基本信息
        vid_stem = Path(sample_info['video_name']).stem
        img_shape = sample_info['img_shape']
        total_frames = sample_info['total_frames']
        seq_parts = sample_info['seq_parts']
        skpt_samples = sample_info['skpt_samples']
        status = sample_info['status']
        # 获取 落地脚
        fall_foot = sample_info.get('obstruct_frames')

        mark_info = data_cls.mark_info

        if len(seq_parts) == 0 or \
                any(map(lambda x: x is None or (hasattr(x, '__len__') and len(x) ==0), skpt_samples)):
            logger.info(f"@Moss: No-valid seq_parts or skpt in {path}")     # ,跳过无效视频
            invalid_vids.append(path)
            continue
        if not len(fall_foot):  # 没有落地帧，或落地帧不满足条件
            logger.info(f"@Moss: No Fall_foot frame or fall-Foot Moved in {path}")     # ,跳过无效视频
            invalid_vids.append(path)
            continue

        logger.info(f"{str(Path(path).name)}: ")        # 当前 有效视频 loading:
        # StandJump存储单个视频的姿态点到有效样本的图像上
        imgs_seq = deal_videoLabs(sample_info, video_pth=path, model_info=data_cls.model_info, mark_info=mark_info, proj_name=proj_name)

        save_dir = save_pth
        if 2 in status:
            save_dir = str(Path(save_dir) / 'small_jump')
        elif 3 in status:
            save_dir = str(Path(save_dir) / 'middle_jump')
        elif 4 in status:
            save_dir = str(Path(save_dir) / 'Big_jump')
        Path(save_dir).mkdir(exist_ok=True, parents=True)
        # 1个视频对应1个样本
        sample_name = vid_stem + f'_total{total_frames}_track{len(seq_parts)}.pkl'
        a_skpts = torch.stack(sample_info['skpt_samples'])

        # 预处理
        det_skpts = a_skpts[:, 6:]  # 取出所有骨骼点
        if mark_info.get('mirror') is None:  # 凡是从左边跳的画面都有mirror标签，有标签的不作处理；
            # 以下是没有标签的，需要镜像
            det_skpts[:, ::3] = mark_info.get('vid_width') - det_skpts[:, ::3]  # 仅在x方向对称

        a_sample = {'img_shape': img_shape, 'vid_pth': path, 'total_frames': total_frames,
                    'sample_name': sample_name, 'pred_skpts': det_skpts,
                    'seq_vid': seq_parts}
        # 存储 姿态点序列 样本信息
        with open(save_dir + os.sep + sample_name, 'wb') as fs:
            pickle.dump(a_sample, fs)  # saved pkl

        # 生成vids & 存储
        lab_videoName = Path(sample_name).with_suffix('.avi').__str__()
        vid_writer = cv2.VideoWriter(save_dir + os.sep + lab_videoName, cv2.VideoWriter_fourcc(*'XVID'), 25,
                                     (1920, 1080))  # 创建保存视频的对象
        with mod_timer():
            for idf in a_sample['seq_vid']:
                vid_writer.write(imgs_seq[idf])  # saving avi
        vid_writer.release()


    return invalid_vids



def gen_sitReach_lab_fake_vid(vid_info, data_cls):
    """
    坐位体前屈 作弊检测：生成样本和视频标签
    :param data_cls:  用于获取 样本保存文件夹data_cls.save_dir
    :param vid_info:
    :return: 返回无效视频路径
    """
    save_pth = data_cls.save_dir
    proj_name = data_cls.proj_name

    invalid_vids = []
    for path, sample_info in vid_info.items():
        # 提取基本信息
        vid_stem = Path(sample_info['video_name']).stem
        img_shape = sample_info['img_shape']
        status = sample_info['status']
        total_frames = sample_info['total_frames']
        seq_parts = sample_info['seq_parts']
        skpt_samples = sample_info['skpt_samples']
        board_info = sample_info['board_info']

        if len(seq_parts) == 0 or any(map(lambda x: x is None or (hasattr(x, '__len__') and len(x) ==0), skpt_samples)):
            logger.info(f"@Moss: No valid seq_parts or skpt in {path}")     # 针对手工录制的视频,跳过无效视频
            invalid_vids.append(path)
            continue

        logger.info(f"{str(Path(path).name)}: ")        # 当前 有效视频 loading:
        imgs_seq = deal_videoLabs(sample_info, video_pth=path, model_info=data_cls.model_info, mark_info=board_info, proj_name=proj_name)        # 存储单个视频的姿态点到有效样本的图像上

        save_dir = save_pth
        Path(save_dir).mkdir(exist_ok=True)

        # 盲区保存逻辑
        fade_zone = set(sample_info['obstruct_frames'])  # 盲区 帧列表
        fade_frames = list(set(seq_parts).intersection(fade_zone))
        fade_frames = sorted(fade_frames)
        len_fade = len(fade_frames)
        if len_fade:  # 当前样本中存在盲区帧
            sample_name = vid_stem + f'_fade_zone_len{len_fade}_start{fade_frames[0]}_end{fade_frames[-1]}.jpg'
            if not (hasattr(imgs_seq[fade_frames[0]], 'shape') and hasattr(imgs_seq[fade_frames[-1]], 'shape')):
                continue
            first_endImg = np.concatenate((imgs_seq[fade_frames[0]], imgs_seq[fade_frames[-1]]), axis=0)
            _FadeImg = Image.fromarray(first_endImg)
            _FadeImg.save(f"{save_dir + os.sep + sample_name}")
            print('fade—frames:', fade_frames)
            continue


        # 1个视频对应1个样本
        len_seq_part = len(seq_parts)
        sample_name = vid_stem + f'_len{len_seq_part}_start{seq_parts[0]}_{seq_parts[-1]}.pkl'
        a_sample = {'img_shape': img_shape, 'vid_pth': path, 'total_frames': total_frames,
                    'sample_name': sample_name, 'status': status,
                    'start': seq_parts[0], 'end': seq_parts[-1], 'len_status': len_seq_part,
                    'seq_vid': seq_parts, 'pred_skpts': skpt_samples}
        # 存储 姿态点序列 样本信息
        with open(save_dir + os.sep + sample_name, 'wb') as fs:
            pickle.dump(a_sample, fs)  # saved pkl

        # 生成vids & 存储
        lab_videoName = Path(sample_name).with_suffix('.avi').__str__()
        vid_writer = cv2.VideoWriter(save_dir + os.sep + lab_videoName, cv2.VideoWriter_fourcc(*'XVID'),
                                     25, (1920, 1080))  # 创建保存视频的对象
        with mod_timer():
            for idf in a_sample['seq_vid']:
                vid_writer.write(imgs_seq[idf])  # saving avi 坐位体前屈 防作弊 标签
        vid_writer.release()


    return invalid_vids



def gen_sitUp_lab_fake_vid(vid_info, data_cls):
    """
    仰卧起坐 作弊检测：生成样本和视频标签
    :param data_cls:  用于获取 样本保存文件夹data_cls.save_dir
    :param vid_info:
    :return: 返回无效视频路径
    """
    save_pth = data_cls.save_dir
    proj_name = data_cls.proj_name

    invalid_vids = []
    for path, sample_info in vid_info.items():
        # 提取基本信息
        img_shape = sample_info['img_shape']
        vid_stem = Path(sample_info['video_name']).stem
        total_frames = sample_info['total_frames']
        seq_parts = sample_info['seq_parts']
        skpt_samples = sample_info['skpt_samples']
        status = sample_info['status']


        if len(seq_parts) == 0 or any(map(lambda x: x is None or (hasattr(x, '__len__') and len(x) ==0), skpt_samples)):
            logger.info(f"@Moss: No valid seq_parts or skpt in {path}")     # 针对手工录制的视频,跳过无效视频
            invalid_vids.append(path)
            continue

        logger.info(f"{str(Path(path).name)}: ")        # 当前 有效视频 loading:
        save_oriImg = data_cls.save_oriImg
        # 存储 单个视频的姿态点到有效样本的图像上
        imgs_seq = deal_videoLabs(sample_info, video_pth=path, model_info=data_cls.model_info, mark_info={}, proj_name=proj_name,
                                  save_oriImg=save_oriImg)
        if save_oriImg:
            imgs_seq, ori_img0s_seq = imgs_seq
        else:
            ori_img0s_seq = None

        save_dir = save_pth + os.sep + Path(path).stem
        fade_zone = set(sample_info['obstruct_frames'])             # 盲区 帧列表
        Path(save_dir).mkdir(exist_ok=True)
        for aid, a_skpts in enumerate(sample_info['skpt_samples']):
            # 生成pkl 1个视频对应多个pkl和视频标签
            len_status = len(seq_parts[aid])                 # clip样本的原始长度
            if len_status < 10:
                continue

            # 盲区保存逻辑
            fade_frames = list(set(seq_parts[aid]).intersection(fade_zone))
            len_fade = len(fade_frames)
            if len_fade:         # 当前样本中存在盲区帧
                sample_name = vid_stem + f'_fade_zone_len{len_fade}_start{fade_frames[0]}_end{fade_frames[-1]}_{aid}.jpg'
                if not (hasattr(imgs_seq[fade_frames[0]], 'shape') and hasattr(imgs_seq[fade_frames[-1]], 'shape')):
                    continue
                first_endImg = np.concatenate((imgs_seq[fade_frames[0]], imgs_seq[fade_frames[-1]]), axis=0)
                _FadeImg = Image.fromarray(first_endImg)
                _FadeImg.save(f"{save_dir + os.sep + sample_name}")
                print('fade—frames:', fade_frames)
                continue

            sample_name = vid_stem + f'_len{len_status}_loop{seq_parts[aid][0]}_{seq_parts[aid][-1]}_{aid}.pkl'

            a_sample = {'img_shape': img_shape, 'vid_pth': path, 'total_frames': total_frames,
                        'sample_name': sample_name, 'pred_skpts': a_skpts, 'status': status, 'start': seq_parts[aid][0], 'end': seq_parts[aid][-1], 'len_status':len_status,
                        'seq_vid': seq_parts[aid], 'idx_sample': seq_parts[aid], 'ori_skpts': a_skpts}

            # 存储 姿态点序列 样本信息
            with open(save_dir + os.sep + sample_name, 'wb') as fs:
                pickle.dump(a_sample, fs)  # saved pkl

            # 生成vids & 存储
            lab_videoName = Path(sample_name).with_suffix('.avi').__str__()
            vid_writer = cv2.VideoWriter(save_dir + os.sep + lab_videoName, cv2.VideoWriter_fourcc(*'XVID'), 25, (1920, 1080))  # 创建保存视频的对象
            lab_videoName = Path(sample_name).with_suffix('.mp4').__str__()
            ori_vid_writer = cv2.VideoWriter(save_dir + os.sep + lab_videoName, cv2.VideoWriter_fourcc(*'mp4v'), 25, (1920, 1080)) if save_oriImg else None  # 创建保存原始视频的对象
            with mod_timer():
                for idf in sample_info['seq_parts'][aid]:
                    vid_writer.write(imgs_seq[idf])  # saving avi 仰卧起坐防作弊 标签
                    ori_vid_writer.write(ori_img0s_seq[idf]) if save_oriImg else None     # saving avi 仰卧起坐防作弊 标签
            vid_writer.release()
            ori_vid_writer.release() if save_oriImg else None




    return invalid_vids


def gen_sitUp_lab_fusion_vid(vid_info, data_cls):
    """
    PoseRGB fusion label generate @Moss
    仰卧起坐 作弊检测：生成样本和视频标签
    :param data_cls:  用于获取 样本保存文件夹data_cls.save_dir
    :param vid_info:
    :return: 返回无效视频路径
    """
    save_pth = data_cls.save_dir
    proj_name = data_cls.proj_name

    invalid_vids = []
    for path, sample_info in vid_info.items():
        # 提取基本信息
        img_shape = sample_info['img_shape']
        vid_stem = Path(sample_info['video_name']).stem
        total_frames = sample_info['total_frames']
        seq_parts = sample_info['seq_parts']
        skpt_samples = sample_info['skpt_samples']
        status = sample_info['status']

        sample_info['peak_parts'] = []      # 获取需要保存的图像下标
        # 保存运动3帧
        for a_seq in seq_parts:
            lay_0 = a_seq[0]  # 取躺平帧
            lay_1 = next((f for f in a_seq if status[f] < 115), None)
            lay_2 = a_seq[-1]         #  取最后1帧
            sample_info['peak_parts'].append([lay_0, lay_1, lay_2])           # 拐点图像下标

        # print(sample_info['peak_parts'])
        if len(seq_parts) == 0 or any(map(lambda x: x is None or (hasattr(x, '__len__') and len(x) ==0), skpt_samples)):
            logger.info(f"@Moss: No valid seq_parts or skpt in {path}")     # 针对手工录制的视频,跳过无效视频
            invalid_vids.append(path)
            continue

        logger.info(f"{str(Path(path).name)}: ")        # 当前 有效视频 loading:
        # 存储 单个视频的姿态点到有效样本的图像上
        imgs_seq = deal_videoLabs(sample_info, video_pth=path, model_info=data_cls.model_info, mark_info={}, proj_name=proj_name,
                                  save_oriImg=False)


        save_dir = save_pth + os.sep + Path(path).stem.split('.')[0]
        Path(save_dir).mkdir(exist_ok=True)
        for aid, a_skpts in enumerate(sample_info['skpt_samples']):
            # 生成pkl 1个视频对应多个pkl和视频标签
            len_status = len(seq_parts[aid])                 # clip样本的原始长度
            if len_status < 10:
                continue

            # 骨骼信息关键裁剪区域
            target_skpts = [skpt[0] for idx, skpt in enumerate(a_skpts) if isinstance(skpt, list) and skpt[0] is not None]
            _idx, up_edge_y = min(enumerate(target_skpts), key=lambda x: x[1][1])          # 上边界
            up_edge_y = int(up_edge_y[1])


            lay_0_skpt = a_skpts[0][0]

            do_Right = lay_0_skpt[9] <= 1920 /2
            lay_2_skpt = target_skpts[-1]
            # 定义 左边界
            if do_Right:      # 朝->右边坐
                delt_x = (lay_0_skpt[21] + lay_0_skpt[24]) * 0.5 - lay_0_skpt[0]
                up_edge_x = lay_0_skpt[0] - delt_x      # 取肩点中心到人体框的距离
                up_edge_x = 0 if up_edge_x < 0 else int(up_edge_x)  # 左边界x
                down_edge_x = int(lay_2_skpt[2])        # # 右边界
            else:
                delt_x = lay_0_skpt[2] -(lay_0_skpt[21] + lay_0_skpt[24]) * 0.5
                up_edge_x = int(lay_2_skpt[0])            # 朝左 做 左边界
                down_edge_x = lay_0_skpt[2] + delt_x
                down_edge_x = 1920 if down_edge_x > 1920 else int(down_edge_x)      # 右边界


            sample_name = vid_stem + f'_len{len_status}_loop{seq_parts[aid][0]}_{seq_parts[aid][-1]}_{aid}.pkl'

            a_sample = {'img_shape': img_shape, 'vid_pth': path, 'total_frames': total_frames,
                        'sample_name': sample_name, 'pred_skpts': a_skpts, 'status': status, 'start': seq_parts[aid][0], 'end': seq_parts[aid][-1], 'len_status':len_status,
                        'seq_vid': seq_parts[aid], 'idx_sample': seq_parts[aid], 'ori_skpts': a_skpts}

            # 存储 姿态点序列 样本信息
            with open(save_dir + os.sep + sample_name, 'wb') as fs:
                pickle.dump(a_sample, fs)  # saved pkl

            # 生成vids & 存储
            lab_videoName = Path(sample_name).with_suffix('.avi').__str__()
            ori_vid_writer = cv2.VideoWriter(save_dir + os.sep + lab_videoName, cv2.VideoWriter_fourcc(*'XVID'), 25, (1920, 1080))  # 创建保存原始视频的对象
            with mod_timer():
                for idf in sample_info['seq_parts'][aid]:
                    skl_im, peak_img = imgs_seq[idf]
                    ori_vid_writer.write(skl_im)  # saving avi 仰卧起坐防作弊 标签
                    if peak_img is not None:
                        # 保存原始图像
                        peak_name = vid_stem + f'_len{len_status}_loop{seq_parts[aid][0]}_{seq_parts[aid][-1]}_{aid}-{idf}.jpg'
                        # 裁剪图像
                        peak_img_cropped = peak_img[up_edge_y:1080, up_edge_x:down_edge_x]
                        if os.name == 'posix':
                            success = cv2.imwrite(save_dir + os.sep + peak_name, peak_img_cropped)
                            if success:
                                print(peak_name)
                        elif os.name == 'nt':
                            cv2.imencode('.jpg', peak_img_cropped)[1].tofile(save_dir + os.sep + peak_name)
                            print(peak_name)
                        else:
                            raise TypeError(f"@Moss: {os.name} platform not support cv2.")



            ori_vid_writer.release()


    return invalid_vids


def gen_run50_lab_fake_img(base_info, trackSkpt_pos, track_pos):
    """
        50m 作弊检测：生成 指定跑道(pos)样本 和 图像标签

    :return: 返回无效视频路径
    """
    pos, path, im0, im0s, frame_id, oriVid_shape, check_imgs, P_lst, dets_info = base_info

    P1s, P2s, P1e, P2e, P3e = P_lst         # 拿来所有标定，防止有其他用途
    im0_cp = copy.deepcopy(im0)  # 结束帧的画面
    # cv2.imwrite(f'tes-{frame_id}.png', crop_start_pos)
    start_pt = (P1e[pos][0] + P1e[pos + 1][0]) * 0.5, (P1e[pos][1] + P1e[pos + 1][1]) * 0.5
    num = len(trackSkpt_pos)

    lab_videoName = f'{Path(path).stem}_P{pos + 1}_{frame_id}'
    save_dir = Path(path).parent.__str__()
    # vid_writer = cv2.VideoWriter(save_dir + os.sep + lab_videoName+'.avi', cv2.VideoWriter_fourcc(*'XVID'), 25, (1920, 1080))  # 创建保存视频的对象


    sample_lst = [i for i in range(num) if i % 10 ==0 or i==num-1]      # 10帧抽样用于展示在标签中
    for tid, det_i in enumerate(trackSkpt_pos):
        if tid not in sample_lst or det_i is None:
            continue
        det_box, det_skpt, tid_img = det_i[0][:4], torch.flatten(det_i[1]), det_i[-1]
        plot_skeleton_kpts(im0_cp, kpts=det_skpt, steps=3, orig_shape=im0.shape[:2], kpt_num=17)
        det_i.pop()         # 删除最后1个元素：图像


    # 过线帧的人体框
    crop_start, fist_info, end_info = overLine_person_lab(im0s, im0_cp, trackSkpt_pos, track_pos)
    end_h, end_w, end_per = end_info        # 用于裁剪的信息

    # 终点摄像头 起点附近有效区域的 丢失跟踪样本 和标签
    s1s2_m2m1 = fist_info[3] if len(fist_info)==4 else [None,None,None,None]
    Start_run, crop_p1p2 = get_EndCam_StartAreaTarget(dets_info, s1s2_m2m1, im0s, im0_cp)

    # 将标定画在图上
    plot_markInfo(marks=P_lst[-3:], im0=im0_cp)

    # 兼容视频起点双摄
    check_imgs_pos = check_imgs[pos-1] if 0 <= abs(pos-1) < len(check_imgs) else check_imgs[0]
    if not len(check_imgs):
        print(f"No Info in Pos{pos}")
        lab_image = im0_cp
        lab_image[:end_h, :end_w] = end_per
        lab_image = np.vstack((crop_p1p2, lab_image)) if crop_p1p2 is not None else lab_image
        det_Info = [Start_run, dets_info, P_lst]
        save_sample(im0, path, save_dir, lab_videoName, lab_image, oriVid_shape, num, trackSkpt_pos, start_pt, det_Info)
        return
    if not len(check_imgs_pos):
        lab_image = np.vstack((crop_start, im0_cp))
        lab_image = np.vstack((crop_p1p2, lab_image)) if crop_p1p2 is not None else lab_image
        det_Info = [Start_run, dets_info, P_lst]
        save_sample(im0, path, save_dir, lab_videoName, lab_image, oriVid_shape, num, trackSkpt_pos, start_pt, det_Info)
        return

    # 拼接图像
    check_imgs_pos = [check_imgs[p] for p in range(len(check_imgs)) if len(check_imgs[p])][0]
    start_pos = check_imgs_pos[-1]
    start_dy = max(P2s[pi][1] for pi in range(len(P2s))).__int__()
    crop_start_pos = start_pos[:start_dy, :, :]  # 裁剪起点图像
    lab_image = np.vstack((crop_start_pos, im0_cp))
    lab_image[:end_h, :end_w] = end_per     # 直接默认贴在左边

    # 拼接图片
    lab_image = np.vstack((lab_image, crop_start))
    lab_image = np.vstack((crop_p1p2, lab_image)) if crop_p1p2 is not None else lab_image
    det_Info = [Start_run, dets_info, P_lst]
    save_sample(im0, path, save_dir, lab_videoName, lab_image, oriVid_shape, num, trackSkpt_pos, start_pt, det_Info)

    return


def overLine_person_lab(im0s, im0_cp, trackSkpt_pos, track_pos):
    """
    终点过线人体的跟踪轨迹 即标签图像生成
    """
    trackSkpt_cp1 = copy.deepcopy(trackSkpt_pos)

    last_box = next((x for x in reversed(trackSkpt_pos) if x is not None), np.random.randn(8))[0][:4].astype(np.int32)
    end_per = im0s[last_box[1]:last_box[3], last_box[0]:last_box[2]]
    end_h, end_w = end_per.shape[:2]
    plot_one_box(last_box, im0_cp, label=f' trackId-{track_pos}', color=colors(0, True), line_thickness=4)
    # 起点
    fist_info = next((x for x in trackSkpt_cp1 if x is not None), None)
    fist_box = fist_info[0][:4]
    first_top, first_down = int(fist_box[1]-45),  int(fist_box[3] + 2 * abs(fist_box[3]-fist_box[1]))
    fist_img = fist_info[2]
    plot_one_box(fist_box, fist_img, label=f' trackId-{track_pos}', color=colors(0, True), line_thickness=4)

    crop_start = fist_img[first_top:first_down, :,:]  # 裁剪起点图像
    end_info = end_h, end_w, end_per

    return crop_start, fist_info, end_info


def get_EndCam_StartAreaTarget(dets_info, s1s2_m2m1, im0s, im0_cp):
    """
    # 计算和筛选 终点摄像头 起点附近有效区域的 丢失跟踪样本
    return Start_run, crop_p1p2         # 样本和标签
    """

    End_star, Trac_star, all_dets = dets_info
    crop_p1p2 = None
    Start_run = {}
    if s1s2_m2m1[0] is not None:
        cv2.polylines(im0s, [np.array(s1s2_m2m1, dtype=np.int32).reshape(1, -1, 2)], isClosed=True, color=(0, 255, 0))
        cv2.polylines(im0_cp, [np.array(s1s2_m2m1, dtype=np.int32).reshape(1, -1, 2)], isClosed=True, color=(0, 255, 0))
        num_circles = Trac_star - End_star
        for ti, de_i in enumerate(range(End_star, Trac_star)):
            a_skpt = compare_inter_max_EndCam(xyxy_list=all_dets[de_i], points=s1s2_m2m1)
            Start_run[de_i] = a_skpt        # 存储样本
            if a_skpt is not None:          # 将人体姿态画在标定上、框是不准确且相对较大的
                plot_skeleton_kpts(im0s, kpts=a_skpt[6:], steps=3, orig_shape=im0s.shape[:2], kpt_num=17)
        # 按标定裁剪画面
        crop_p1p2 = im0s[:s1s2_m2m1[2][1]+100, :, :]        # H,W,C

    return Start_run, crop_p1p2


def save_sample(im0, path, save_dir, lab_videoName, lab_image, oriVid_shape, num, trackSkpt_pos, start_pt, det_Info=[]):
    # 存储图像信息
    imageLab_path = (Path(path).parent / f'{lab_videoName}.jpg').__str__()
    imageLab_writer, ori_im = cv2.imencode('.jpg', lab_image)
    if imageLab_writer:
        with open(imageLab_path, 'wb') as f:
            f.write(ori_im)
    print(f"image label generated {imageLab_writer},", end=' ')

    # 存储pkl信息: 1个跑道(pos)上有符合条件的过线人体，对应有1个pkl
    sample_name = f"{lab_videoName}.pkl"
    Start_run, detsInfo_lst, marks_info = det_Info
    a_sample = {'oriVid_shape': oriVid_shape,'img_shape': im0.shape, 'vid_pth': path, 'total_frames': num,
                'sample_name': sample_name, 'pred_skpts': trackSkpt_pos,
                'start_pt': start_pt,
                'Start_run':Start_run,
                # [End_star:开始引入跟踪的 终点画面切入大概时间, Trac_star:跟踪id首个不为None的frame_id, all_dets]
                'EndCam_starFrame':detsInfo_lst[0], 'Trac_star':detsInfo_lst[1], 'all_dets':detsInfo_lst[2],
                'P12sP123e': marks_info}
    # 存储 姿态点序列 样本信息
    with open(save_dir + os.sep + sample_name, 'wb') as fs:
        pickle.dump(a_sample, fs)  # saved pkl
    print(f'pkl generated.')

    return



if __name__ == '__main__':
    #
    # 读取baseline
    data_cls = read_vidConfig(f"Config/baseline.yml")

    # 基于关键帧序列 采样生成 样本帧索引id

    proj_info = data_cls[data_cls.proj_name]
    vid_info = gen_samples(data_cls.vid_info, total_frames=proj_info.total_frames,
                           interval_frames=proj_info.interval_frames, sample_rate=proj_info.sample_rate)

    # 生成单样本及单标签
    Path(data_cls.save_dir).mkdir(exist_ok=True, parents=True)  # 不存在则创建 存储路径
    invalid_vids = gen_lab_vid(vid_info, data_cls)

    if not len(invalid_vids) ==0:
        Path(data_cls.videos_pth + '_invalid_vids').mkdir(exist_ok=True)
        dst = Path(data_cls.videos_pth + '_invalid_vids')
        for src_pth in invalid_vids:
            dst_pth = str(Path(dst) / Path(src_pth).name)
            os.rename(src_pth, dst_pth)




