# -*-coding:utf-8-*-
"""
针对原始视频，出成绩的帧，进行隔帧保存原始图片
# 新增功能，保存裁剪后的图片         @Moss 20241214 09:44
"""
from GenVidInfo import *
from Gen_Samples import *

import os
from Config.devices_selected import select_device, get_free_gpu_ids

import faulthandler
faulthandler.enable()           # python -X faulthandler your_script.py



def main(data_cls):
    # device = select_device(get_free_gpu_ids(num_gpus=1, selected_device=data_cls.devices), batch_size=128)            # 用1张卡进行推理
    device = get_free_gpu_ids(num_gpus=1, selected_device=data_cls.devices)  # 用1张卡进行推理
    data_cls.card_id = int(device)
    # os.environ["CUDA_VISABLE_DEVICES"] = f'{device}'                                # 选择空闲的卡
    print(f"@Moss: we find and use device {device} for free cuda infer")

    if not Path(data_cls.get('save_dir')).exists():
        ori_save = data_cls['videos_pth']
        data_cls['save_dir'] = Path(ori_save).with_name(f"{Path(ori_save).name}_sampled").__str__()  # 采样数据存储位置

    # 获取 符合逻辑的关键帧idx和骨骼点
    data_dict = track_sitreach_videos_img(data_cls, multiprocess=False)  # 新增逻辑 筛选及保存出成绩的图片




if __name__ == '__main__':

    # ================================1. 先读配置项，读取视频, 保存data_info.pkl =====================================================================

    data_cls = read_config(f"Config/baseline_sitReach_fake.yml")  # 读取所有配置内容, 如无必要，无需修改主函数

    main(data_cls)
