# -*-coding:utf-8-*-
import multiprocessing
from pathlib import Path

import tqdm


def main_try(sub_dirs):
    print('001')
    try:
        print('')
    except Exception as e:
        print(e)


    return


def process_subdir(sub_dir):
    return main_try(sub_dir)


def multiprocess_main(dirs:str, arg):

    sub_dirs = [str(sub) for sub in Path(dirs).glob('*') if sub.is_dir()]       # 获取路径下的所有文件夹(不含子目录)

    with multiprocessing.Pool(processes=multiprocessing.cpu_count()) as pool:
        # pool.starmap(main_try, [(arg, sub_dir) for sub_dir in sub_dirs])
        pool.map(process_subdir, sub_dirs)
        # pool.map(main_try, sub_dirs)

    return


def move_likeName(path):
    list(Path(path).rglob('*'))

    return



if __name__ == '__main__':
    # dirs = '/root/share175/sport_datas/sit_and_reach/classify_cheating/ori_videos/SitReach_oriVideos/Zhengzhou_hangtianhangkongxueyuan/14_sitForward_C_2023_10'
    # multiprocess_main(dirs, arg=1)


    # dir1 = '/root/share175/sport_datas/sit_and_reach/classify_cheating/ori_videos/SitUp_oriVideos/Zhengzhou_shangxueyuan/nearby_oridata_1018/2024_10_18_10_37_2_192.168.2.129_1_3_E.mp4_sampled'
    # move_likeName(dir1)


    path1 = f"/root/share175/sport_datas/sit_and_reach/classify_cheating/ori_videos"


    mpv4 = tqdm.tqdm(list(Path(path1).rglob('*.mp4')))
    print(len(mpv4))