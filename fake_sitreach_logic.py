# -*-coding:utf-8-*-
from GenVidInfo import *
from Gen_Samples import *

import os
from Config.devices_selected import select_device, get_free_gpu_ids

import faulthandler
faulthandler.enable()           # python -X faulthandler your_script.py



def main(data_cls):
    # device = select_device(get_free_gpu_ids(num_gpus=1, selected_device=data_cls.devices), batch_size=128)            # 用1张卡进行推理
    device = get_free_gpu_ids(num_gpus=1, selected_device=data_cls.devices)  # 用1张卡进行推理
    data_cls.card_id = int(device)
    # os.environ["CUDA_VISABLE_DEVICES"] = f'{device}'                                # 选择空闲的卡
    print(f"@Moss: we find and use device {device} for free cuda infer")

    if not Path(data_cls.get('save_dir')).exists():
        ori_save = data_cls['videos_pth']
        data_cls['save_dir'] = Path(ori_save).with_name(f"{Path(ori_save).name}_sampled").__str__()  # 采样数据存储位置

    # 获取 符合逻辑的关键帧idx和骨骼点
    data_dict = track_sitreach_videos_logic(data_cls, multiprocess=False)  # 新增逻辑过滤盲区有人的样本

    # 存储信息
    # with open(f'{data_cls.videos_pth}/data_info.pkl', 'wb') as f:
    #     pickle.dump(data_dict, f)

    # ============================== 2. 再读pkl，生成样本 ================================================================================

    # assert Path(f'{data_cls.videos_pth}/data_info.pkl').exists(), f"@Moss: No file in {data_cls.videos_pth}/data_info.pkl， Please <EMAIL>"
    # with open(f'{data_cls.videos_pth}/data_info.pkl', 'rb') as dt:
    #     vid_info = pickle.load(dt)

    vid_info = data_dict
    data_cls['vid_info'] = vid_info  # 写入大类
    data_cls_pkl = data_cls

    # 基于关键帧序列 采样生成 样本帧索引id
    sitreach = data_cls_pkl[data_cls_pkl.proj_name]
    vid_info = gen_sitReach_suspects_samples(data_cls_pkl.vid_info, len_min=sitreach.len_min)

    # 生成 单样本 及 单标签
    Path(data_cls_pkl.save_dir).mkdir(exist_ok=True, parents=True)          # 不存在则创建 存储路径
    invalid_vids = gen_sitReach_lab_fake_vid(vid_info, data_cls_pkl)

    if not len(invalid_vids) == 0:
        Path(data_cls_pkl.videos_pth + '_invalid_vids').mkdir(exist_ok=True)
        dst = Path(data_cls_pkl.videos_pth + '_invalid_vids')
        for src_pth in invalid_vids:
            dst_pth = str(Path(dst) / Path(src_pth).name)
            os.rename(src_pth, dst_pth)



if __name__ == '__main__':

    # ================================1. 先读配置项，读取视频, 保存data_info.pkl =====================================================================

    data_cls = read_config(f"Config/baseline_sitReach_fake.yml")  # 读取所有配置内容, 如无必要，无需修改主函数

    data_cls_bp = copy.deepcopy(data_cls)

    all_vids = list(Path(data_cls_bp.videos_pth).rglob('*.mp4'))
    len_vids = len(all_vids)

    if len_vids < 200:
        main(data_cls)
    else:
        n = round(len_vids / 100)
        size = len_vids // n
        remainder = len_vids % n
        parts, start = [], 0

        for i in range(n):
            # 可计算每份大小，考虑余数
            end = start + size + (1 if 1<remainder else 0)
            # 分割数据
            part = all_vids[start: end]
            parts.append(part)
            start = end
            # make dir
            sub_dir = (Path(data_cls.videos_pth) / f'auto_folder_{i + 1}')
            sub_dir.mkdir(exist_ok=True)
            for vid in part:
                vid.rename(sub_dir / vid.name)

        all_vids_club = [fo for fo in Path(data_cls_bp.videos_pth).iterdir() if fo.is_dir()]
        for folder in all_vids_club:
            data_cls.videos_pth = str(folder)
            main(data_cls)


    # main(data_cls)
