import numpy as np
# from utils.self_define import calculate_run_time

# @calculate_run_time
def non_max_suppression_np_pose(
    prediction,
    conf_thres=0.25,
    iou_thres=0.45,
    classes=None,
    agnostic=False,
    multi_label=False,
    labels=(),
    max_det=300,
    nc=0,  # number of classes (optional)
    max_time_img=0.05,
    max_nms=30000,
    max_wh=7680,
    in_place=True,
    rotated=False,
):
    """
    Perform non-maximum suppression (NMS) on a set of boxes using NumPy, compatible with YOLOv8 pose model.

    Args:
        prediction (np.ndarray): A tensor of shape (batch_size, num_classes + 4 + num_keypoints * 3, num_boxes)
            containing the predicted boxes, classes, and keypoints. The tensor should be in the format
            output by a model, such as YOLOv8 pose.
        conf_thres (float): The confidence threshold below which boxes will be filtered out.
            Valid values are between 0.0 and 1.0.
        iou_thres (float): The IoU threshold below which boxes will be filtered out during NMS.
            Valid values are between 0.0 and 1.0.
        classes (List[int]): A list of class indices to consider. If None, all classes will be considered.
        agnostic (bool): If True, the model is agnostic to the number of classes, and all
            classes will be considered as one.
        multi_label (bool): If True, each box may have multiple labels.
        labels (List[List[Union[int, float, np.ndarray]]]): A list of lists, where each inner
            list contains the apriori labels for a given image. The list should be in the format
            output by a dataloader, with each label being a tuple of (class_index, x1, y1, x2, y2).
        max_det (int): The maximum number of boxes to keep after NMS.
        nc (int, optional): The number of classes output by the model. Any indices after this will be considered keypoints.
        max_time_img (float): The maximum time (seconds) for processing one image.
        max_nms (int): The maximum number of boxes into NMS.
        max_wh (int): The maximum box width and height in pixels.
        in_place (bool): If True, the input prediction tensor will be modified in place.
        rotated (bool): If Oriented Bounding Boxes (OBB) are being passed for NMS.

    Returns:
        (List[np.ndarray]): A list of length batch_size, where each element is a tensor of
            shape (num_boxes, 6 + num_keypoints * 3) containing the kept boxes, with columns
            (x1, y1, x2, y2, confidence, class, keypoint1_x, keypoint1_y, keypoint1_score, ...).
    """
    # Checks
    assert 0 <= conf_thres <= 1, f"Invalid Confidence threshold {conf_thres}, valid values are between 0.0 and 1.0"
    assert 0 <= iou_thres <= 1, f"Invalid IoU {iou_thres}, valid values are between 0.0 and 1.0"
    if isinstance(prediction, (list, tuple)):  # YOLOv8 model in validation model, output = (inference_out, loss_out)
        prediction = prediction[0]  # select only inference output

    bs = prediction.shape[0]  # batch size (BCN,例如：1,56,n)
    nc = nc or (prediction.shape[1] - 4 - 51)  # number of classes. TODO 51表示17*3个人体姿态关键点，4表示xywh的人体框
    nm = 51  # number of keypoints * 3 (x, y, score)
    mi = 4 + nc  # keypoints start index
    xc = prediction[:, 4:mi].max(axis=1) > conf_thres  # candidates xc -> (1,n)[例如：1,7056]

    # Settings
    time_limit = 2.0 + max_time_img * bs  # seconds to quit after
    multi_label &= nc > 1  # multiple labels per box (adds 0.5ms/img)

    prediction = np.transpose(prediction, (0, 2, 1))  # shape(1,57,6300) to shape(1,6300,57)
    if not rotated:
        if in_place:
            prediction[..., :4] = xywh2xyxy_np(prediction[..., :4])  # xywh[中心点+宽高] to xyxy[左上角右下角]     # TODO prediction -> batch,n,56
        else:
            prediction = np.concatenate((xywh2xyxy_np(prediction[..., :4]), prediction[..., 4:]),axis=-1)  # xywh to xyxy

    output = [np.zeros((0, 6 + nm))] * bs
    for xi, x in enumerate(prediction):  # image index, image inference
        # Apply constraints         xi表示batch中的一个元素，x-> n，56
        x = x[xc[xi]]  # confidence     例:7056,56 -> 10,56

        # Cat apriori labels if autolabelling
        if labels and len(labels[xi]) and not rotated:
            lb = labels[xi]
            v = np.zeros((len(lb), nc + nm + 4))
            v[:, :4] = xywh2xyxy_np(lb[:, 1:5])  # box
            v[range(len(lb)), lb[:, 0].astype(int) + 4] = 1.0  # cls
            x = np.concatenate((x, v), axis=0)

        # If none remain process next image
        if not x.shape[0]:      # 如果当前帧没有预测结果，直接跳过
            continue

        # Detections matrix nx6 (xyxy, conf, cls)
        box, cls, keypoints = np.split(x, [4, 4 + nc], axis=1)      # np.aplit(x,[4,5],axis=1):表示在维度1上切分2次，得到3个数组。切分的位置就是[4,5]

        if multi_label:
            i, j = np.where(cls > conf_thres)
            x = np.concatenate((box[i], x[i, 4 + j, None], j[:, None].astype(float), keypoints[i]), axis=1)
        else:  # best class only
            conf = cls.max(axis=1, keepdims=True)
            j = cls.argmax(axis=1, keepdims=True)           # 预测为哪个类别
            x = np.concatenate((box, conf, j.astype(float), keypoints), axis=1)[conf.flatten() > conf_thres]        #

        # Filter by class
        if classes is not None:
            x = x[(x[:, 5:6] == classes).any(axis=1)]

        # Check shape
        n = x.shape[0]  # number of boxes
        if not n:  # no boxes
            continue
        if n > max_nms:  # excess boxes
            x = x[x[:, 4].argsort()[::-1][:max_nms]]  # sort by confidence and remove excess boxes

        # Batched NMS
        c = x[:, 5:6] * (0 if agnostic else max_wh)  # classes
        scores = x[:, 4]  # scores
        if rotated:
            boxes = np.concatenate((x[:, :2] + c, x[:, 2:4], x[:, -1:]), axis=-1)  # xywhr
            i = nms_rotated_np(boxes, scores, iou_thres)
        else:
            boxes = x[:, :4] + c  # boxes (offset by class)
            i = nms_np(boxes, scores, iou_thres)  # NMS
        i = i[:max_det]  # limit detections

        output[xi] = x[i]

    return output


def xywh2xyxy_np(x):
    """
    Convert bounding box coordinates from (x, y, w, h) to (x1, y1, x2, y2).
    """
    y = np.copy(x)
    y[:,:, 0] = x[:, :, 0] - x[:, :, 2] / 2  # x1 = x - w/2
    y[:,:, 1] = x[:, :, 1] - x[:, :, 3] / 2  # y1 = y - h/2
    y[:,:, 2] = x[:, :, 0] + x[:, :, 2] / 2  # x2 = x + w/2
    y[:,:, 3] = x[:, :, 1] + x[:, :, 3] / 2  # y2 = y + h/2
    return y


def nms_np(boxes, scores, iou_threshold):
    """
    Perform non-maximum suppression (NMS) on a set of boxes using NumPy.
    """
    x1 = boxes[:, 0]
    y1 = boxes[:, 1]
    x2 = boxes[:, 2]
    y2 = boxes[:, 3]

    areas = (x2 - x1 + 1) * (y2 - y1 + 1)
    order = scores.argsort()[::-1]

    keep = []
    while order.size > 0:
        i = order[0]
        keep.append(i)

        xx1 = np.maximum(x1[i], x1[order[1:]])      # np.maximum(x,x_list) 逐位比较，将x和x_list中的所有数据逐位进行比较。
        yy1 = np.maximum(y1[i], y1[order[1:]])
        xx2 = np.minimum(x2[i], x2[order[1:]])
        yy2 = np.minimum(y2[i], y2[order[1:]])

        w = np.maximum(0, xx2 - xx1 + 1)
        h = np.maximum(0, yy2 - yy1 + 1)
        inter = w * h
        iou = inter / (areas[i] + areas[order[1:]] - inter)

        inds = np.where(iou <= iou_threshold)[0]
        order = order[inds + 1]

    return np.array(keep)


def nms_rotated_np(boxes, scores, iou_threshold):
    """
    Perform non-maximum suppression (NMS) on a set of rotated boxes using NumPy.
    """
    # Placeholder for rotated NMS implementation
    raise NotImplementedError("Rotated NMS is not implemented in this version.")


def scale_coords_np(img1_shape, coords, img0_shape, ratio_pad=None, normalize=False, padding=True):
    """
    Rescale segment coordinates (xy) from img1_shape to img0_shape.

    Args:
        img1_shape (tuple): The shape of the image that the coords are from.
        coords (torch.Tensor): the coords to be scaled of shape n,2.
        img0_shape (tuple): the shape of the image that the segmentation is being applied to.
        ratio_pad (tuple): the ratio of the image size to the padded image size.
        normalize (bool): If True, the coordinates will be normalized to the range [0, 1]. Defaults to False.
        padding (bool): If True, assuming the boxes is based on image augmented by yolo style. If False then do regular
            rescaling.

    Returns:
        coords (torch.Tensor): The scaled coordinates.
    """
    if ratio_pad is None:  # calculate from img0_shape
        gain = min(img1_shape[0] / img0_shape[0], img1_shape[1] / img0_shape[1])  # gain  = old / new
        pad = (img1_shape[1] - img0_shape[1] * gain) / 2, (img1_shape[0] - img0_shape[0] * gain) / 2  # wh padding
    else:
        gain = ratio_pad[0][0]
        pad = ratio_pad[1]

    if padding:
        coords[..., 0] -= pad[0]  # x padding
        coords[..., 1] -= pad[1]  # y padding
    coords[..., 0] /= gain
    coords[..., 1] /= gain
    coords = clip_coords_np(coords, img0_shape)
    if normalize:
        coords[..., 0] /= img0_shape[1]  # width
        coords[..., 1] /= img0_shape[0]  # height
    return coords

def clip_coords_np(coords, shape):
    """
    Clip line coordinates to the image boundaries.

    Args:
        coords (numpy.ndarray): A list of line coordinates.
        shape (tuple): A tuple of integers representing the size of the image in the format (height, width).

    Returns:
        (numpy.ndarray): Clipped coordinates
    """
    coords[..., 0] = coords[..., 0].clip(0, shape[1])  # x
    coords[..., 1] = coords[..., 1].clip(0, shape[0])  # y
    return coords


def scale_boxes_np(img1_shape, boxes, img0_shape, ratio_pad=None, padding=True, xywh=False):
    """
    Rescales bounding boxes (in the format of xyxy by default) from the shape of the image they were originally
    specified in (img1_shape) to the shape of a different image (img0_shape).

    Args:
        img1_shape (tuple): The shape of the image that the bounding boxes are for, in the format of (height, width).
        boxes (torch.Tensor): the bounding boxes of the objects in the image, in the format of (x1, y1, x2, y2)
        img0_shape (tuple): the shape of the target image, in the format of (height, width).
        ratio_pad (tuple): a tuple of (ratio, pad) for scaling the boxes. If not provided, the ratio and pad will be
            calculated based on the size difference between the two images.
        padding (bool): If True, assuming the boxes is based on image augmented by yolo style. If False then do regular
            rescaling.
        xywh (bool): The box format is xywh or not, default=False.

    Returns:
        boxes (torch.Tensor): The scaled bounding boxes, in the format of (x1, y1, x2, y2)
    """
    if ratio_pad is None:  # calculate from img0_shape
        gain = min(img1_shape[0] / img0_shape[0], img1_shape[1] / img0_shape[1])  # gain  = old / new
        pad = (
            round((img1_shape[1] - img0_shape[1] * gain) / 2 - 0.1),
            round((img1_shape[0] - img0_shape[0] * gain) / 2 - 0.1),
        )  # wh padding
    else:
        gain = ratio_pad[0][0]
        pad = ratio_pad[1]

    if padding:
        boxes[..., 0] -= pad[0]  # x padding
        boxes[..., 1] -= pad[1]  # y padding
        if not xywh:
            boxes[..., 2] -= pad[0]  # x padding
            boxes[..., 3] -= pad[1]  # y padding
    boxes[..., :4] /= gain
    return clip_boxes_np(boxes, img0_shape)


def clip_boxes_np(boxes, shape):
    """
    Takes a list of bounding boxes and a shape (height, width) and clips the bounding boxes to the shape.

    Args:
        boxes (numpy.ndarray): the bounding boxes to clip
        shape (tuple): the shape of the image

    Returns:
        (numpy.ndarray): Clipped boxes
    """
    boxes[..., [0, 2]] = boxes[..., [0, 2]].clip(0, shape[1])  # x1, x2
    boxes[..., [1, 3]] = boxes[..., [1, 3]].clip(0, shape[0])  # y1, y2
    return boxes
