'''
    脚本作用：使用tensorrt进行模型推理测试。通过继承的方式，可以实现分类/检测/姿态等模型的tensorrt推理。
    本脚本需要导入non_max_suppression_np.py脚本中的函数
'''

import time
import pycuda.driver as cuda            # pycuda：Python对CUDA的封装，用于管理设别和主机之间的数据传输
import pycuda.autoinit
import tensorrt as trt                  # TensorRT API 提供对推理引擎的加载和执行支持

import numpy as np
import os
import cv2
from tensorRT.non_max_suppression_np import non_max_suppression_np_pose,scale_coords_np,scale_boxes_np


class TensorRTInference:
    def __init__(self, engine_path):
        self.logger = trt.Logger(trt.Logger.WARNING)
        self.runtime = trt.Runtime(self.logger)             # 用于反序列化TensorRT引擎

        # 读取序列化的 TensorRT 引擎
        with open(engine_path, "rb") as f:
            self.engine = self.runtime.deserialize_cuda_engine(f.read())

        # 创建执行上下文，用于设置输入/输出张量的形状和执行推理
        self.context = self.engine.create_execution_context()

        # 获取输入和输出张量名称
        self.input_tensor_name, self.output_tensor_name = self._get_io_tensor_names()

        # CUDA 相关初始化
        self.stream = cuda.Stream()     # CUDA 流，用于异步执行推理和数据传输
        self.input_d = None         # 推理时动态分配，记录输入数据在GPU上的内存地址
        self.output_d = None        # 推理时动态分配
        self.input_size = 0         # 记录输入设备内存的大小，用于动态分配GPU的内存
        self.output_size = 0        # 记录输出设备内存的大小

        print(f"Loaded TensorRT engine with input: {self.input_tensor_name}, output: {self.output_tensor_name}")

    def _get_io_tensor_names(self):
        """获取输入和输出张量的名称"""
        input_tensor_name = None
        output_tensor_name = None
        for i in range(self.engine.num_io_tensors):         # 获取引擎中所有输入/输出张量的数量
            tensor_name = self.engine.get_tensor_name(i)    # 获取第 i 个张量的名称
            tensor_mode = self.engine.get_tensor_mode(tensor_name)      # 获取张量的模式
            if tensor_mode == trt.TensorIOMode.INPUT:
                input_tensor_name = tensor_name
            elif tensor_mode == trt.TensorIOMode.OUTPUT:
                output_tensor_name = tensor_name
        return input_tensor_name, output_tensor_name

    def _allocate_memory(self,input_data,output_shape):
        '''分配设备内存'''
        # 释放旧的的设备内存
        if self.input_d is not None:
            self.input_d.free()
        if self.output_d is not None:
            self.output_d.free()
        # 分配新的设备内存
        self.input_size = input_data.nbytes         # 记录输入内存大小，输入数据的总字节数
        self.output_size = int(np.prod(output_shape)*input_data.dtype.itemsize)         # 记录输出内存大小。np.prod(output_shape) 输出张量的总元素数量，input_data.dtype.itemsize 输入数据每个元素的总字节数
        self.input_d = cuda.mem_alloc(self.input_size)
        self.output_d = cuda.mem_alloc(self.output_size)                # 分配GPU内存

    def infer(self, input_data):
        """执行推理"""
        input_shape = input_data.shape          # input_shape -> (1,3,448,768)
        self.context.set_input_shape(self.input_tensor_name, input_shape)
        assert self.context.all_binding_shapes_specified, "Not all binding shapes were specified!"

        # 获取动态输出形状
        output_shape = self.context.get_tensor_shape(self.output_tensor_name)

        # 分配设备内存（仅在首次推理或形状变化时重新分配）
        if self.input_d is None or self.input_size != input_data.nbytes or self.output_d is None or self.output_size != np.prod(output_shape) * input_data.dtype.itemsize:
            self._allocate_memory(input_data,output_shape)

        # 复制输入数据到 GPU
        cuda.memcpy_htod_async(self.input_d, input_data, self.stream)

        # 设置张量的内存地址
        self.context.set_tensor_address(self.input_tensor_name, int(self.input_d))
        self.context.set_tensor_address(self.output_tensor_name, int(self.output_d))

        # 异步执行推理
        self.context.execute_async_v3(stream_handle=self.stream.handle)

        # 读取输出数据
        output_data = np.empty(output_shape, dtype=np.float32)
        cuda.memcpy_dtoh_async(output_data, self.output_d, self.stream)

        # 同步流，确保数据拷贝完成
        self.stream.synchronize()
        return output_data


class ClassifyTensorRTInference(TensorRTInference):

    def __init__(self,engine_path,imgsz):
        super().__init__(engine_path)
        self.imgsz = imgsz

    def preprocess(self,im):
        im = self._resize_with_max_size(im,self.imgsz[0],self.imgsz[1])
        mean = np.array([0.485, 0.456, 0.406], dtype=np.float32)
        std = np.array([0.229, 0.224, 0.225], dtype=np.float32)
        im = (im.astype(np.float16) / 255.0 - mean) / std
        im = im.transpose((2, 0, 1))[::-1]
        im = np.ascontiguousarray(im)
        if len(im.shape) == 3:
            img = im[None]          # expand for batch dim
        return img

    def postprocess(self,pred):
        pred = np.exp(pred)/np.sum(np.exp(pred),axis=1,keepdims=True)       # pred是numpy，做softmax，得到概率值。
        max_prob = np.max(pred)                 # 获取概率最大的值
        pred_class = np.argmax(pred)            # 获取概率最大的值对应的类别
        return max_prob, pred_class

    def predict(self,img):
        img = self.preprocess(img)
        pred = self.infer(img)
        max_prob, pred_class = self.postprocess(pred)
        return max_prob, pred_class

    @staticmethod
    def _resize_with_max_size(image, size, max_size):
        '''不失真的resize，但是输入是正方形'''
        original_height, original_width, _ = image.shape
        # todo 先判断确定最终的width和height
        if original_width > original_height:            # todo 原始图片width>height
            if max_size < size * (original_width / original_height):            # TODO 长边超过max_size
                width = max_size
                height = int(max_size * (original_height / original_width))
            else:                   # TODO 长边不超过max_size
                height = size
                width = int(size * (original_width / original_height))
            pad_w_left = (max_size - width) // 2
            pad_w_right = (max_size - width) - pad_w_left
            pad_h_top = (max_size - height) // 2
            pad_h_bottom = (max_size - height) - pad_h_top
        else:        # todo 原始图片height>width
            if max_size < size * (original_height / original_width):            # TODO 长边超过max_size
                height = max_size
                width = int(max_size * (original_width / original_height))
            else:               # TODO 长边不超过max_size
                width = size
                height = int(size * (original_height / original_width))
            pad_w_left = (max_size - width) // 2
            pad_w_right = (max_size - width) - pad_w_left
            pad_h_top = (max_size - height) // 2
            pad_h_bottom = (max_size - height) - pad_h_top

        new_size = (width, height)
        # TODO 用opencv的cv2.resize()函数调整图像大小
        resized_image = cv2.resize(image, new_size, interpolation=cv2.INTER_LINEAR)
        padding_image = cv2.copyMakeBorder(resized_image, pad_h_top, pad_h_bottom, pad_w_left, pad_w_right,cv2.BORDER_CONSTANT, value=[114, 114, 114])
        return padding_image

    @staticmethod
    def _resize_with_maxsize_rect(image, size, max_size):
        '''不失真的resize，但是输入是矩形，不是正方形'''
        original_height, original_width, _ = image.shape
        # todo 先判断确定最终的width和height
        if original_width > original_height:                # todo 原始图片width>height
            if max_size < size * (original_width / original_height):            # TODO 长边超过max_size
                width = max_size
                height = int(max_size * (original_height / original_width))
                pad_w_left = 0
                pad_w_right = 0
                pad_h_top = (size - height) // 2
                pad_h_bottom = (size - height) - pad_h_top
            else:               # TODO 长边不超过max_size
                height = size
                width = int(size * (original_width / original_height))
                pad_w_left = (max_size - width) // 2
                pad_w_right = (max_size - width) - pad_w_left
                pad_h_top = 0
                pad_h_bottom = 0
        else:           # todo 原始图片height>width
            if max_size < size * (original_height / original_width):            # TODO 长边超过max_size
                height = max_size
                width = int(max_size * (original_width / original_height))
                pad_w_left = (size - width) // 2
                pad_w_right = (size - width) - pad_w_left
                pad_h_top = 0
                pad_h_bottom = 0
            else:                   # TODO 长边不超过max_size
                width = size
                height = int(size * (original_height / original_width))
                pad_w_left = 0
                pad_w_right = 0
                pad_h_top = (max_size - height) // 2
                pad_h_bottom = (max_size - height) - pad_h_top
        new_size = (width, height)
        # TODO 用opencv的cv2.resize()函数调整图像大小
        resized_image = cv2.resize(image, new_size, interpolation=cv2.INTER_LINEAR)
        padding_image = cv2.copyMakeBorder(resized_image, pad_h_top, pad_h_bottom, pad_w_left, pad_w_right,cv2.BORDER_CONSTANT, value=[114, 114, 114])
        return padding_image


class PoseTensorRTInference(TensorRTInference):
    def __init__(self, engine_path, conf, iou):
        super().__init__(engine_path)
        self.conf = conf
        self.iou = iou

    def preprocess(self, im):       # 对目标检测的数据进行预处理
        # 输入到这里的im是已经resize到模型输入尺寸的。resize工作是在dataset类中做的
        im = im.astype('float32')       # im -> ndarray (3,448,768)
        if len(im.shape) == 3:
            im = im[None]           # expand for batch dim      (1,3,448,768)
        im = im / 255.0             # 归一化
        return im                   # 模型推理尺寸1*3*384*288

    def postprocess(self, preds, img, orig_imgs):
        """Return detection results for a given input image or list of images."""
        preds = non_max_suppression_np_pose(preds,self.conf,self.iou)
        results = []
        for i, pred in enumerate(preds):
            orig_img = orig_imgs
            pred[:, :4] = scale_boxes_np(img.shape[1:], pred[:, :4], orig_img.shape).round()
            pred_kpts = pred[:, 6:].reshape(len(pred), *(17,3)) if len(pred) else pred[:, 6:]
            pred_kpts = scale_coords_np(img.shape[1:], pred_kpts, orig_img.shape)
            results.append((pred[:, :6],pred_kpts))
        return results          # 返回的结果是基于原图的


    def predict(self,img,orig_imgs):
        img_trt = self.preprocess(img)          # img_trt = (1,3,h,w)
        pred_trt = self.infer(img_trt)          # 输出是一个numpy数组      (1,56,n)
        pred = self.postprocess(pred_trt, img, orig_imgs)           # 输出 [(a,b)]        a=(n,6),b=(n,17,3)
        return pred


def build_engine_batch(onnx_file_path, fp16_mode=False):
    '''导出为batch形式'''
    logger = trt.Logger(trt.Logger.WARNING)  # trt.Logger() 用于记录TensorRT构建过程中的信息，等级是警告
    # 创建构建器和网络
    builder = trt.Builder(logger)  # TensorRT的核心对象，用于构建优化的推理引擎
    network = builder.create_network(1 << int(trt.NetworkDefinitionCreationFlag.EXPLICIT_BATCH))  # 创建一个空的神经网络，设置显示批次维度
    # 解析ONNX模型
    parser = trt.OnnxParser(network, logger)  # 用于解析ONNX模型数据并转换为TensorRT网络格式

    # 判断ONNX模型是否存在
    if not os.path.exists(onnx_file_path):
        print(f'ONNX file {onnx_file_path} not found!')
        return False

    with open(onnx_file_path, 'rb') as f:  # 如果解析失败，会打印所有错误
        if not parser.parse(f.read()):
            for error in range(parser.num_errors):
                print(parser.get_error(error))
            raise ValueError('Failed to parse ONNX file.')

    # 配置动态输入形状
    for i in range(network.num_inputs):
        input_tensor = network.get_input(i)
        print(f"Input tensor {i} name:{input_tensor.name}")
        input_tensor.shape = [-1] + list(input_tensor.shape[1:])        # 设置动态批次的维度（-1）

    # 配置引擎构建参数
    config = builder.create_builder_config()  # 创建构建配置，用于设置优化选项
    config.set_memory_pool_limit(trt.MemoryPoolType.WORKSPACE, 1 << 31)  # TODO 这是TensorRT 10.x版本推荐的设置内存池限制的方式
    # config.max_workspace_size = 1 << 30     # 1GB 配置构建引擎的GPU内存工作空间大小.#TODO 这是旧版本TensorRT 7.x版本的设置方式
    if fp16_mode:
        config.set_flag(trt.BuilderFlag.FP16)  # 半精度设置，提到速度，稍微降低精度

    # 创建优化配置文件，用于动态批次
    profile = builder.create_optimization_profile()
    for i in range(network.num_inputs):
        input_tensor = network.get_input(i)
        input_name = input_tensor.name
        input_shape = input_tensor.shape
        # 假设动态批次的范围是[1,4]
        min_shape = [1] + list(input_shape[1:])
        opt_shape = [4] + list(input_shape[1:])
        max_shape = [8] + list(input_shape[1:])
        profile.set_shape(input_name,min_shape,opt_shape,max_shape)
    config.add_optimization_profile(profile)

    # 构建TensorRT引擎
    # engine = builder.build_engine(network,config)     # 根据网络和配置，构建TensorRT TODO 旧版本，生成的是非序列化的TensorRT Engine。
    engine = builder.build_serialized_network(network, config)  # 直接构建引擎，直接生成已序列化的Engine
    if engine is None:
        raise RuntimeError('Failed to build the engine.')  # 如果构建失败，返回None，抛出异常

    # 保存引擎文件
    with open(onnx_file_path.replace('onnx', 'engine'), 'wb') as f:
        # f.write(engine.serialize())       # 旧版本的TensorRT，因为engine是非序列化的，所以需要手动对其进行序列化
        f.write(engine)
    print(f"TensorRT engine saved to:{onnx_file_path.replace('onnx', 'engine')}")
    return True


def build_engine(onnx_file_path, fp16_mode=False):
    '''导出为规定形状的形式，非batch形式'''
    logger = trt.Logger(trt.Logger.WARNING)     # trt.Logger() 用于记录TensorRT构建过程中的信息，等级是警告
    # 创建构建器和网络
    builder = trt.Builder(logger)       # TensorRT的核心对象，用于构建优化的推理引擎
    network = builder.create_network(1 << int(trt.NetworkDefinitionCreationFlag.EXPLICIT_BATCH))        # 创建一个空的神经网络，设置显示批次维度
    # 解析ONNX模型
    parser = trt.OnnxParser(network,logger)     # 用于解析ONNX模型数据并转换为TensorRT网络格式

    # 判断ONNX模型是否存在
    if not os.path.exists(onnx_file_path):
        print(f'ONNX file {onnx_file_path} not found!')
        return False

    with open(onnx_file_path,'rb') as f:        # 如果解析失败，会打印所有错误
        if not parser.parse(f.read()):
            for error in range(parser.num_errors):
                print(parser.get_error(error))
            raise ValueError('Failed to parse ONNX file.')

    # 配置引擎构建参数
    config = builder.create_builder_config()        # 创建构建配置，用于设置优化选项
    config.set_memory_pool_limit(trt.MemoryPoolType.WORKSPACE, 1 << 31)     #TODO 这是TensorRT 10.x版本推荐的设置内存池限制的方式
    # config.max_workspace_size = 1 << 30     # 1GB 配置构建引擎的GPU内存工作空间大小.#TODO 这是旧版本TensorRT 7.x版本的设置方式
    if fp16_mode:
        config.set_flag(trt.BuilderFlag.FP16)       # 半精度设置，提到速度，稍微降低精度

    # 构建TensorRT引擎
    # engine = builder.build_engine(network,config)     # 根据网络和配置，构建TensorRT TODO 旧版本，生成的是非序列化的TensorRT Engine。
    engine = builder.build_serialized_network(network, config)  # 直接构建引擎，直接生成已序列化的Engine
    if engine is None:
        raise RuntimeError('Failed to build the engine.')   # 如果构建失败，返回None，抛出异常

    # 保存引擎文件
    engine_file_path = onnx_file_path.replace('onnx','engine')
    p,f = os.path.split(engine_file_path)                           # TODO 增加Engine前缀
    engine_file_path = os.path.join(p,'Engine_'+f)

    with open(engine_file_path,'wb') as f:
        # f.write(engine.serialize())       # 旧版本的TensorRT，因为engine是非序列化的，所以需要手动对其进行序列化
        f.write(engine)
    print(f"TensorRT engine saved to:{p}")
    return True


# TODO：姿态模型-示例用法
if __name__ == "__main__":
    engine_file = "/root/persons/ai_group/Kyrie/Code_Tool/Yolov8_inference_pose/Inference_Model/engine/pose/Engine_Run50_100_detect_person_pose_20241114_V16_768_448.engine"  # 替换为实际的 TensorRT 引擎路径
    trt_infer = PoseTensorRTInference(engine_file,0.5,0.4)

    t1 = time.time()
    for i in range(100):
        # 构造输入数据
        input_array = np.random.randn(1, 3, 768, 448).astype(np.float32)  # 根据模型修改形状
        ori_input = np.random.randn(1440,2560,3).astype(np.float32)  # 根据模型修改形状
        output = trt_infer.predict(input_array,ori_input)
    t2 = time.time()
    print(t2-t1)

    print("Inference Output Shape:", output.shape)


# TODO 分类模型-示例用法
# if __name__ == "__main__":
#     engine_file = "/root/persons/ai_group/Kyrie/Code_Tool/Yolov8_inference_pose/Inference_Model/engine/classify/Engine_Run50_100_raise_hand_202201026_V10_128_128.engine"  # 替换为实际的 TensorRT 引擎路径
#     trt_infer = ClassifyTensorRTInference(engine_file,(112,128))
#
#     t1 = time.time()
#     for i in range(100):
#         # 构造输入数据
#         input_array = np.random.randn(150,128,3).astype(np.float32)  # 根据模型修改形状
#         output = trt_infer.predict(input_array)
#     t2 = time.time()
#     print(t2-t1)
#
#     print("Inference Output Shape:", output)






# # 老的代码,非常不高效
# import pycuda.driver as cuda        # pycuda：Python对CUDA的封装，用于管理设别和主机之间的数据传输
# import pycuda.autoinit
# import numpy as np
# import tensorrt as trt              # TensorRT API 提供对推理引擎的加载和执行支持
#
# # 推理函数
# def infer_dynamic_v3(engine, input_data):
#     with engine.create_execution_context() as context:
#         # 打印所有张量信息
#         print("All tensors:")
#         for i in range(engine.num_io_tensors):
#             tensor_name = engine.get_tensor_name(i)
#             tensor_mode = engine.get_tensor_mode(tensor_name)
#             io_type = "Input" if tensor_mode == trt.TensorIOMode.INPUT else "Output"
#             print(f"Tensor {i}: Name: {tensor_name}, Type: {io_type}, Shape: {engine.get_tensor_shape(tensor_name)}")
#
#         # 找到输入和输出张量名称
#         input_tensor_name = next(
#             engine.get_tensor_name(i) for i in range(engine.num_io_tensors)
#             if engine.get_tensor_mode(engine.get_tensor_name(i)) == trt.TensorIOMode.INPUT
#         )
#         output_tensor_name = next(
#             engine.get_tensor_name(i) for i in range(engine.num_io_tensors)
#             if engine.get_tensor_mode(engine.get_tensor_name(i)) == trt.TensorIOMode.OUTPUT
#         )
#
#         # 设置动态输入形状
#         context.set_input_shape(input_tensor_name, input_data.shape)
#         assert context.all_binding_shapes_specified, "Not all binding shapes were specified!"
#
#         # 获取动态输出形状
#         output_shape = context.get_tensor_shape(output_tensor_name)
#
#         # 创建 CUDA 流
#         stream = cuda.Stream()
#
#         # 分配设备内存
#         input_d = cuda.mem_alloc(input_data.nbytes)
#         output_d = cuda.mem_alloc(int(np.prod(output_shape) * input_data.dtype.itemsize))
#
#         # 主机到设备的数据传输
#         cuda.memcpy_htod_async(input_d, input_data, stream)
#
#         # 设置张量的内存地址
#         context.set_tensor_address(input_tensor_name, int(input_d))
#         context.set_tensor_address(output_tensor_name, int(output_d))
#
#         # 执行推理
#         context.execute_async_v3(stream_handle=stream.handle)
#
#         # 设备到主机的数据传输
#         output_data = np.empty(output_shape, dtype=np.float32)
#         cuda.memcpy_dtoh_async(output_data, output_d, stream)
#
#         # 同步流
#         stream.synchronize()
#
#         return output_data

# TODO 生成engine模型的示例
# if __name__ == '__main__':
#
#     onnx_file_path = '/root/persons/ai_group/Kyrie/Code_Tool/Data_Online_Collection/AutoCollectDataManagerVer2_0211/model/onnx_model'
#     # onnx_file_path = '/root/persons/ai_group/Kyrie/Code_Tool/Data_Online_Collection/AutoCollectDataManagerVer2_0211/model/onnx_model/detect_person_pose_situp_sitandreach_20241129_V39_768_448.onnx'
#     # onnx_file_path = '/root/persons/ai_group/Kyrie/Code_Tool/Data_Online_Collection/trt_inference/onnx_model/RopeJump_classifyRope_20250109_V17_224x224.onnx'
#
#     # 支持多个模型一次性运行
#     if os.path.isfile(onnx_file_path):              # 如果是一个模型文件
#         engine = build_engine(onnx_file_path, True)
#         # engine = build_engine_batch(onnx_file_path, True)           # TODO 导出为batch形式
#         if engine:
#             print('Engine successfully created!')
#     elif os.path.isdir(onnx_file_path):             # 如果是多个模型文件,则一次性的生成多个模型的结果
#         onnx_files_path = os.listdir(onnx_file_path)
#         root_onnx_path = onnx_file_path
#
#         for onnx_file_path in onnx_files_path:
#             print(onnx_file_path)
#             onnx_file_path = os.path.join(root_onnx_path, onnx_file_path)
#             engine = build_engine(onnx_file_path, True)
#             # engine = build_engine_batch(onnx_file_path, True)           # TODO 导出为batch形式
#             if engine:
#                 print('Engine successfully created!')
#             else:
#                 print(onnx_file_path)