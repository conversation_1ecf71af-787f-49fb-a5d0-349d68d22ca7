# -*-coding:utf-8-*-
from pathlib import Path

import numpy
import torch
import numpy as np
import time
import torchvision
import cv2

from utils.general import scale_coords,increment_path,plot_one_box,time_synchronized,colors,\
                                preprocess          # point_in_polygon,overlap_area,compare_inter_max,
from utils.general_v8 import non_max_suppression_V8, non_max_suppression_V80729, non_max_suppression
from tensorRT.non_max_suppression_np import non_max_suppression_np_pose
from utils.utils import letterbox,preprocess_img, scale_boxes


# 视频中每帧的推理结果 load yolo-pose onnx model
def pose_v8_infer(session, img, version='v8'):
    """
    yolo-V8 姿态模型推理
    """
    if session.__class__.__name__ == 'InferenceSession':
        res_np = session.run([session.get_outputs()[0].name], {session.get_inputs()[0].name: img})[0]
        _preds = torch.tensor(res_np)
        if version == 'v8':
            preds = non_max_suppression(_preds, conf_thres=0.40, iou_thres=0.45, classes=None, nc=1)  # 后处理[(1,56, 5040)] -> [(13, 57)]
        else:               # V10
            conf_thres = 0.5
            preds = [pred[pred[:, 4] > conf_thres] for pred in _preds]          # v10后处理
    elif session.__class__.__name__ == 'PoseTensorRTInference':
        pred_trt = session.infer(img)  # 输出是一个numpy数组      (1,56,n)
        preds = non_max_suppression_np_pose(pred_trt, session.conf, session.iou)

    else:
        raise TypeError(f'session not belong to [Trt, Onnx]')

    return preds


def pose_v8_detFoot(session, img0s, imgsz=[416, 736], version='v8'):
    """
    脚检测？
    """
    # 这里传入的是 原图 需要进行预处理
    img = preprocess(img0s, imgsz=imgsz)
    res_np = session.run([session.get_outputs()[0].name], {session.get_inputs()[0].name: img})[0]
    _preds = torch.tensor(res_np)
    if version == 'v8':
        preds = non_max_suppression(_preds, conf_thres=0.40, iou_thres=0.45, classes=None, nc=1)  # 后处理[(1,56, 5040)] -> [(13, 57)]
        preds = scale_boxes(imgsz, preds[0], img0s.shape)

    else:               # V10
        conf_thres = 0.5
        preds = [pred[pred[:, 4] > conf_thres] for pred in _preds]          # v10后处理



    return preds


# def scale_boxes(img1_shape, boxes, img0_shape, ratio_pad=None):
#     """
#     Rescales bounding boxes (in the format of xyxy) from the shape of the image they were originally specified in
#     (img1_shape) to the shape of a different image (img0_shape).
#
#     Args:
#       img1_shape (tuple): The shape of the image that the bounding boxes are for, in the format of (height, width).
#       boxes (torch.Tensor): the bounding boxes of the objects in the image, in the format of (x1, y1, x2, y2)
#       img0_shape (tuple): the shape of the target image, in the format of (height, width).
#       ratio_pad (tuple): a tuple of (ratio, pad) for scaling the boxes. If not provided, the ratio and pad will be
#                          calculated based on the size difference between the two images.
#
#     Returns:
#       boxes (torch.Tensor): The scaled bounding boxes, in the format of (x1, y1, x2, y2)
#     """
#
#     def clip_boxes(boxes, shape):
#         """
#         It takes a list of bounding boxes and a shape (height, width) and clips the bounding boxes to the
#         shape
#
#         Args:
#           boxes (torch.Tensor): the bounding boxes to clip
#           shape (tuple): the shape of the image
#         """
#         if isinstance(boxes, torch.Tensor):  # faster individually
#             boxes[..., 0].clamp_(0, shape[1])  # x1
#             boxes[..., 1].clamp_(0, shape[0])  # y1
#             boxes[..., 2].clamp_(0, shape[1])  # x2
#             boxes[..., 3].clamp_(0, shape[0])  # y2
#         else:  # np.array (faster grouped)
#             boxes[..., [0, 2]] = boxes[..., [0, 2]].clip(0, shape[1])  # x1, x2
#             boxes[..., [1, 3]] = boxes[..., [1, 3]].clip(0, shape[0])  # y1, y2
#
#
#     if ratio_pad is None:  # calculate from img0_shape
#         gain = min(img1_shape[0] / img0_shape[0], img1_shape[1] / img0_shape[1])  # gain  = old / new
#         pad = (img1_shape[1] - img0_shape[1] * gain) / 2, (img1_shape[0] - img0_shape[0] * gain) / 2  # wh padding
#     else:
#         gain = ratio_pad[0][0]
#         pad = ratio_pad[1]
#
#     boxes[..., [0, 2]] -= pad[0]  # x padding
#     boxes[..., [1, 3]] -= pad[1]  # y padding
#     boxes[..., :4] /= gain
#     clip_boxes(boxes, img0_shape)
#     return boxes



def detect_board_v8_infer(session, img_ori, img_size, board_pos=None, vid_H=1440):
    # 需要对原始图像做前处理
    input_img, _, _ = letterbox(img_ori, new_shape=img_size)
    img = preprocess_img(input_img)
    # infer
    _preds = torch.tensor(session.run([session.get_outputs()[0].name], {session.get_inputs()[0].name: img})[0])
    # 后处理
    preds = non_max_suppression_V8(_preds, conf_thres=0.5, iou_thres=0.45, classes=None, nc=1)  # 后处理[(1,56, 5040)] -> [(13, 57)]
    if isinstance(preds, list):
        preds = preds[0]
    if hasattr(preds, 'shape') and len(preds) != 0:
        len_preds = preds.shape[0]
    else:
        return False, False, False, False

    if board_pos is None:
        # scale_boxes作用是对bbox缩放到原图,以最大缩放比例缩放
        pred_board = scale_boxes(img.shape[2:], preds[:, :4], img_ori.shape)
        xyxy_board = pred_board[0, :]
        # 判断朝向: 板子中心在右边, 则朝右边
        way = 'R' if (xyxy_board[2] + xyxy_board[0]) > 1920 else 'L'
        # 获取板子的宽度
        board_width = abs(xyxy_board[0] - xyxy_board[2]).__int__()
        board_info = {'way': way, 'xyxy_board': xyxy_board, 'board_width': board_width}

        return xyxy_board, way, board_width, board_info

    elif board_pos in [1,3,5, 2,4,6]:
        if vid_H == 1440:
            split_line = [0, 490, 850, vid_H]
        elif vid_H == 2160:
            split_line = [0, 770, 1050, vid_H]
        else:
            raise ValueError(vid_H)
        y_split = [(0, split_line[1] * 0.75), (split_line[1] * 0.75, split_line[2] * 0.75), (split_line[2] * 0.75, vid_H * 0.75)]  # 1080p画面下y分割线，用于区分板子的位置
        # 画面右侧(自下而上)1\3\5， 左侧2\4\6
        board_info_list = []
        for a_preds in preds:
            # scale_boxes作用是对bbox缩放到原图,以最大缩放比例缩放
            xyxy_board = scale_boxes(img.shape[2:], a_preds[:4], img_ori.shape)
            # 判断朝向: 板子中心在右边(则向左推), 板子中心在左边(则向右推)
            way = 'L' if (xyxy_board[2] + xyxy_board[0]) > 1920 else 'R'        # R 表示向右推
            # 获取板子的宽度
            board_width = abs(xyxy_board[0] - xyxy_board[2]).__int__()
            board_high = abs(xyxy_board[1] - xyxy_board[3]).__int__()
            # 获取位置pos
            position = [5, 3, 1] if way == 'L' else [6, 4, 2]
            new_generator = (p for (top, down), p in zip(y_split, position) if top < xyxy_board[3] <down)       # 改用板子下边做判断
            pos = next(new_generator, None)         # 每次循环中重新创建生成器
            if not pos == board_pos:
                continue

            # 获取位置对应的人体区域, 用于确定属于该板子的人体
            per_dy = xyxy_board[1] + board_high + 0.5 * board_width  # person down y: W/2 + H
            per_tx = xyxy_board[2] + (board_width + 50) if way == 'L' else xyxy_board[0] - (board_width + 50)
            per_box = [xyxy_board[0], xyxy_board[1], per_tx, per_dy] if way == 'L' else [per_tx, xyxy_board[1],
                                                                                         xyxy_board[0], per_dy]

            board_info = {'way': way, 'xyxy_board': xyxy_board, 'board_width': board_width, 'pos': pos, 'per_box': per_box}

            board_info_list.append(board_info)
        return None, None, None, board_info_list
    else:
        return False, False, False, False


# --------------以下是pull up yoloV5 模型服务


def scale_coordsV5(img1_shape, coords, img0_shape, ratio_pad=None):
    # Rescale coords (xyxy) from img1_shape to img0_shape
    if ratio_pad is None:  # calculate from img0_shape
        gain = min(img1_shape[0] / img0_shape[0], img1_shape[1] / img0_shape[1])  # gain  = old / new
        pad = (img1_shape[1] - img0_shape[1] * gain) / 2, (img1_shape[0] - img0_shape[0] * gain) / 2  # wh padding
    else:
        gain = ratio_pad[0][0]
        pad = ratio_pad[1]

    coords[:, [0, 2]] -= pad[0]  # x padding
    coords[:, [1, 3]] -= pad[1]  # y padding
    coords[:, :4] /= gain
    clip_coords(coords, img0_shape)
    return coords


def detect_v5_infer(session, img_ori, img_size):
    # 需要对原始图像做前处理
    input_img, _, _ = letterbox(img_ori, new_shape=img_size)
    #print("input_img.shape",input_img.shape)
    img = preprocess_img(input_img)
    # infer
    _preds = torch.tensor(session.run([session.get_outputs()[0].name], {session.get_inputs()[0].name: img})[0])
    # 后处理
    preds = non_max_suppressionV5(_preds, conf_thres=0.5, iou_thres=0.45, classes=None)  # 后处理[(1,56, 5040)] -> [(13, 57)]
    # print(preds)

    # scale_boxes作用是对bbox缩放到原图,以最大缩放比例缩放
    # pred_board = scale_boxes(img.shape[2:], preds[0][:, :4], img_ori.shape)
    det_list = []
    for i, det in enumerate(preds):  # detections per image

        #gn = torch.tensor(img_ori.shape)[[1, 0, 1, 0]]  # normalization gain whwh
        if det is not None and len(det):
            # Rescale boxes from img_size to im0 size
            det[:, :4] = scale_coordsV5(input_img.shape, det[:, :4], img_ori.shape).round()
        det_list.append(det)
    return det_list


    #return pred_board[0, :]


def detect_v8_infer(session, img_ori, img_size):
    # 需要对原始图像做前处理
    input_img, _, _ = letterbox(img_ori, new_shape=img_size)
    #print("input_img.shape",input_img.shape)
    img = preprocess_img(input_img)
    # infer
    _preds = torch.tensor(session.run([session.get_outputs()[0].name], {session.get_inputs()[0].name: img})[0])
    # 后处理
    preds = non_max_suppression(_preds, conf_thres=0.4, iou_thres=0.45, classes=None)  # 后处理[(1,56, 5040)] -> [(13, 57)]
    # print(preds)

    # scale_boxes作用是对bbox缩放到原图,以最大缩放比例缩放
    # pred_board = scale_boxes(img.shape[2:], preds[0][:, :4], img_ori.shape)
    det_list = []
    for i, det in enumerate(preds):  # detections per image

        #gn = torch.tensor(img_ori.shape)[[1, 0, 1, 0]]  # normalization gain whwh
        if det is not None and len(det):
            # Rescale boxes from img_size to im0 size
            det[:, :4] = scale_coordsV5(input_img.shape, det[:, :4], img_ori.shape).round()
        det_list.append(det)
    return det_list


    #return pred_board[0, :]



def non_max_suppressionV5(prediction, conf_thres=0.25, iou_thres=0.45, classes=None, agnostic=False, multi_label=False,
                        labels=(), max_det=300):
    """Runs Non-Maximum Suppression (NMS) on inference results

    Returns:
         list of detections, on (n,6) tensor per image [xyxy, conf, cls]
    """

    def xywh2xyxy(x):
        """
        Convert bounding box coordinates from (x, y, width, height) format to (x1, y1, x2, y2) format where (x1, y1) is the
        top-left corner and (x2, y2) is the bottom-right corner.

        Args:
            x (np.ndarray | torch.Tensor): The input bounding box coordinates in (x, y, width, height) format.
        Returns:
            y (np.ndarray | torch.Tensor): The bounding box coordinates in (x1, y1, x2, y2) format.
        """
        y = torch.empty_like(x) if isinstance(x, torch.Tensor) else np.empty_like(x)
        dw = x[..., 2] / 2  # half-width
        dh = x[..., 3] / 2  # half-height
        y[..., 0] = x[..., 0] - dw  # top left x
        y[..., 1] = x[..., 1] - dh  # top left y
        y[..., 2] = x[..., 0] + dw  # bottom right x
        y[..., 3] = x[..., 1] + dh  # bottom right y
        return y

    def box_iou(box1, box2, eps=1e-7):
        """
        Calculate intersection-over-union (IoU) of boxes.
        Both sets of boxes are expected to be in (x1, y1, x2, y2) format.
        Based on https://github.com/pytorch/vision/blob/master/torchvision/ops/boxes.py

        Args:
            box1 (torch.Tensor): A tensor of shape (N, 4) representing N bounding boxes.
            box2 (torch.Tensor): A tensor of shape (M, 4) representing M bounding boxes.
            eps (float, optional): A small value to avoid division by zero. Defaults to 1e-7.

        Returns:
            (torch.Tensor): An NxM tensor containing the pairwise IoU values for every element in box1 and box2.
        """

        # inter(N,M) = (rb(N,M,2) - lt(N,M,2)).clamp(0).prod(2)
        (a1, a2), (b1, b2) = box1.unsqueeze(1).chunk(2, 2), box2.unsqueeze(0).chunk(2, 2)
        inter = (torch.min(a2, b2) - torch.max(a1, b1)).clamp_(0).prod(2)

        # IoU = inter / (area1 + area2 - inter)
        return inter / ((a2 - a1).prod(2) + (b2 - b1).prod(2) - inter + eps)

    nc = prediction.shape[2] - 5  # number of classes
    xc = prediction[..., 4] > conf_thres  # candidates

    # Checks
    assert 0 <= conf_thres <= 1, f'Invalid Confidence threshold {conf_thres}, valid values are between 0.0 and 1.0'
    assert 0 <= iou_thres <= 1, f'Invalid IoU {iou_thres}, valid values are between 0.0 and 1.0'

    # Settings
    min_wh, max_wh = 2, 4096  # (pixels) minimum and maximum box width and height
    max_nms = 30000  # maximum number of boxes into torchvision.ops.nms()
    time_limit = 10.0  # seconds to quit after
    redundant = True  # require redundant detections
    multi_label &= nc > 1  # multiple labels per box (adds 0.5ms/img)
    merge = False  # use merge-NMS

    t = time.time()
    output = [torch.zeros((0, 6), device=prediction.device)] * prediction.shape[0]
    for xi, x in enumerate(prediction):  # image index, image inference
        # Apply constraints
        # x[((x[..., 2:4] < min_wh) | (x[..., 2:4] > max_wh)).any(1), 4] = 0  # width-height
        x = x[xc[xi]]  # confidence

        # Cat apriori labels if autolabelling
        if labels and len(labels[xi]):
            l = labels[xi]
            v = torch.zeros((len(l), nc + 5), device=x.device)
            v[:, :4] = l[:, 1:5]  # box
            v[:, 4] = 1.0  # conf
            v[range(len(l)), l[:, 0].long() + 5] = 1.0  # cls
            x = torch.cat((x, v), 0)

        # If none remain process next image
        if not x.shape[0]:
            continue

        # Compute conf
        x[:, 5:] *= x[:, 4:5]  # conf = obj_conf * cls_conf

        # Box (center x, center y, width, height) to (x1, y1, x2, y2)
        box = xywh2xyxy(x[:, :4])

        # Detections matrix nx6 (xyxy, conf, cls)
        if multi_label:
            i, j = (x[:, 5:] > conf_thres).nonzero(as_tuple=False).T
            x = torch.cat((box[i], x[i, j + 5, None], j[:, None].float()), 1)
        else:  # best class only
            conf, j = x[:, 5:].max(1, keepdim=True)
            x = torch.cat((box, conf, j.float()), 1)[conf.view(-1) > conf_thres]

        # Filter by class
        if classes is not None:
            x = x[(x[:, 5:6] == torch.tensor(classes, device=x.device)).any(1)]

        # Apply finite constraint
        # if not torch.isfinite(x).all():
        #     x = x[torch.isfinite(x).all(1)]

        # Check shape
        n = x.shape[0]  # number of boxes
        if not n:  # no boxes
            continue
        elif n > max_nms:  # excess boxes
            x = x[x[:, 4].argsort(descending=True)[:max_nms]]  # sort by confidence

        # Batched NMS
        c = x[:, 5:6] * (0 if agnostic else max_wh)  # classes
        boxes, scores = x[:, :4] + c, x[:, 4]  # boxes (offset by class), scores
        i = torchvision.ops.nms(boxes, scores, iou_thres)  # NMS
        if i.shape[0] > max_det:  # limit detections
            i = i[:max_det]
        if merge and (1 < n < 3E3):  # Merge NMS (boxes merged using weighted mean)
            # update boxes as boxes(i,4) = weights(i,n) * boxes(n,4)
            iou = box_iou(boxes[i], boxes) > iou_thres  # iou matrix
            weights = iou * scores[None]  # box weights
            x[i, :4] = torch.mm(weights, x[:, :4]).float() / weights.sum(1, keepdim=True)  # merged boxes
            if redundant:
                i = i[iou.sum(1) > 1]  # require redundancy

        output[xi] = x[i]
        if (time.time() - t) > time_limit:
            print(f'WARNING: NMS time limit {time_limit}s exceeded')
            break  # time limit exceeded

    return output


def clip_coords(coords, shape):
    """
    Clip line coordinates to the image boundaries.

    Args:
        coords (torch.Tensor | numpy.ndarray): A list of line coordinates.
        shape (tuple): A tuple of integers representing the size of the image in the format (height, width).

    Returns:
        (None): The function modifies the input `coordinates` in place, by clipping each coordinate to the image boundaries.
    """
    if isinstance(coords, torch.Tensor):  # faster individually
        coords[..., 0].clamp_(0, shape[1])  # x
        coords[..., 1].clamp_(0, shape[0])  # y
    else:  # np.array (faster grouped)
        coords[..., 0] = coords[..., 0].clip(0, shape[1])  # x
        coords[..., 1] = coords[..., 1].clip(0, shape[0])  # y

