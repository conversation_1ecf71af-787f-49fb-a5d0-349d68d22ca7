"""
这是一个数据采集项目的大框架，
用于采集动作识别、场景理解的数据，包含项目：
                                        实心球，立定跳远，仰卧起坐，坐位体前屈,50m
@Moss Proj2024-2026
"""

# 公用模块
1. 数据工具，用于对数据进行分组、拆分、合并、筛选等操作
tools/项目
2. 基于姿态模型推理视频, 生成data_info.pkl文件在原始视频路径下
GenVidInfo.py
3. 读取data_info.pkl，切割存储样本和标签
Gen_Samples.py

================================= trigger ==============================================================
实心球   防误触发项目【已完成】：
1. 检查配置
Config/baseline.yml 包含：原始视频路径, pkl样本存储路径，标定区域文件
2. 执行
python trigger_solidball.py

----------------------------------------------------------------------------------------

立定跳远 防误触发项目【暂缓开发中 -> kyrie接手开发202502】：
1. 检查配置
Config/baseline_standjump.yml 包含：原始视频路径, pkl样本存储路径，标定区域文件
2. 执行
python trigger_standjump.py

------------------------------------------------------------------------------------------
================================== fake =============================================================

立定跳远 拉手作弊 防作弊 项目：【20250109已集成】
1. 检查配置
Config/baseline_standjump_fake.yml
需要包含：
原始视频路径，
标定区域文件(Config/mark_standjump_hand_hold文件夹),
模型路径和尺寸(classify_hand_hold,hand_hold_size), hand_hold_size:[320, 384], 320为resize尺寸，384为中心裁剪尺寸

2. 运行
python fake_handHold_jump.py

------------------------------------------------------------------------------------------
立定跳远 防作弊项目：【已完成】
1. 检查配置
Config/baseline_standjump_fake.yml 包含：原始视频路径, pkl样本存储路径，标定区域文件

2. 执行
python fake_standjump.py   # 老方案
python fake_standjump_logic.py   # 新方案
-----------------------------------------------------------------------------------------
20240831
单人仰卧起坐 防作弊项目:【已完成】
1. 检查配置
Config/baseline_sitUp_fake.yml 包含：原始视频路径, pkl样本存储路径
1. 定位：根据人体框逻辑判断，无需标定配置文件

2. 执行
# python fake_situp.py      # 无过滤逻辑版本
python fake_situp_logic.py  # 最新版本： 加入盲区过滤逻辑
2.1 多进程数据采集脚本
python multi_fake_situp.py


3. 多卡多进程-原始视频推理脚本
python detect_videos_sitUp.py       # 夜里跑数据

# 单人仰卧起坐 双模态方案     20250630
1. 数据采集
python fake_situp_fusion.py      # 配置文件 Config/baseline_sitUp_fusion.yml
2. 数据稀疏抽样
python tools/sitUpAndreach_cheat/devide_sitUpVids.py
3. 基于骨骼动作识别模型的数据预筛选
python  /root/share175/sport_trains/action_recongnition/base_skeleton/GCN_yoloPose/detect_cheat_situpAndreach.py --class_sp True
4. 基于图像分类模型的数据预筛
python tools/sitUpAndreach_cheat/kneefoul_Classify.py


-----------------------------------------------------------------------------------------
20240831
单人坐位体前屈 防作弊项目:【已完成】
Config/baseline_sitReach_fake.yml 包含：原始视频路径, pkl样本存储路径
1. 定位：根据姿态点逻辑判断，无需配置文件

2. 执行
python fake_sitreach.py                 # 老方案：基于骨骼
python fake_sitreach_logic.py           # 老方案: 基于骨骼+logic过滤
python fake_sitreach_img.py             # 新方案: 基于裁剪图像

3. 多卡多进程-原始视频推理脚本
python detect_videos_sitreach.py        # 老方案

-------------------------------------------
2025.01.21
多人坐位体前屈 防作弊项目: 【已完成】
Config/baseline_sitReach_fake.yml

2. 执行
python fake_Multisitreach_img.py


-----------------------------------------------------------------------------------
202408
引体向上 防作弊项目：【已完成】
数据裁剪、样本生成脚本【@tony @Moss合并】
说明：


配置文件路径：Config/baseline_pullup.yml
python fake_pullup_frames.py



-----------------------------------50m作弊轨迹识别-Base mmaction2:PoseC3d-------------------------------------
2025.02.15
50m 防作弊：

0. {可选} 视频分组 工具[split_manyVdis]
python tools/run50/split_manyVdis.py            # 将路径中的mp4视频划分到多个文件夹中，命名方式为001, 002...
1. 无效数据 筛选
* 配置视频路径: Config/baseline_run50_fake.yml
python -m tools.run50.useful_vidFilter.py           # 从项目根目录运行，筛选无效数据，输出Grade有效视频,其余无效数据

2. 视频采集 获取样本
输入：
    配置文件：Config/baseline_run50_fake.yml
    * 修改视频路径，标定文件可以放在视频路径;
    * 配置标定，标定放在视频路径下即可 或 给出路径；              # 要求终点Camera-207 标定到可见的起点
    * 确定是否使用tensorRT推理，仅需要 修改参数 trt_infer: True
    python fake_run50.py
输出：
    * 在视频路径中生成 pkl样本 和 jpg标签
    * 在样本的上级目录 生成 视频dir.txt 记录 采集过的视频名

2.1 {可选}样本分组[海量数据使用]
# 采集后1个文件夹中包含多个视频、样本；
    这里按jpg样本名的前缀，将相同视频跑出来的样本生成1个文件夹进行存放
    并将mp4视频也一并放到 样本文件夹中【样本文件夹的命名即视频名前缀，如2025_3_24_15_34_0】
# 采集新增 断点续采功能：采集过的样本会记录在txt文件中；
注意：下次再跑txt中的视频不会重新采集；
python tools/run50/batch_DirSamples.py              # 1个视频的所有样本会放到同一个文件夹中

2.2 负样本 逻辑匹配[依据 位置距离]
# 输入
    * 像素差阈值：thres_dx=40pix, thres_dy=110pix
    * 人数超过阈值列表: pers_thres=[15人, 20人, 25人, 35人, 50人]
    * rate: 过线人体框高度 / 起点终点标定高度 > rate 的样本认为是 PerNum_BigPerson       # 一般是标定异常带来的大人体 或 非终点画面
    * 样本路径: 文件夹名

python  tools/run50/read_Samples.py
# 输出
    * 检出的负样本: 文件夹名_PosFilted (检出的多人场景、大人体场景: PerNum_BigPerson)
    * 检出日志：文件夹名_Position_dxy_log.txt


3{可选} 自动化标注[小作坊]
python check_imglab.py


4. 训练验证集 数据划分
4.1 按pkl名生成标签，划分训练和验证
输入:
    *  数据路径: 作弊数据集路径/train/skl_points
    *  label_name : labels.txt      # 在数据路径下，txt中每行只写入标签名(即类别文件夹名)
    *  验证数据占比：0.2
python create_vidlabel_lst.py
输出[都要有]：
    * 在数据路径skl_points同级目录 生成datasets/all_label.txt,
                                             train_label.txt,
                                             val_label.txt

4.2 基于标签生成训练-验证pkl
(1). 划分训练和验证, 按pkl所在路径 生成标签，
输入:
    *  数据路径: 作弊数据集路径/train/skl_points
    *  label_name : labels.txt      # 在数据路径下，txt中每行只写入标签名(即类别文件夹名)
    *  验证数据占比：0.2
    * (可选)Add-dataMod: False       # 添加数据模式，避免重复生成数据: 说明,
若设置 Add-dataMod = True, 默认训练集目录下已存在 datasets/all_label_ann.pkl，会向已存在的大pkl，添加本次新加入的数据数据，原数据路径在数据集中被修改 也会重新生成！

python create_vidlabel_lst.py
输出：
    * 在数据路径skl_points同级目录 生成datasets/all_label.txt, train_label.txt, val_label.txt

(2) 根据标签文件，生成训练-验证pkl们
输入:
    * (1)中输出的标签路径
python custom_yoloPose_extraction.py
输出:
    * 在数据路径skl_points同级目录 生成datasets/all_label_ann.pkl, trainval_yolopose.pkl
说明, pkl中包含如下信息：
    * dict_keys(['keypoint',      # (1, T, 17, 2)
           'keypoint_score',      # (1, T, 17)
           'frames_skpt',       # 有效轨迹帧id [T]
           'start_pt',          # 起点标定中心 (x, y)
           'total_frames',      # 有效总帧数
           'vid_frames',         # 视频部分的原始帧数(包含跟踪丢失和出画面的帧)
           'img_shape', 'original_shape',
           'frame_dir', 'filename', 'label'])

4.3 训练
cd /root/share175/sport_trains/run_50M/classify_cheating/mmaction2-main0401/mmaction2-main/tools
# 快捷方式, 如已完成4.1, 4.2 生成，则需要注释掉sh脚本中对应模块
bash train_sport.sh         #
# 1行命令配置 GPU显卡监控等待-标签生成-训练
bash GPU_Monitor_TrainSport.sh

5.0 模型反推样本
cd tools/run50/Generate_labels
需要修改的内容包括：
    *  {必选配置}: CUDA_DEVICES, test_batch, CHECKPOINT, data_pth
    *  {默认配置}: Method, score_thre(可选)
    *  {两个项目的路径}: script_pth, run50_pth
bash detect_samples.sh
    # 以上代码会执行：
    (1) create_vidlabel_lst.py 在 data_pth 路径下 生成 datasets/all_label.txt
    (2) custom_yoloPose_extraction.py 生成 datasets/all_label_ann.pkl
    (3) dist_test.sh 加载数据-模型-推理, 生成 datasets/pred_gt_labels.txt 包含预测标签
    (4) Select_samples.py 依据预测标签, 生成 datasets/model_Match文件夹

5.0.1 模型反推样本-GPU资源监控的排队模式
其他 同5.0的配置
bash GPU_Monitor_detect.sh

5.0.2 分析现网XML文件
tools/run50/analyzeCpp_XML.py       # 保存的是逻辑上出成绩 的原始姿态点


6. pth模型导出onnx
cd /root/share175/sport_trains/run_50M/classify_cheating/mmaction2-main0401/mmaction2-main/tools
python tools/deployment/export_onnx_posec3d.py





